@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:bold;
	src : url("../Fonts/wcb.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:normal;
	src : url("../Fonts/wcn.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}

html, body {
	font-family:"Walkman-Chanakya-905";
}
body {
	font-size:120%;
	line-height:150%;
	text-align:justify;
}
td, th {
	border-style:solid;
	border-width:1px;
}
table {
	border-collapse:collapse;
}
li {
	display:block;
}
.red_text
{
color:rgb(237,28,36);
}

p.chap-title {
	color:#00aeef;
	font-style:normal;
	font-variant:normal;
	font-weight:bold;
}

p.question-heading {
	color:#00aeef;
	font-size:1.1em;
	font-weight:bold;	
}

p.abhyas {
	color:#00aeef;
	font-size:1.3em;
	font-variant:normal;
	font-weight:normal;
	margin-bottom:36px;
	text-align:center;
}
/* Hightlisght Boxes */
.NewWordBox{
background-color:#FFE9C3;
padding: 15px;
}
.activityBox{
background-color:#C7EAFB;
padding: 15px;
font-size:0.9em;
line-height:120%;
}
/* Hightlight Boxes Heading : CSS given directly to <b> tag*/
.NewWordBox b, .activityBox b, .box b 
{
	font-weight:bold;
	font-size:1.2em;
}
/* Hightlight Boxes Sub Heading */
.NewWordBox .Subheading, .activityBox .Subheading, .box .Subheading 
{
	font-weight:bold;
	font-size:1em;
}
/* Chapter Name */
h2
{
color:#fff;
font-size:1.5em;
background:#00AEEF;
padding:15px;
}
/* Chapter number */
h4
{
color:rgb(236,0,140);
font-size:1.3em;
padding: 15px;
}
/* Concept Heading */
.ConceptHeading
{
color:#00aeef;
font-size:1.3em;
font-weight:bold;
margin-top:20px;
text-transform:uppercase;
}
/* Sub Heading */
.SubHeading
{
color:#666666;
font-size:1.1em;
font-weight:bold;
}
/* Sub Heading 2*/
.SubHeading2
{
color:#d1640f;
font-size:1em;
font-weight:bold;
}
.clear
{
	clear:both;
}

.lining_box
{
border:2px solid #000;
padding:15px;
border-radius:15px;
}
.lining_box1
{
border:2px solid #000;
padding:5px;
}
.note
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
}
.block_element
{
display:block;
}
.englishMeaning
{
	font-family:Arial, Helvetica, sans-serif;
	font-size:0.8em;
	}
.img_rt
{
float:right;
clear:both;
}

.img_lft
{
float:left;
clear:both;
}

p.para-style-override-1 {
	text-align:left;
}
p.para-style-override-2 {
	color:#000000;
	
}
p.para-style-override-3 {
	margin-left:0px;
	text-align:left;
	
}

p.para-style-override-5 {
	margin-bottom:0px;
	183px;
}
p.para-style-override-6 {
	margin-bottom:30px;
}
p.para-style-override-7 {
	
	
	font-style:normal;
	font-weight:normal;
	margin-left:21px;
	text-align:justify;
}
p.para-style-override-8 {
	margin-left:21px;
	text-align:justify;
}
p.para-style-override-9 {
	margin-left:74px;
	text-align:justify;
	
}
p.para-style-override-10 {
	
	
	font-style:normal;
	font-weight:normal;
	margin-left:74px;
	text-align:justify;
	
}
p.para-style-override-11 {
	margin-left:54px;
	
}
p.para-style-override-12 {
	margin-left:24px;
	
}
p.para-style-override-13 {
	margin-left:25px;
	
}
p.para-style-override-14 {
	color:#00aeef;
	
	
	font-style:normal;
	font-weight:bold;
	margin-bottom:3px;
	margin-left:45px;
	text-align:justify;
	
}
span.char-style-override-1 {
	
	
}
span.char-style-override-2 {
	
	font-style:italic;
	
	
}
span.char-style-override-3 {
	
	font-style:italic;
	font-weight:bold;
}
span.char-style-override-4 {
	
	font-style:normal;
	font-weight:bold;
}
span.char-style-override-5 {
	color:#ffffff;
}
span.char-style-override-6 {
	
	
	font-style:normal;
	font-weight:normal;
}
span.char-style-override-7 {
	
	
	font-style:normal;
	font-weight:normal;
}
.img
{
	margin-left: auto;
    margin-right: auto;
	
	width:50%;
}

ul li
{
	list-style-type:bullet;
}
div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 96%;

position:absolute;

top:10%;

font-weight:bold;

font-size:28px;

color:#fff;

}

div.chapter_pos div

{

background:#266A2E;

padding:10px;

width:40%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
.cover_img_small

{

width:50%;

}
#prelims .para-style-override-19, #prelims .char-style-override-35
{
	font-weight:bold;
}
#prelims .heading, #prelims .char-style-override-29
{
	font-size: 1.667em; 
	color: rgb(236, 0, 140); 
	font-size: 1.67em; 
	font-weight: bold;
}
#prelims .char-style-override-3
{
	font-style:italic;
	font-weight:normal;
}
#prelims .subheading
{
	
	color:rgb(236, 0, 140); 
}
#prelims img
{
	width:100%;
}
#prelims .para-style-override-7, #prelims p.para-style-override-8 {
	
	
	margin-left:0px;
	text-align:left;
}

@media only screen and (max-width: 767px) {
div.chapter_pos
{
top:10%;
font-size:1em;
}
div.chapter_pos div
{
width:70%;
}
.cover_img_small
{
width:90%;
}
}