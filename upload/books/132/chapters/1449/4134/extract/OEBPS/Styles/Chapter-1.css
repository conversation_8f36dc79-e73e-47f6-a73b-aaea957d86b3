html, body {
font-family:Arial, Helvetica, sans-serif;
}

body {
font-size:100%;
line-height:150%;
padding:2%;
text-align:justify;
}

* {
margin:0;
padding:0;
}

.image {
text-align:center;
}

.subjectHead {
text-align:right;
text-transform:uppercase;
font-size:150%;
margin-bottom:3%;
color:rgb(222,118,28);
}
span.char-style-override-7 {
	color:#231f20;
}
p.Heading {
	color:#6d6e71;
}
p.subHeading {
	color:#00aeef;
	font-size:1.1em;
	font-weight:bold;
}
h1{
color:#00aeef;
font-size:1.3em;
font-weight:bold;
}

.box_heading
{
color:#00aeef;
font-size:1.2em;
font-weight:bold;
text-align: center;
}





h3{color:#00aeef;
font-size:1em;
font-weight:bold;
}


p.furtherReading {
	color:#00aeef;
}
span.char-style-override-32 {
	color:#231f20;
}
span.char-style-override-79,  span.char-style-override-44, span.char-style-override-88, span.char-style-override-5, span.char-style-override-27 {
	vertical-align:super;
}
span.char-style-override-29, span.char-style-override-34, span.char-style-override-42, span.char-style-override-47, span.char-style-override-55, span.char-style-override-58, span.char-style-override-76, span.char-style-override-78
span.char-style-override-79,  span.char-style-override-44, span.char-style-override-88, span.char-style-override-5, span.char-style-override-27 {
	vertical-align:sub;
}
.char-style-override-12
{
	font-weight:bold;
}
.chapterText {
font-size:130%;
}

.mainHead {
font-size:120%;
font-weight:bold;
margin:2% 0;
}

.activity {
font-size:120%;
color:rgb(0, 174, 239);
margin:2% 0;
}

.endnote {
font-size:95%;
padding:2%;
}

.questions {
font-size:125%;
margin:2% 0;
color:rgb(222,118,28);
}

.exercises {
color:#00aeef;
font-size:1.1em;
text-align: center;

}
.center {
	text-align: center;
}

.excercise {
text-transform:uppercase;
font-weight:bold;
margin:1% 0%;
}
.lining_box
{
border:2px solid #00aeef;
border-radius:15px;
padding:15px;
}
.box {
background-color:#c7eafb;
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.box1 {
background-color:#d1d3d4;
padding: 15px;
font-size:0.9em;
line-height:120%;
}

.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}

.newWordsBox{
background-color:rgba(252, 187, 118, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}
.img
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:50%;
}
ul
{
	margin-left:45px;
}
.caption, .para-style-override-1, .para-style-override-12, .char-style-override-25, .char-style-override-23
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
	text-align:center;
}
.note,  .footnote-reference
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
}
p
{
	margin-top:10px;
}
h2
{
color:#fff;
font-size:1.5em;
background:#00aeef;
padding:15px;
}

/* Chapter number */
h4
{
color:#00aeef;
font-size:1.3em;
font-weight:bold;
}
.footer
{
	display:none;
}
table td
{
	padding:10px;
}
.conc
{
	color:#006699;
}
div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 96%;

position:absolute;

top:40%;

font-weight:bold;

font-size:28px;

color:#fff;

}

div.chapter_pos div

{

background:#421C52;

padding:10px;

width:40%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
.img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:auto;
}
.bold
{
	font-weight:bold;
}
.cover_img_small
{
width:50%;
}
@media only screen and (max-width: 767px) {

div.chapter_pos

{
top:40%;
font-size:1em;
}
div.chapter_pos div

{
width:70%;
}
.cover_img_small
{
width:90%;
}
}

body {
font-family:"Arial";
font-size:100%;
line-height:150%;
padding:2%;
text-align:justify;
}
#prelims
{
	line-height:200%;
}
#prelims .char-style-override-21
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color:#00aeef;
}
#prelims .char-style-override-6
{
	font-style:italic;
}
#prelims .subheading
{
	
	color:#00aeef;
}

img
{
max-width:100%;
}
p.imgs img, div.imgs img
{
position:relative;
top:20px;
}


