@font-face {
font-family:"Walkman-Chanakya-905";
font-style:normal;
font-weight:bold;
src : url("../Fonts/wcb.ttf");
}
@font-face {
font-family:"Walkman-Chanakya-905";
font-style:italic;
font-weight:bold;
src : url("../Fonts/wcbi.ttf");
}
@font-face {
font-family:"Walkman-Chanakya-905";
font-style:oblique;
font-weight:bold;
src : url("../Fonts/wcbi.ttf");
}
@font-face {
font-family:"Walkman-Chanakya-905";
font-style:normal;
font-weight:normal;
src : url("../Fonts/wcn.ttf");
}
@font-face {
font-family:"Walkman-Chanakya-905";
font-style:italic;
font-weight:normal;
src : url("../Fonts/wcni.ttf");
}
@font-face {
font-family:"Walkman-Chanakya-905";
font-style:oblique;
font-weight:normal;
src : url("../Fonts/wcni.ttf");
}

html, body {
font-family:"Walkman-Chanakya-905";
}

body {
font-size:120%;
line-height:150%;
padding:2%;
text-align:justify;
}

* {
margin:0;
padding:0;
}
p.activity {
font-size:1.2em;
color:rgb(0, 174, 239);
margin-bottom:20px;
font-weight:bold;
}
.questions1 {
font-size:1.0em;
color:rgb(0, 174, 239);
font-style:italic;
font-weight:bold;

}
.asterik
{
font-size:0.83em;
color:rgb(0, 174, 239);
font-style:italic;

}
.questions {
font-size:125%;
margin:2% 0;
color:#FF8000;
font-weight:bold;
}
.example {
font-size:1.0em;
margin:2% 0;
color:#CC0000;
font-weight:bold;
}
.exercises {
color:rgb(46, 49, 146);
font-size:1.2em;
font-weight:bold;
}
.center {
	text-align: center;
}
.red_box
{
color:#fff;
font-size:1.5em;
background:#CC0000;
padding:10px;
}

.excercise {
text-transform:uppercase;
font-weight:bold;
margin:1% 0%;
}

/* Hightlisght Boxes */
.NewWordBox{
background-color:#FFDBB4;
padding: 15px;
font-size:0.9em;
line-height:150%;
}
.activityBox{
background-color:#CDFFFF;
padding: 15px;
font-size:0.9em;
line-height:150%;
}
.box{
background-color:#FFFBD0;
padding: 15px;
font-size:0.9em;
line-height:150%;
}
/* Hightlight Boxes Heading : CSS given directly to <b> tag*/
.NewWordBox b, .activityBox b, .box b 
{
	font-weight:bold;
	font-size:1.2em;
}
/* Hightlight Boxes Sub Heading */
.NewWordBox .Subheading, .activityBox .Subheading, .box .Subheading 
{
	font-weight:bold;
	font-size:1em;
}

ul
{
	margin-left:45px;
}
.caption
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
	text-align:center;
}
/* Chapter Name */
h2
{
color:#fff;
font-size:1.5em;
background:#FF8000;
padding:10px;
}
/* Chapter number */
h4
{
color:#CC0000;
font-size:1.3em;
}
/* Concept Heading */
.ConceptHeading
{
color:#CC0000;
font-size:1.3em;
font-weight:bold;
margin-top:40px;
}
/* Sub Heading */
.SubHeading
{
font-weight:bold;
color:#CC0000;
font-size:1.1em;
margin-top:40px;
}
/* Sub Heading 2*/
.SubHeading2
{
color:#d1640f;
font-size:1em;
font-weight:bold;
}
/* if Mathematics or science book use */
#MathSc img
{
	position:relative;
	top:5px;
}
#MathSc .img1
{
	position:relative;
	top:10px;
}
#MathSc .img2
{
	position:relative;
	top:15px;
}
#MathSc .img3
{
	position:relative;
	top:25px;
}
#MathSc .img4
{
	position:relative;
	top:25px;
}
#MathSc .img5
{
	position:relative;
	top:25px;
}
#MathSc .img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:95%;
}
.clear
{
	clear:both;
}

.lining_box
{
border:2px solid #000;
padding:15px;
border-radius:15px;
}
.image {
text-align:center;
}
.author {
text-align:right;
}

.chapterHeading {
font-size:160%;
color: gray;
margin-bottom:20px;
}

.chapterNumber {
font-size: 125%;
}

.subHeading {
color:#ce1337;
font-size:125%;
}

.center {
text-align: center;
}

.excercise {
text-transform:uppercase;
font-weight:bold;
margin:1% 0%;
}




ul
{
margin-left:45px;
}
.caption
{
font-style: italic;
font-size: 0.83em;
color: #4D4D4D;
text-align:center;
}
p
{
margin-top:10px;
}

.footer
{
display:none;
}
table td
{
padding:10px;
}
.conc
{
color:#006699;
}
.englishMeaning
{
font-family:arial;
font-size:0.8em;
}
.right
{
display:inline;
float:right;
clear:both;
}

.italic
{
font-weight:bold;
font-size:100%;
color:#03C;
}
.note
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
}
.center
{
text-align:center;
}
.right
{
text-align:right;
}
.background
{
background:#999;
font-weight:bold;
}
.superscript{
position:relative;
top:-15%;
font-size: 85%;
font-family:Arial, Helvetica, sans-serif;
}

.subscript{
position:relative;
bottom:-25%;
font-size: 85%;
font-family:Arial, Helvetica, sans-serif;
}
.work
{
font-size:105% ;
}
div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 96%;

position:absolute;

top:40%;

font-weight:bold;

font-size:28px;

color:#fff;

}

div.chapter_pos div

{

background:#421C52;

padding:10px;

width:40%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}.activitybox2{

background-color:#F4A460;

padding: 5px 5px 5px 5px;

margin: 5px 5px 5px 5px;

} 
.cover_img_small
{
width:50%;
}
@media only screen and (max-width: 767px) {

div.chapter_pos

{
top:40%;
font-size:1em;
}
div.chapter_pos div

{
width:80%;
}
.cover_img_small
{
width:90%;
}
}


#prelims .para-style-override-13, .char-style-override-26
{
	font-weight:bold;
}
#prelims .char-style-override-19
{
	font-size: 1.667em; 
	color: #008C44; 
	font-size: 1.67em; 
	font-weight: bold;
}
#prelims .char-style-override-3
{
	font-style:italic;
}
#prelims .subheading
{
	
	color:#b55414;
}