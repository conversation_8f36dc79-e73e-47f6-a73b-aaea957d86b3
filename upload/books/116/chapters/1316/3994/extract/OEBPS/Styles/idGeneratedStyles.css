
html, body {
font-family:Arial, Helvetica, sans-serif;
}

body {
font-size:100%;
line-height:150%;
padding:2%;
text-align:justify;
}

* {
margin:0;
padding:0;
}
.box{
background-color:#666;
padding: 15px;
font-size:0.9em;
line-height:150%;
}
img
{
	max-width:100%;
}
h4,.caption
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
	text-align:center;
}
/* Chapter Name */
h1
{
color:#2B2E34;
font-size:1.5em;

padding:10px;
}
/* Chapter number */
h2
{
color:#2B2E34;
font-size:1.3em;
font-weight:bold;
margin-top:10px;
}
/* Concept Heading */
h3
{
color:#2B2E34;
font-size:1.1em;
font-weight:bold;
margin-top:10px;
}
/* Sub Heading */

/* Sub Heading 2*/
h5
{
color:#CC0000;
font-size:1.1em;
font-weight:bold;
}
.clear
{
	clear:both;
}
.blue
{

padding:15px;
border-radius:0px;
border:1px solid black;
border-left: 20px solid  #81CEE7;
}
.brown
{

padding:15px;
border-radius:0px;
border:1px solid black;
border-left: 20px solid  #D8D8DA;


}
.brown1
{
padding:15px;
border-radius:0px;

background:#D8D8DA;
}
.lining_box
{
border:2px solid #000;
padding:15px;
border-radius:15px;
}
.white
{
border:0px solid #000;
padding:15px;
border-radius:0px;
background:#D6D7D8;
}
.white1{
border:2px solid #000;
padding:15px;
border-radius:0px;

}
.note
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
}
p
{
	margin-top:10px;
}
table
{
	width:100%;
	border:1px solid #000;
	border-collapse:collapse;
}
td
{
	padding:10px;
	border:1px solid #000;
	border-collapse:collapse;
}
.cover_img_small
{
width:50%;
}
div.layout


{


text-align: center;


}


div.chapter_pos



{



text-align: center;



width: 96%;



position:absolute;



top:70%;



font-weight:bold;



font-size:28px;



color:#0D0604;



}



div.chapter_pos div



{



background:#B6F71F;



padding:10px;



width:30%;



margin:auto;


opacity:0.9;



}



div.chapter_pos div span



{



font-size:0.7em;



color:#eaeaea;



font-weight:normal;



}


@media only screen and (max-width: 767px) {



div.chapter_pos



{



font-size:0.8em;


line-height:120%;


top:50%;


}



div.chapter_pos div span



{



font-size:0.7em;



}


}