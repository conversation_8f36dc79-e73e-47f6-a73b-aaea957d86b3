html, body {
font-family:Arial, Helvetica, sans-serif;
}

body {
font-size:100%;
line-height:150%;
padding:2%;
text-align:justify;
}

* {
margin:0;
padding:0;
}
h4
{
color:#000;
font-size:1.3em;
}
h2
{
color:#fff;
font-size:1.5em;
background:#00aeef;
padding:10px;
}
#MathSc img
{
	position:relative;
	top:15px;
	height:40px;
	
}
#MathSc img.small, #MathSc .image p img.small
{
	height:22px;
	top:5px;
	
}
#MathSc img.medium
{
	height:30px;
	top:5px;
	
}
#MathSc p.medium img
{
	height:30px;
	top:5px;
	
}
#MathSc p.small img
{
	height:22px;
	top:5px;
	
}
#MathSc .image  img
{
	position:relative;
	top:15px;
	height:auto;
	max-width:100%;
}
#MathSc p img.img1
{
	position:relative;
	top:14px;
}
#MathSc .img2
{
	position:relative;
	top:20px;
}
#MathSc .img3
{
	position:relative;
	top:0px;
}

/* Concept Heading */
.ConceptHeading, .chapterheading
{
color:black;
font-size:1.3em;
font-weight:bold;
margin-top:20px;
}
.glossary-text
{
color:#00aeef;
}

/* Sub Heading */
.SubHeading, .topicheading, .char-style-override-25
{
color:#00aeef;
font-size:1.1em;
font-weight:bold;
}
/* Sub Heading 2*/
.SubHeading2
{
color:#d1640f;
font-size:1em;
font-weight:bold;
}
.char-style-override-19{
font-size:0.7em;

vertcal-align:sub
}
.underline_txt

{

font-decoration:underline;

}

.bold_txt

{

font-weight:bold;

}

.center_element

{

margin:auto;

}

.italics_txt

{

font-style:italic;

}

.block_element

{

display:block;

}

.img_rt

{

float:right;

clear:both;

}

.img_lft

{

float:left;

}

.box{
background-color:#C7EAFB;
padding: 15px ;
font-size:0.9em;
}
.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 15px;
font-size:0.9em;
}

.newWordsBox{
background-color:rgba(252, 187, 118, 0.4);
padding:15px;
font-size:0.9em;
}
.image {
text-align:center;
}

.subjectHead {
text-align:right;
text-transform:uppercase;
font-size:150%;
margin-bottom:3%;
color:rgb(222,118,28);
}
.char-style-override-5 {
	color:#00aeef;
}
span.char-style-override-6 {
	color:#00aeef;
	text-transform:uppercase;
}
span.char-style-override-1 {
	color:#00aeef;
	
}
span.char-style-override-15 {
	color:#00aeef;
}
span.char-style-override-25, span.char-style-override-26, span.char-style-override-27, span.char-style-override-28 {
	color:#00aeef;
}
span.char-style-override-61 {
	color:#00aeef;
	text-transform:uppercase;
}
.chapterText {
font-size:130%;
}

.mainHead {
font-size:120%;
font-weight:bold;
margin:2% 0;
}

.activity {
font-size:120%;
color:rgb(0, 174, 239);
margin:2% 0;
}
.lining_box
{
border:1px solid #000;
padding:15px;
}
.lining_boxb
{
border:2px solid #00aeef;
padding:15px;
}
.lining_boxbr{
border:2px solid #00aeef;
padding:15px;
border-radius:15px;
}
.endnote {
font-size:95%;
padding:2%;
}

.questions {
font-size:125%;
margin:2% 0;
color:rgb(222,118,28);
}

.exercises {
color:rgb(46, 49, 146);
font-size:115%;
margin:2% 0;
}
.center {
	text-align: center;
}

.excercise {
text-transform:uppercase;
font-weight:bold;
margin:1% 0%;
}


.box .char-style-override-26, .box .char-style-override-27, .box .char-style-override-25, .box .char-style-override-28
{
	color:#000;
}
.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}

.newWordsBox{
background-color:rgba(252, 187, 118, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}
.img
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:50%;
}
ul
{
	margin-left:45px;
}
.caption
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
	text-align:center;
}
.note
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
}
p
{
	margin-top:10px;
}

.footer
{
	display:none;
}
table td
{
	padding:10px;
}
.conc
{
	color:#006699;
}
div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 96%;

position:absolute;

top:40%;

font-weight:bold;

font-size:28px;

color:#fff;

}

div.chapter_pos div

{

background:#FF0000;

padding:10px;

width:40%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
.img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:80%;
}
.bold
{
	font-weight:bold;
}
.cover_img_small
{
width:50%;
}
@media only screen and (max-width: 767px) {

div.chapter_pos

{
top:20%;
font-size:1em;
}
div.chapter_pos div

{
width:70%;
}
.cover_img_small
{
width:90%;
}
}



#prelims
{
	line-height:200%;
}
#prelims .char-style-override-21
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color:#00aeef;
}
#prelims .char-style-override-2
{
	font-style:italic;
}
#prelims .subheading
{
	
	color:#00aeef;
}



