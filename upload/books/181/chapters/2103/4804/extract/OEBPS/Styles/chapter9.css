@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:bold;
	src : url("../Fonts/wcb.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:normal;
	src : url("../Fonts/wcn.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}

html, body {
	font-family:"Walkman-Chanakya-905";
}

body {
font-size:120%;
line-height:150%;
padding:2%;
text-align:justify;
}

* {
margin:0;
padding:0;
}
span.char-style-override-1 {
	font-size:1.294em;
	font-style:normal;
	font-weight:normal;
}
span.char-style-override-2 {
	color:#0089cf;
	font-size:16px;
	font-style:normal;
	font-weight:bold;
}
p.para-style-override-3 {
	font-family:"Walkman-Chanakya-905", serif;
	font-size:1.917em;
	font-style:normal;
	font-weight:bold;
}
p.Chapter-Name {
	font-size:1.833em;
	line-height:1.2;
	text-align:left;
}
span.char-style-override-6 {
	color:#0089cf;
	font-size:1.5em;
	font-style:normal;
	font-weight:bold;
}
span.char-style-override-10 {
	color:#a3238e;
	font-size:1.143em;
	font-style:normal;
	font-weight:normal;
}
span.char-style-override-18 {
	color:#0089cf;
	font-size:18px;
	font-style:normal;
	font-weight:bold;
}
p.para-style-override-15 {
	font-size:1.125em;
	font-style:normal;
	font-weight:bold;
}
span.char-style-override-6 {
	color:#0089cf;
	font-size:1.5em;
	font-style:normal;
	font-weight:bold;
}
span.char-style-override-38 {
	color:#ec008c;
	font-size:1.5em;
	font-style:normal;
	font-weight:bold;
}
span.char-style-override-40 {
	color:#ec008c;
	font-size:1.167em;
	font-style:normal;
	font-weight:normal;
}
span.char-style-override-42 {
	font-size:1.125em;
	font-style:normal;
	font-weight:bold;
}
span.char-style-override-22 {
	font-size:1.167em;
	font-style:normal;
	font-weight:normal;
}
span.char-style-override-44 {
	color:#6d6e71;
	font-family:arial;
	font-size:1.333em;
	font-style:normal;
	font-weight:bold;
}
.image {
text-align:center;
}
.author {
text-align:right;
}

/* Concept Heading */
.ConceptHeading
{
color:#00aeef;
font-size:1.3em;
font-weight:bold;
margin-top:20px;
}
/* Sub Heading */
.SubHeading
{
color:#00aeef;
font-size:1.1em;
font-weight:bold;
}
/* Sub Heading 2*/
.SubHeading2
{
color:#00aeef;
font-size:1em;
font-weight:bold;
}
.center {
	text-align: center;
}

.excercise {

font-weight:bold;
margin:1% 0%;
}

.lining_box2
{
border:2px solid #800080;
padding:15px;
border-radius:15px;
}
.lining_box3
{
border:1px solid #00aeef;
padding:15px;
}
.box2{
background-color:#FFF9C2;
padding: 15px;
font-size:0.9em;
line-height:120%;
}

.box{
background-color:rgba(3, 78, 162, 0.4);
padding: 15px;
font-size:0.9em;
line-height:120%;
}
/* Hightlight Boxes */
.NewWordBox{
background-color:#FCD5BB;
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.activityBox{
background-color:#ADDFE3;
padding: 15px;
font-size:0.9em;
line-height:120%;
}
img
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:50%;
}
.img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:auto;
}
ul
{
	margin-left:45px;
}
.caption
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
	text-align:center;
}
p
{
	margin-top:10px;
}
/* Chapter Name */
h2
{
color:#fff;
font-size:1.5em;
background:#00aeef;
padding:10px;
}
/* Chapter number */
h4
{
color:#000;
font-size:1.3em;
}
.clear
{
	clear:both;
}
.footer
{
	display:none;
}
table td
{
	padding:10px;
}
.conc
{
	color:#006699;
}
.englishMeaning, .english
{
	font-family:arial;
}
.right
{
	display:inline;
	float:right;
	clear:both;
}
.bold
{
	font-size:115%;
	font-family: Walkman-Chanakya-905;
		font-weight:bold;
}
.italic
{
	font-weight:bold;
	font-size:100%;
	color:#03C;
}
.center
{
	text-align:center;
}
.right
{
	text-align:right;
}
.background
{
	background:#999;
	font-weight:bold;
	
}
.superscript{
position:relative;
top:-15%;
font-size: 85%;
font-family:Arial, Helvetica, sans-serif;
}

.subscript{
position:relative;
bottom:-25%;
font-size: 85%;
font-family:Arial, Helvetica, sans-serif;
}
.work
{
	font-size:105% ;
}
div.layout
{
  text-align: center;
}
div.chapter_pos
{
  text-align: center;
  width: 96%;
  position:absolute;
	top:70%;
	font-weight:bold;
	font-size:28px;
	color:#fff;
}
.char-style-override-22
{
font-style: italic;
}
div.chapter_pos div
{
	background:#266A2E;
	padding:10px;
	width:30%;
	margin:auto;
opacity:0.9;
}
div.chapter_pos div span
{
	font-size:0.7em;
	color:#aeaeae;
	font-weight:normal;
}
.cover_img_small
{
width:50%;
}

@media only screen and (max-width: 767px) {
div.chapter_pos
{
top:70%;
font-size:1em;
}

div.chapter_pos div
{
width:70%;
}
.cover_img_small
{
width:90%;
}
}