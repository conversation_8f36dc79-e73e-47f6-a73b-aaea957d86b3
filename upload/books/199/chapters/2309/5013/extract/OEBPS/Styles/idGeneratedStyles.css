@font-face {

font-family:"Walkman-Chanakya-905";

font-style:normal;

font-weight:bold;

src : url("../Fonts/wcb.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:italic;

font-weight:bold;

src : url("../Fonts/wcbi.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:oblique;

font-weight:bold;

src : url("../Fonts/wcbi.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:normal;

font-weight:normal;

src : url("../Fonts/wcn.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:italic;

font-weight:normal;

src : url("../Fonts/wcni.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:oblique;

font-weight:normal;

src : url("../Fonts/wcni.ttf");

}


html, body {

font-family:"Walkman-Chanakya-905";

}


body {

font-size:120%;

line-height:150%;

text-align:justify;

}



.chapterHeading {

text-align:center;

font-weight:bold;

font-size:200%;

color:#360;

}

img

{

max-width:100%;

}


.bold, .char-style-override-2, .Activity-Rule, .char-style-override-32 {

font-weight:bold;

}


.chapterImage {

height:100px;

width:100px;

float:right;

margin-left:1%;

}


.chapter {

text-align:left;

font-weight:bold;

font-size:130%;

color:#C85C44;

}

.chapterNumber {

text-align:left;

font-weight:bold;

font-size:150%;

color:gray;

}

.underline_txt

{

font-decoration:underline;

}


.bold_txt, .Table-Heading

{

font-weight:bold;

}


.center_element

{

margin:auto;

}

.italics_txt

{

font-style:italic;

}

.block_element

{

display:block;

}

.img_rt

{

float:right;

clear:both;

}

.img_lft

{

float:left;

}


.chapterImage:after {

clear:both;

}


.image {

text-align: center;

}


.center {

text-align: center;

font-family: "Walkman-Chanakya-905";

}


.left {

float:left;

}


.right {

float:right;

}


.numbers {

text-align:center;

font-size:120%;

font-weight:bold;

}


.englishMeaning,.char-style-override-6, .char-style-override-11, .char-style-override-5, .char-style-override-12

{

font-family:Arial, Helvetica, sans-serif;

font-size:75%;

}



/* Cover page band */

div.layout

{

text-align: center;

}

div.chapter_pos

{

text-align: center;

width: 96%;

position:absolute;

top:20%;

line-height:110%;

font-weight:bold;

font-size:28px;

color:#fff;

}

div.chapter_pos div

{

background:#7884B9;

padding:10px;

width:40%;

margin:auto;

opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}

#cover img

{

margin:auto;

width:65%;

}

#others img

{

margin-left: auto;

margin-right: auto;

display: block;

width:50%;

}

#others .img1

{


width:20%;

}

#others .img_wid

{

margin-left: auto;

margin-right: auto;

display: block;

width:90%;

}

/* if Mathematics or science book use */

#MathSc img

{

position:relative;

top:15px;

}

#MathSc .img1

{

position:relative;

top:5px;

}

#MathSc .img2

{

position:relative;

top:20px;

}

#MathSc .img3

{

position:relative;

top:0px;

}

#MathSc .img_wid

{

margin-left: auto;

margin-right: auto;

display: block;

width:50%;

}

.clear

{

clear:both;

}

.lining_box

{

border:2px solid #B1DDCE;

padding:15px;

border-radius:15px;


}


ul

{

margin-left:45px;

}


.caption, .Figure-No-, .Caption, .Caption-2

{

font-style: italic;

font-size: 0.83em;

color: #4D4D4D;

text-align:center;

}

p

{

margin-top:10px;

}

/* Chapter Name */

h2

{

color:#fff;

font-size:1.5em;

background:#91278F;

padding:10px;

}

/* Chapter number */

h4

{

color:#444;
font-size:1.3em;
padding:10px;

}

h3, .BoxHead

{

background:#0099CC;

padding:10px;

color:#fff;

margin:0px;

}

.blue_box

{

background:#0099CC;

padding:10px;

color:#fff;

margin:0px;

font-size:0.9em;

}

h3.numbers

{

background:#E47297;

padding:10px;

margin:0px;

}

.Activity-Rule

{

background:#FFC3CE;

padding:10px;

border-bottom:1px solid #E47297;

margin:0px;

}

/* Concept Heading */

.ConceptHeading, .subjectHead, .Main-Head, .TITLE-1

{

color:#91278F;

font-size:1.3em;

font-weight:bold;

margin-top:20px;

}

.char-style-override-9, .char-style-override-10

{


color:#e924aa;

}


/* Sub Heading */

.SubHeading, .sub-head, .chapterSubheading, .TITLE-2

{

font-size:1.1em;

font-weight:bold;

color:#000;

}

/* Sub Heading 2*/

.SubHeading2

{

color:#d1640f;

font-size:1em;

font-weight:bold;

}

.footer

{

display:none;

}

table

{

width:100%;

border:1px solid #000;

border-collapse:collapse;

}

td

{

padding:10px;

border:1px solid #000;

border-collapse:collapse;

}

.no_border table , .no_border table td

{

border:0px solid #000;

}

/* Hightlisght Boxes */

.NewWordBox{

background-color:#F7E7BD;

padding: 15px;

margin: 15px;

font-size:0.9em;

line-height:120%;

}

.words

{

background-color:#C5D0C1;

padding: 15px;

font-size:0.9em;

line-height:120%;

width:100%;

font-weight:bold;

}

.bluetext

{

color:#00aeef;

}


.activityBox, .subheading{

background-color:#FFC3CE;

padding: 15px;

font-size:0.9em;

line-height:120%;

}

.box1, .BoxText, .greenbox{

background-color:#E3EEE7;

padding: 15px;

line-height:120%;

font-size:0.9em;

}

.box, .subheadingBlue, .bubble, .purplebox {

background-color:#E7CFE5;

padding: 15px;

font-size:0.9em;

line-height:120%;

}

.peachbox

{

background-color:#FFE0BF;

padding: 15px;

font-size:0.9em;

line-height:120%;

}
.exgbox

{

background-color:#B1DDCE;

padding: 15px;

font-size:0.9em;

line-height:120%;

}

.head, .MEANING
{

font-size:1.2em;

font-weight:bold;

}


/* Hightlight Boxes Heading : CSS given directly to <b> tag*/

.NewWordBox b, .activityBox b, .box b

{

font-weight:bold;

font-size:1.2em;

}

/* Hightlight Boxes Sub Heading */

.NewWordBox .Subheading, .activityBox .Subheading, .box .Subheading

{

font-weight:bold;

font-size:1em;

}
.cover_img_small

{

width:50%;

}
#prelims .char-style-override-25, #prelims .para-style-override-25
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color: rgb(236, 0, 140); 
	font-size: 1.67em; 
	font-weight: bold;
}
#prelims .char-style-override-10
{
	font-style:italic;
}
#prelims .subheading
{
	
	color:rgb(236, 0, 140); 
}
#prelims img
{
	width:100%;
}
@media only screen and (max-width: 767px) {

div.chapter_pos

{

top:20%;

font-size:1em;

}

div.chapter_pos div span

{

font-size:0.5em;

}

div.chapter_pos div

{

width:70%;

}

.cover_img_small

{

width:90%;

}

}
