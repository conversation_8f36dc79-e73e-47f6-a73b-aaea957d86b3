@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:bold;
	src : url("../Fonts/wcb.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:normal;
	src : url("../Fonts/wcn.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}

html, body {
	font-family:"Walkman-Chanakya-905";
}
body {
	font-size:100%;
	line-height:150%;
	padding:15px;
	text-align:justify;
}

.chapterHeading {
	font-size:140%;
	font-weight:bold;
	color:#03C;
	
}

.chapterNumber {
	font-size:36px;
	font-weight:bold;
}


.meaning {
	font-size:95%;
}

.image {
text-align:center;
}


.englishMeaningblue{
	font-size:90%;
	font-weight:bold;
	font-family:Arial, Helvetica, sans-serif;
	color: #03C;
}
.blue{
	color: #00BAB7;
}

.englishMeaning{
	font-family:Arial, Helvetica, sans-serif;
	color: #000;
	font-size:0.8em;
}
.bold
{
	font-size:100%;
color:#00BFFF;
}
.italic
{
	font-weight:bold;
	font-size:100%;
	color:#03C;
}.chapterNumber {
	font-size: 120%;
	color: #F00;
	text-align:right;
}


.chapterNumber p {
	color: #000;
	text-align: left;
	font-size: 120%;
	font-family: Walkman-Chanakya-905;
}

.superscript{
position:relative;
top:-10%;
font-size: 80%;
font-family:Arial, Helvetica, sans-serif;
}

.subscript{
position:relative;
vertical-align:baseline;
bottom:-40%;
font-size: 80%;
font-family:Arial, Helvetica, sans-serif;
}
.author2 {
	text-align:right;
}

.line{
border-bottom:5px solid blue;
}
div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 95%;
position:absolute;

top:60%;

font-weight:bold;

font-size:28px;

color:#fff;

}

div.chapter_pos div

{

background:#9A3334;

padding:15px;

width:40%;
line-height:120%;
margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
.caption

{

font-style: italic;

font-size: 0.83em;

color: #4D4D4D;

text-align:center;

}

p

{

margin-top:10px;

}



.footer

{

display:none;

}

table td

{

padding:15px;

}

.conc

{

color:#006699;

}
.img_wid

{

margin-left: auto;

margin-right: auto;

display: block;

width:80%;

}

#prelims .para-style-override-18,  .char-style-override-26, .char-style-override-17 
{
	font-weight:bold;
}
#prelims .heading
{
	
	color: rgb(236, 0, 140); 
	font-size: 1.67em; 
	
}
#prelims .char-style-override-19, .char-style-override-3
{
	font-style:italic;
}
#prelims .subheading
{
	
	color:rgb(236, 0, 140); 
}
	.underline_txt
{
font-decoration:underline;
}
	

table
{
    width:100%;
    border:1px solid #000;
    border-collapse:collapse;
}
td
{
    padding:10px;
    border:1px solid #000;
    border-collapse:collapse;
}

#MathSc img
{
position:relative;
top:8px;
height:25px;
width:100%;

}


#MathSc img.medium, #MathSc .med img
{
height:45px;
top:18px;
margin-bottom:2px;

}
#MathSc .med3 img
{
height:50px;
top:18px;
margin-bottom:2px;

}
#MathSc .med2 img
{
height:30px;
top:10px;
margin-bottom:2px;

}
#MathSc p img.small, #MathSc p.small img
{
position:relative;
top:10px;
height:30px;
max-width:100%;

}
#MathSc p img.small2
{
position:relative;
top:10px;
height:25px;
max-width:100%;

}
#MathSc p img.rupee
{
position:relative;
top:3px;
height:12px;

}
#MathSc .high
{
height:auto; 
width:auto;
}
#MathSc .image img, #MathSc .caption img, #MathSc img.image, #MathSc p.normal img
{
height:auto;
position:relative;
top:20px;
}

h2
{
color:#fff;
font-size:1.5em;
background:#D0AFD3;
padding:10px;
font-weight:bold;
}
/* Chapter number */
h4
{
color:#A9A9A9;
font-size:1.3em;
}
/* Concept Heading */
.ConceptHeading
{
color:#FF0000;
font-size:1.3em;
font-weight:bold;
margin-top:20px;
}
/* Sub Heading */
.SubHeading
{
color:#00BFFF;
font-size:1.3em;

}
/* Sub Heading 2*/
.SubHeading2
{
color:#F48480;
font-size:1.1em;

}
/* Hightlisght Boxes */
.NewWordBox{
background-color:#F7E7BD;
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.red
{
color:#FF0000;
font-size:1.1em;

}
.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.box{
background-color:rgba(3, 78, 162, 0.4);
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.lining_box
{
border:3px solid #F48480;
padding:15px;
border-radius:15px;
}
.lining_box2
{
border:3px solid #F48480; border-radius:15px;
padding:15px;
}
.cover_img_small
{
width:50%;
}

@media only screen and (max-width: 767px) {

div.chapter_pos

{
top:10%;
font-size:1em;
}
div.chapter_pos div

{
width:70%;
}
.cover_img_small
{
width:90%;
}
}

img
{
max-width:100%;
}
p.imgs img, div.imgs img
{
position:relative;
top:20px;
}