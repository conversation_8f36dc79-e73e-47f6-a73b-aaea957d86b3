
<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_1{left:802px;top:1098px;}
#t2_1{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_1{left:339px;top:235px;letter-spacing:-0.1px;}
#t4_1{left:59px;top:414px;letter-spacing:-0.2px;}

.s1_1{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_b;
	color: rgb(255,255,255);
}

.s2_1{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_f;
	color: rgb(35,31,32);
}

.s3_1{
	FONT-SIZE: 146.7px;
	FONT-FAMILY: Times-New-Roman-Bold0200_j;
	color: rgb(255,255,255);
}

.s4_1{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-625_n;
	color: rgb(255,255,255);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts1" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0200_j;
	src: url("fonts/Times-New-Roman-Bold0200_j.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_f;
	src: url("fonts/Times-New-Roman-Bold0100_f.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_b;
	src: url("fonts/Arial-Bold091-625_b.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0116-625_n;
	src: url("fonts/Times-New-Roman-Bold0116-625_n.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg1Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg1" style="-webkit-user-select: none;"><object width="880" height="1155" data="1/1.svg" type="image/svg+xml" id="pdf1" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_1" class="t s1_1">3</div>
<div id="t2_1" class="t s2_1">VII</div>
<div id="t3_1" class="t s3_1">32</div>
<div id="t4_1" class="t s4_1">32.1</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_2{left:64px;top:1098px;}
#t2_2{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_2{left:279px;top:100px;letter-spacing:-0.3px;}
#t4_2{left:264px;top:182px;}
#t5_2{left:264px;top:220px;}
#t6_2{left:264px;top:289px;}
#t7_2{left:264px;top:327px;}
#t8_2{left:264px;top:366px;}
#t9_2{left:264px;top:404px;}
#ta_2{left:225px;top:487px;letter-spacing:3.2px;}
#tb_2{left:331px;top:525px;letter-spacing:-0.4px;}
#tc_2{left:77px;top:630px;}
#td_2{left:76px;top:674px;}
#te_2{left:83px;top:850px;}
#tf_2{left:113px;top:872px;}
#tg_2{left:81px;top:960px;}

.s1_2{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_9u;
	color: rgb(255,255,255);
}

.s2_2{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_9y;
	color: rgb(35,31,32);
}

.s3_2{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-62_a0;
	color: rgb(255,255,255);
}

.s4_2{
	FONT-SIZE: 79.4px;
	FONT-FAMILY: Symbol_ds;
	color: rgb(35,31,32);
}

.s5_2{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-62_a0;
	color: rgb(35,31,32);
}

.s6_2{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_oi;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts2" type="text/css" >

@font-face {
	font-family: Arial-Bold091-625_9u;
	src: url("fonts/Arial-Bold091-625_9u.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_9y;
	src: url("fonts/Times-New-Roman-Bold0100_9y.woff") format("woff");
}

@font-face {
	font-family: Symbol_ds;
	src: url("fonts/Symbol_ds.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0116-62_a0;
	src: url("fonts/Times-New-Roman-Bold0116-62_a0.woff") format("woff");
}

@font-face {
	font-family: Arial083-313_oi;
	src: url("fonts/Arial083-313_oi.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg2Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg2" style="-webkit-user-select: none;"><object width="880" height="1155" data="2/2.svg" type="image/svg+xml" id="pdf2" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_2" class="t s1_2">4</div>
<div id="t2_2" class="t s2_2">VII</div>
<div id="t3_2" class="t s3_2">32.2</div>
<div id="t4_2" class="t s4_2">•</div>
<div id="t5_2" class="t s4_2">•</div>
<div id="t6_2" class="t s4_2">•</div>
<div id="t7_2" class="t s4_2">•</div>
<div id="t8_2" class="t s4_2">•</div>
<div id="t9_2" class="t s4_2">•</div>
<div id="ta_2" class="t s3_2">32.3</div>
<div id="tb_2" class="t s5_2">32.3.1</div>
<div id="tc_2" class="t s6_2">=</div>
<div id="td_2" class="t s6_2">=</div>
<div id="te_2" class="t s6_2">=</div>
<div id="tf_2" class="t s6_2">=</div>
<div id="tg_2" class="t s6_2">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_3{left:802px;top:1098px;}
#t2_3{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_3{left:165px;top:266px;letter-spacing:-0.4px;}
#t4_3{left:165px;top:916px;letter-spacing:-0.4px;}
#t5_3{left:817px;top:195px;}
#t6_3{left:723px;top:290px;}
#t7_3{left:722px;top:422px;}
#t8_3{left:724px;top:521px;}
#t9_3{left:747px;top:609px;}
#ta_3{left:728px;top:631px;}

.s1_3{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_o-;
	color: rgb(255,255,255);
}

.s2_3{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_p2;
	color: rgb(35,31,32);
}

.s3_3{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-62_p6;
	color: rgb(35,31,32);
}

.s4_3{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_11j;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts3" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0116-62_p6;
	src: url("fonts/Times-New-Roman-Bold0116-62_p6.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_o-;
	src: url("fonts/Arial-Bold091-625_o-.woff") format("woff");
}

@font-face {
	font-family: Arial083-313_11j;
	src: url("fonts/Arial083-313_11j.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_p2;
	src: url("fonts/Times-New-Roman-Bold0100_p2.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg3Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg3" style="-webkit-user-select: none;"><object width="880" height="1155" data="3/3.svg" type="image/svg+xml" id="pdf3" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_3" class="t s1_3">5</div>
<div id="t2_3" class="t s2_3">VII</div>
<div id="t3_3" class="t s3_3">32.3.2</div>
<div id="t4_3" class="t s3_3">32.3.3</div>
<div id="t5_3" class="t s4_3">=</div>
<div id="t6_3" class="t s4_3">=</div>
<div id="t7_3" class="t s4_3">=</div>
<div id="t8_3" class="t s4_3">=</div>
<div id="t9_3" class="t s4_3">=</div>
<div id="ta_3" class="t s4_3">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_4{left:64px;top:1098px;}
#t2_4{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_4{left:80px;top:195px;}
#t4_4{left:70px;top:217px;}
#t5_4{left:134px;top:239px;}
#t6_4{left:76px;top:437px;}
#t7_4{left:79px;top:635px;}

.s1_4{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_11-;
	color: rgb(255,255,255);
}

.s2_4{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_122;
	color: rgb(35,31,32);
}

.s3_4{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_1f1;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts4" type="text/css" >

@font-face {
	font-family: Arial083-313_1f1;
	src: url("fonts/Arial083-313_1f1.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_11-;
	src: url("fonts/Arial-Bold091-625_11-.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_122;
	src: url("fonts/Times-New-Roman-Bold0100_122.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg4Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg4" style="-webkit-user-select: none;"><object width="880" height="1155" data="4/4.svg" type="image/svg+xml" id="pdf4" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_4" class="t s1_4">6</div>
<div id="t2_4" class="t s2_4">VII</div>
<div id="t3_4" class="t s3_4">=</div>
<div id="t4_4" class="t s3_4">=</div>
<div id="t5_4" class="t s3_4">=</div>
<div id="t6_4" class="t s3_4">=</div>
<div id="t7_4" class="t s3_4">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_5{left:802px;top:1098px;}
#t2_5{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_5{left:165px;top:286px;letter-spacing:-0.4px;}
#t4_5{left:749px;top:334px;}
#t5_5{left:745px;top:378px;}
#t6_5{left:729px;top:400px;}
#t7_5{left:746px;top:422px;}
#t8_5{left:747px;top:518px;}
#t9_5{left:737px;top:637px;}
#ta_5{left:722px;top:659px;}
#tb_5{left:754px;top:681px;}
#tc_5{left:745px;top:703px;}

.s1_5{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_1fi;
	color: rgb(255,255,255);
}

.s2_5{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_1fm;
	color: rgb(35,31,32);
}

.s3_5{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-6_1fq;
	color: rgb(35,31,32);
}

.s4_5{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_1u1;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts5" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0100_1fm;
	src: url("fonts/Times-New-Roman-Bold0100_1fm.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_1fi;
	src: url("fonts/Arial-Bold091-625_1fi.woff") format("woff");
}

@font-face {
	font-family: Arial083-313_1u1;
	src: url("fonts/Arial083-313_1u1.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0116-6_1fq;
	src: url("fonts/Times-New-Roman-Bold0116-6_1fq.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg5Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg5" style="-webkit-user-select: none;"><object width="880" height="1155" data="5/5.svg" type="image/svg+xml" id="pdf5" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_5" class="t s1_5">7</div>
<div id="t2_5" class="t s2_5">VII</div>
<div id="t3_5" class="t s3_5">32.3.4</div>
<div id="t4_5" class="t s4_5">=</div>
<div id="t5_5" class="t s4_5">=</div>
<div id="t6_5" class="t s4_5">=</div>
<div id="t7_5" class="t s4_5">=</div>
<div id="t8_5" class="t s4_5">=</div>
<div id="t9_5" class="t s4_5">=</div>
<div id="ta_5" class="t s4_5">=</div>
<div id="tb_5" class="t s4_5">=</div>
<div id="tc_5" class="t s4_5">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_6{left:64px;top:1098px;}
#t2_6{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_6{left:331px;top:450px;letter-spacing:-0.4px;}
#t4_6{left:77px;top:288px;}
#t5_6{left:94px;top:310px;}
#t6_6{left:88px;top:376px;}
#t7_6{left:90px;top:640px;}
#t8_6{left:88px;top:662px;}
#t9_6{left:78px;top:842px;}
#ta_6{left:86px;top:864px;}
#tb_6{left:106px;top:908px;}
#tc_6{left:94px;top:930px;}

.s1_6{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_1ui;
	color: rgb(255,255,255);
}

.s2_6{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_1um;
	color: rgb(35,31,32);
}

.s3_6{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-6_1uq;
	color: rgb(35,31,32);
}

.s4_6{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_271;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts6" type="text/css" >

@font-face {
	font-family: Arial083-313_271;
	src: url("fonts/Arial083-313_271.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_1ui;
	src: url("fonts/Arial-Bold091-625_1ui.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_1um;
	src: url("fonts/Times-New-Roman-Bold0100_1um.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0116-6_1uq;
	src: url("fonts/Times-New-Roman-Bold0116-6_1uq.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg6Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg6" style="-webkit-user-select: none;"><object width="880" height="1155" data="6/6.svg" type="image/svg+xml" id="pdf6" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_6" class="t s1_6">8</div>
<div id="t2_6" class="t s2_6">VII</div>
<div id="t3_6" class="t s3_6">32.3.5</div>
<div id="t4_6" class="t s4_6">=</div>
<div id="t5_6" class="t s4_6">=</div>
<div id="t6_6" class="t s4_6">=</div>
<div id="t7_6" class="t s4_6">=</div>
<div id="t8_6" class="t s4_6">=</div>
<div id="t9_6" class="t s4_6">=</div>
<div id="ta_6" class="t s4_6">=</div>
<div id="tb_6" class="t s4_6">=</div>
<div id="tc_6" class="t s4_6">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_7{left:802px;top:1098px;}
#t2_7{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_7{left:165px;top:364px;letter-spacing:-0.4px;}
#t4_7{left:731px;top:195px;}
#t5_7{left:734px;top:217px;}
#t6_7{left:744px;top:239px;}
#t7_7{left:739px;top:283px;}
#t8_7{left:763px;top:415px;}
#t9_7{left:718px;top:472px;}
#ta_7{left:740px;top:626px;}
#tb_7{left:725px;top:736px;}
#tc_7{left:735px;top:758px;}
#td_7{left:730px;top:824px;}
#te_7{left:739px;top:846px;}
#tf_7{left:719px;top:978px;}
#tg_7{left:769px;top:1000px;}

.s1_7{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_27i;
	color: rgb(255,255,255);
}

.s2_7{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_27m;
	color: rgb(35,31,32);
}

.s3_7{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-6_27q;
	color: rgb(35,31,32);
}

.s4_7{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_2m1;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts7" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0100_27m;
	src: url("fonts/Times-New-Roman-Bold0100_27m.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_27i;
	src: url("fonts/Arial-Bold091-625_27i.woff") format("woff");
}

@font-face {
	font-family: Arial083-313_2m1;
	src: url("fonts/Arial083-313_2m1.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0116-6_27q;
	src: url("fonts/Times-New-Roman-Bold0116-6_27q.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg7Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg7" style="-webkit-user-select: none;"><object width="880" height="1155" data="7/7.svg" type="image/svg+xml" id="pdf7" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_7" class="t s1_7">9</div>
<div id="t2_7" class="t s2_7">VII</div>
<div id="t3_7" class="t s3_7">32.3.6</div>
<div id="t4_7" class="t s4_7">=</div>
<div id="t5_7" class="t s4_7">=</div>
<div id="t6_7" class="t s4_7">=</div>
<div id="t7_7" class="t s4_7">=</div>
<div id="t8_7" class="t s4_7">=</div>
<div id="t9_7" class="t s4_7">=</div>
<div id="ta_7" class="t s4_7">=</div>
<div id="tb_7" class="t s4_7">=</div>
<div id="tc_7" class="t s4_7">=</div>
<div id="td_7" class="t s4_7">=</div>
<div id="te_7" class="t s4_7">=</div>
<div id="tf_7" class="t s4_7">=</div>
<div id="tg_7" class="t s4_7">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_8{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_8{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_8{left:104px;top:195px;}
#t4_8{left:105px;top:239px;}
#t5_8{left:133px;top:283px;}
#t6_8{left:79px;top:327px;}
#t7_8{left:65px;top:481px;}
#t8_8{left:96px;top:657px;}
#t9_8{left:68px;top:1053px;}

.s1_8{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_2mi;
	color: rgb(255,255,255);
}

.s2_8{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_2mm;
	color: rgb(35,31,32);
}

.s3_8{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_2zl;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts8" type="text/css" >

@font-face {
	font-family: Arial083-313_2zl;
	src: url("fonts/Arial083-313_2zl.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_2mi;
	src: url("fonts/Arial-Bold091-625_2mi.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_2mm;
	src: url("fonts/Times-New-Roman-Bold0100_2mm.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg8Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg8" style="-webkit-user-select: none;"><object width="880" height="1155" data="8/8.svg" type="image/svg+xml" id="pdf8" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_8" class="t s1_8">10</div>
<div id="t2_8" class="t s2_8">VII</div>
<div id="t3_8" class="t s3_8">=</div>
<div id="t4_8" class="t s3_8">=</div>
<div id="t5_8" class="t s3_8">=</div>
<div id="t6_8" class="t s3_8">=</div>
<div id="t7_8" class="t s3_8">=</div>
<div id="t8_8" class="t s3_8">=</div>
<div id="t9_8" class="t s3_8">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_9{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_9{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_9{left:165px;top:854px;letter-spacing:-0.4px;}
#t4_9{left:734px;top:968px;}

.s1_9{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_2-0;
	color: rgb(255,255,255);
}

.s2_9{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_2-4;
	color: rgb(35,31,32);
}

.s3_9{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-6_2-8;
	color: rgb(35,31,32);
}

.s4_9{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_3cl;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts9" type="text/css" >

@font-face {
	font-family: Arial083-313_3cl;
	src: url("fonts/Arial083-313_3cl.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_2-0;
	src: url("fonts/Arial-Bold091-625_2-0.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_2-4;
	src: url("fonts/Times-New-Roman-Bold0100_2-4.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0116-6_2-8;
	src: url("fonts/Times-New-Roman-Bold0116-6_2-8.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg9Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg9" style="-webkit-user-select: none;"><object width="880" height="1155" data="9/9.svg" type="image/svg+xml" id="pdf9" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_9" class="t s1_9">11</div>
<div id="t2_9" class="t s2_9">VII</div>
<div id="t3_9" class="t s3_9">32.3.7</div>
<div id="t4_9" class="t s4_9">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_10{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_10{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_10{left:94px;top:221px;}
#t4_10{left:84px;top:309px;}
#t5_10{left:73px;top:331px;}
#t6_10{left:92px;top:375px;}
#t7_10{left:84px;top:397px;}
#t8_10{left:85px;top:573px;}
#t9_10{left:110px;top:705px;}

.s1_10{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_3d0;
	color: rgb(255,255,255);
}

.s2_10{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_3d4;
	color: rgb(35,31,32);
}

.s3_10{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_3q3;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts10" type="text/css" >

@font-face {
	font-family: Arial083-313_3q3;
	src: url("fonts/Arial083-313_3q3.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_3d4;
	src: url("fonts/Times-New-Roman-Bold0100_3d4.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_3d0;
	src: url("fonts/Arial-Bold091-625_3d0.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg10Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg10" style="-webkit-user-select: none;"><object width="880" height="1155" data="10/10.svg" type="image/svg+xml" id="pdf10" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_10" class="t s1_10">12</div>
<div id="t2_10" class="t s2_10">VII</div>
<div id="t3_10" class="t s3_10">=</div>
<div id="t4_10" class="t s3_10">=</div>
<div id="t5_10" class="t s3_10">=</div>
<div id="t6_10" class="t s3_10">=</div>
<div id="t7_10" class="t s3_10">=</div>
<div id="t8_10" class="t s3_10">=</div>
<div id="t9_10" class="t s3_10">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_11{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_11{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_11{left:165px;top:739px;letter-spacing:-0.4px;}
#t4_11{left:737px;top:430px;}
#t5_11{left:724px;top:474px;}
#t6_11{left:735px;top:782px;}
#t7_11{left:723px;top:958px;}

.s1_11{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_3qk;
	color: rgb(255,255,255);
}

.s2_11{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_3qo;
	color: rgb(35,31,32);
}

.s3_11{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-_3qs;
	color: rgb(35,31,32);
}

.s4_11{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_433;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts11" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0116-_3qs;
	src: url("fonts/Times-New-Roman-Bold0116-_3qs.woff") format("woff");
}

@font-face {
	font-family: Arial083-313_433;
	src: url("fonts/Arial083-313_433.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_3qk;
	src: url("fonts/Arial-Bold091-625_3qk.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_3qo;
	src: url("fonts/Times-New-Roman-Bold0100_3qo.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg11Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg11" style="-webkit-user-select: none;"><object width="880" height="1155" data="11/11.svg" type="image/svg+xml" id="pdf11" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_11" class="t s1_11">13</div>
<div id="t2_11" class="t s2_11">VII</div>
<div id="t3_11" class="t s3_11">32.3.8</div>
<div id="t4_11" class="t s4_11">=</div>
<div id="t5_11" class="t s4_11">=</div>
<div id="t6_11" class="t s4_11">=</div>
<div id="t7_11" class="t s4_11">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_12{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_12{left:134px;top:39px;letter-spacing:-0.2px;}

.s1_12{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_43k;
	color: rgb(255,255,255);
}

.s2_12{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_43o;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts12" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0100_43o;
	src: url("fonts/Times-New-Roman-Bold0100_43o.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_43k;
	src: url("fonts/Arial-Bold091-625_43k.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg12Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg12" style="-webkit-user-select: none;"><object width="880" height="1155" data="12/12.svg" type="image/svg+xml" id="pdf12" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_12" class="t s1_12">14</div>
<div id="t2_12" class="t s2_12">VII</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_13{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_13{left:782px;top:37px;letter-spacing:-0.2px;}

.s1_13{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_4b1;
	color: rgb(255,255,255);
}

.s2_13{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_4b5;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts13" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0100_4b5;
	src: url("fonts/Times-New-Roman-Bold0100_4b5.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_4b1;
	src: url("fonts/Arial-Bold091-625_4b1.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg13Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg13" style="-webkit-user-select: none;"><object width="880" height="1155" data="13/13.svg" type="image/svg+xml" id="pdf13" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_13" class="t s1_13">15</div>
<div id="t2_13" class="t s2_13">VII</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_14{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_14{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_14{left:331px;top:88px;letter-spacing:-0.4px;}
#t4_14{left:96px;top:231px;}
#t5_14{left:138px;top:407px;}
#t6_14{left:87px;top:715px;}
#t7_14{left:94px;top:737px;}
#t8_14{left:112px;top:781px;}
#t9_14{left:72px;top:803px;}
#ta_14{left:80px;top:825px;}

.s1_14{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_4ik;
	color: rgb(255,255,255);
}

.s2_14{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_4io;
	color: rgb(35,31,32);
}

.s3_14{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-_4is;
	color: rgb(35,31,32);
}

.s4_14{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_4x3;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts14" type="text/css" >

@font-face {
	font-family: Arial-Bold091-625_4ik;
	src: url("fonts/Arial-Bold091-625_4ik.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_4io;
	src: url("fonts/Times-New-Roman-Bold0100_4io.woff") format("woff");
}

@font-face {
	font-family: Arial083-313_4x3;
	src: url("fonts/Arial083-313_4x3.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0116-_4is;
	src: url("fonts/Times-New-Roman-Bold0116-_4is.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg14Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg14" style="-webkit-user-select: none;"><object width="880" height="1155" data="14/14.svg" type="image/svg+xml" id="pdf14" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_14" class="t s1_14">16</div>
<div id="t2_14" class="t s2_14">VII</div>
<div id="t3_14" class="t s3_14">32.3.9</div>
<div id="t4_14" class="t s4_14">=</div>
<div id="t5_14" class="t s4_14">=</div>
<div id="t6_14" class="t s4_14">=</div>
<div id="t7_14" class="t s4_14">=</div>
<div id="t8_14" class="t s4_14">=</div>
<div id="t9_14" class="t s4_14">=</div>
<div id="ta_14" class="t s4_14">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_15{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_15{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_15{left:724px;top:195px;}
#t4_15{left:724px;top:217px;}
#t5_15{left:759px;top:459px;}
#t6_15{left:755px;top:481px;}
#t7_15{left:725px;top:679px;}
#t8_15{left:747px;top:701px;}
#t9_15{left:732px;top:723px;}

.s1_15{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_4xk;
	color: rgb(255,255,255);
}

.s2_15{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_4xo;
	color: rgb(35,31,32);
}

.s3_15{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_58n;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts15" type="text/css" >

@font-face {
	font-family: Arial083-313_58n;
	src: url("fonts/Arial083-313_58n.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_4xo;
	src: url("fonts/Times-New-Roman-Bold0100_4xo.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_4xk;
	src: url("fonts/Arial-Bold091-625_4xk.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg15Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg15" style="-webkit-user-select: none;"><object width="880" height="1155" data="15/15.svg" type="image/svg+xml" id="pdf15" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_15" class="t s1_15">17</div>
<div id="t2_15" class="t s2_15">VII</div>
<div id="t3_15" class="t s3_15">=</div>
<div id="t4_15" class="t s3_15">=</div>
<div id="t5_15" class="t s3_15">=</div>
<div id="t6_15" class="t s3_15">=</div>
<div id="t7_15" class="t s3_15">=</div>
<div id="t8_15" class="t s3_15">=</div>
<div id="t9_15" class="t s3_15">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_16{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_16{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_16{left:332px;top:623px;letter-spacing:-0.4px;}
#t4_16{left:73px;top:195px;}
#t5_16{left:101px;top:261px;}
#t6_16{left:105px;top:481px;}
#t7_16{left:99px;top:525px;}
#t8_16{left:65px;top:679px;}
#t9_16{left:75px;top:745px;}
#ta_16{left:88px;top:767px;}
#tb_16{left:77px;top:811px;}
#tc_16{left:93px;top:833px;}
#td_16{left:69px;top:1009px;}

.s1_16{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_592;
	color: rgb(255,255,255);
}

.s2_16{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_596;
	color: rgb(35,31,32);
}

.s3_16{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-_59a;
	color: rgb(35,31,32);
}

.s4_16{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_5nn;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts16" type="text/css" >

@font-face {
	font-family: Arial083-313_5nn;
	src: url("fonts/Arial083-313_5nn.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_592;
	src: url("fonts/Arial-Bold091-625_592.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0116-_59a;
	src: url("fonts/Times-New-Roman-Bold0116-_59a.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_596;
	src: url("fonts/Times-New-Roman-Bold0100_596.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg16Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg16" style="-webkit-user-select: none;"><object width="880" height="1155" data="16/16.svg" type="image/svg+xml" id="pdf16" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_16" class="t s1_16">18</div>
<div id="t2_16" class="t s2_16">VII</div>
<div id="t3_16" class="t s3_16">32.3.10</div>
<div id="t4_16" class="t s4_16">=</div>
<div id="t5_16" class="t s4_16">=</div>
<div id="t6_16" class="t s4_16">=</div>
<div id="t7_16" class="t s4_16">=</div>
<div id="t8_16" class="t s4_16">=</div>
<div id="t9_16" class="t s4_16">=</div>
<div id="ta_16" class="t s4_16">=</div>
<div id="tb_16" class="t s4_16">=</div>
<div id="tc_16" class="t s4_16">=</div>
<div id="td_16" class="t s4_16">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_17{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_17{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_17{left:747px;top:274px;}
#t4_17{left:737px;top:318px;}
#t5_17{left:745px;top:362px;}
#t6_17{left:717px;top:560px;}

.s1_17{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_5o2;
	color: rgb(255,255,255);
}

.s2_17{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_5o6;
	color: rgb(35,31,32);
}

.s3_17{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_5_5;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts17" type="text/css" >

@font-face {
	font-family: Arial083-313_5_5;
	src: url("fonts/Arial083-313_5_5.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_5o6;
	src: url("fonts/Times-New-Roman-Bold0100_5o6.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_5o2;
	src: url("fonts/Arial-Bold091-625_5o2.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg17Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg17" style="-webkit-user-select: none;"><object width="880" height="1155" data="17/17.svg" type="image/svg+xml" id="pdf17" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_17" class="t s1_17">19</div>
<div id="t2_17" class="t s2_17">VII</div>
<div id="t3_17" class="t s3_17">=</div>
<div id="t4_17" class="t s3_17">=</div>
<div id="t5_17" class="t s3_17">=</div>
<div id="t6_17" class="t s3_17">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_18{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_18{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_18{left:72px;top:195px;}
#t4_18{left:79px;top:261px;}
#t5_18{left:94px;top:305px;}
#t6_18{left:79px;top:327px;}
#t7_18{left:76px;top:558px;}
#t8_18{left:80px;top:602px;}
#t9_18{left:71px;top:646px;}
#ta_18{left:71px;top:668px;}
#tb_18{left:135px;top:822px;}

.s1_18{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_5_m;
	color: rgb(255,255,255);
}

.s2_18{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_5_q;
	color: rgb(35,31,32);
}

.s3_18{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_6cp;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts18" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0100_5_q;
	src: url("fonts/Times-New-Roman-Bold0100_5_q.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_5_m;
	src: url("fonts/Arial-Bold091-625_5_m.woff") format("woff");
}

@font-face {
	font-family: Arial083-313_6cp;
	src: url("fonts/Arial083-313_6cp.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg18Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg18" style="-webkit-user-select: none;"><object width="880" height="1155" data="18/18.svg" type="image/svg+xml" id="pdf18" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_18" class="t s1_18">20</div>
<div id="t2_18" class="t s2_18">VII</div>
<div id="t3_18" class="t s3_18">=</div>
<div id="t4_18" class="t s3_18">=</div>
<div id="t5_18" class="t s3_18">=</div>
<div id="t6_18" class="t s3_18">=</div>
<div id="t7_18" class="t s3_18">=</div>
<div id="t8_18" class="t s3_18">=</div>
<div id="t9_18" class="t s3_18">=</div>
<div id="ta_18" class="t s3_18">=</div>
<div id="tb_18" class="t s3_18">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_19{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_19{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_19{left:726px;top:511px;}
#t4_19{left:747px;top:533px;}
#t5_19{left:717px;top:753px;}
#t6_19{left:752px;top:885px;}
#t7_19{left:768px;top:995px;}
#t8_19{left:739px;top:1039px;}

.s1_19{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_6d4;
	color: rgb(255,255,255);
}

.s2_19{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_6d8;
	color: rgb(35,31,32);
}

.s3_19{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_6q7;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts19" type="text/css" >

@font-face {
	font-family: Arial083-313_6q7;
	src: url("fonts/Arial083-313_6q7.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_6d4;
	src: url("fonts/Arial-Bold091-625_6d4.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_6d8;
	src: url("fonts/Times-New-Roman-Bold0100_6d8.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg19Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg19" style="-webkit-user-select: none;"><object width="880" height="1155" data="19/19.svg" type="image/svg+xml" id="pdf19" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_19" class="t s1_19">21</div>
<div id="t2_19" class="t s2_19">VII</div>
<div id="t3_19" class="t s3_19">=</div>
<div id="t4_19" class="t s3_19">=</div>
<div id="t5_19" class="t s3_19">=</div>
<div id="t6_19" class="t s3_19">=</div>
<div id="t7_19" class="t s3_19">=</div>
<div id="t8_19" class="t s3_19">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_20{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_20{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_20{left:109px;top:243px;}
#t4_20{left:61px;top:727px;}

.s1_20{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_6qo;
	color: rgb(255,255,255);
}

.s2_20{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_6qs;
	color: rgb(35,31,32);
}

.s3_20{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_71r;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts20" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0100_6qs;
	src: url("fonts/Times-New-Roman-Bold0100_6qs.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_6qo;
	src: url("fonts/Arial-Bold091-625_6qo.woff") format("woff");
}

@font-face {
	font-family: Arial083-313_71r;
	src: url("fonts/Arial083-313_71r.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg20Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg20" style="-webkit-user-select: none;"><object width="880" height="1155" data="20/20.svg" type="image/svg+xml" id="pdf20" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_20" class="t s1_20">22</div>
<div id="t2_20" class="t s2_20">VII</div>
<div id="t3_20" class="t s3_20">=</div>
<div id="t4_20" class="t s3_20">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_21{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_21{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_21{left:734px;top:195px;}
#t4_21{left:775px;top:393px;}
#t5_21{left:730px;top:569px;}
#t6_21{left:720px;top:679px;}
#t7_21{left:727px;top:877px;}

.s1_21{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_726;
	color: rgb(255,255,255);
}

.s2_21{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_72a;
	color: rgb(35,31,32);
}

.s3_21{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_7f9;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts21" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0100_72a;
	src: url("fonts/Times-New-Roman-Bold0100_72a.woff") format("woff");
}

@font-face {
	font-family: Arial083-313_7f9;
	src: url("fonts/Arial083-313_7f9.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_726;
	src: url("fonts/Arial-Bold091-625_726.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg21Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg21" style="-webkit-user-select: none;"><object width="880" height="1155" data="21/21.svg" type="image/svg+xml" id="pdf21" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_21" class="t s1_21">23</div>
<div id="t2_21" class="t s2_21">VII</div>
<div id="t3_21" class="t s3_21">=</div>
<div id="t4_21" class="t s3_21">=</div>
<div id="t5_21" class="t s3_21">=</div>
<div id="t6_21" class="t s3_21">=</div>
<div id="t7_21" class="t s3_21">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_22{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_22{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_22{left:332px;top:821px;letter-spacing:-1px;}

.s1_22{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_7fq;
	color: rgb(255,255,255);
}

.s2_22{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_7fu;
	color: rgb(35,31,32);
}

.s3_22{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-_7fy;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts22" type="text/css" >

@font-face {
	font-family: Arial-Bold091-625_7fq;
	src: url("fonts/Arial-Bold091-625_7fq.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_7fu;
	src: url("fonts/Times-New-Roman-Bold0100_7fu.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0116-_7fy;
	src: url("fonts/Times-New-Roman-Bold0116-_7fy.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg22Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg22" style="-webkit-user-select: none;"><object width="880" height="1155" data="22/22.svg" type="image/svg+xml" id="pdf22" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_22" class="t s1_22">24</div>
<div id="t2_22" class="t s2_22">VII</div>
<div id="t3_22" class="t s3_22">32.3.11</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_23{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_23{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_23{left:724px;top:933px;}
#t4_23{left:757px;top:955px;}
#t5_23{left:731px;top:1030px;}

.s1_23{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_7op;
	color: rgb(255,255,255);
}

.s2_23{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_7ot;
	color: rgb(35,31,32);
}

.s3_23{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_7_s;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts23" type="text/css" >

@font-face {
	font-family: Arial083-313_7_s;
	src: url("fonts/Arial083-313_7_s.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_7ot;
	src: url("fonts/Times-New-Roman-Bold0100_7ot.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_7op;
	src: url("fonts/Arial-Bold091-625_7op.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg23Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg23" style="-webkit-user-select: none;"><object width="880" height="1155" data="23/23.svg" type="image/svg+xml" id="pdf23" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_23" class="t s1_23">25</div>
<div id="t2_23" class="t s2_23">VII</div>
<div id="t3_23" class="t s3_23">=</div>
<div id="t4_23" class="t s3_23">=</div>
<div id="t5_23" class="t s3_23">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_24{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_24{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_24{left:95px;top:225px;}
#t4_24{left:75px;top:269px;}
#t5_24{left:87px;top:423px;}
#t6_24{left:63px;top:577px;}
#t7_24{left:69px;top:1017px;}

.s1_24{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_807;
	color: rgb(255,255,255);
}

.s2_24{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_80b;
	color: rgb(35,31,32);
}

.s3_24{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_8da;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts24" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0100_80b;
	src: url("fonts/Times-New-Roman-Bold0100_80b.woff") format("woff");
}

@font-face {
	font-family: Arial083-313_8da;
	src: url("fonts/Arial083-313_8da.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_807;
	src: url("fonts/Arial-Bold091-625_807.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg24Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg24" style="-webkit-user-select: none;"><object width="880" height="1155" data="24/24.svg" type="image/svg+xml" id="pdf24" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_24" class="t s1_24">26</div>
<div id="t2_24" class="t s2_24">VII</div>
<div id="t3_24" class="t s3_24">=</div>
<div id="t4_24" class="t s3_24">=</div>
<div id="t5_24" class="t s3_24">=</div>
<div id="t6_24" class="t s3_24">=</div>
<div id="t7_24" class="t s3_24">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_25{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_25{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_25{left:726px;top:195px;}
#t4_25{left:759px;top:217px;}
#t5_25{left:745px;top:305px;}
#t6_25{left:716px;top:692px;}
#t7_25{left:729px;top:780px;}

.s1_25{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_8dr;
	color: rgb(255,255,255);
}

.s2_25{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_8dv;
	color: rgb(35,31,32);
}

.s3_25{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_8qu;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts25" type="text/css" >

@font-face {
	font-family: Arial083-313_8qu;
	src: url("fonts/Arial083-313_8qu.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_8dr;
	src: url("fonts/Arial-Bold091-625_8dr.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_8dv;
	src: url("fonts/Times-New-Roman-Bold0100_8dv.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg25Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg25" style="-webkit-user-select: none;"><object width="880" height="1155" data="25/25.svg" type="image/svg+xml" id="pdf25" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_25" class="t s1_25">27</div>
<div id="t2_25" class="t s2_25">VII</div>
<div id="t3_25" class="t s3_25">=</div>
<div id="t4_25" class="t s3_25">=</div>
<div id="t5_25" class="t s3_25">=</div>
<div id="t6_25" class="t s3_25">=</div>
<div id="t7_25" class="t s3_25">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_26{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_26{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_26{left:66px;top:195px;}
#t4_26{left:78px;top:305px;}
#t5_26{left:71px;top:327px;}
#t6_26{left:88px;top:468px;}
#t7_26{left:96px;top:798px;}
#t8_26{left:71px;top:952px;}

.s1_26{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_8r9;
	color: rgb(255,255,255);
}

.s2_26{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_8rd;
	color: rgb(35,31,32);
}

.s3_26{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_92c;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts26" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0100_8rd;
	src: url("fonts/Times-New-Roman-Bold0100_8rd.woff") format("woff");
}

@font-face {
	font-family: Arial083-313_92c;
	src: url("fonts/Arial083-313_92c.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_8r9;
	src: url("fonts/Arial-Bold091-625_8r9.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg26Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg26" style="-webkit-user-select: none;"><object width="880" height="1155" data="26/26.svg" type="image/svg+xml" id="pdf26" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_26" class="t s1_26">28</div>
<div id="t2_26" class="t s2_26">VII</div>
<div id="t3_26" class="t s3_26">=</div>
<div id="t4_26" class="t s3_26">=</div>
<div id="t5_26" class="t s3_26">=</div>
<div id="t6_26" class="t s3_26">=</div>
<div id="t7_26" class="t s3_26">=</div>
<div id="t8_26" class="t s3_26">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_27{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_27{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_27{left:722px;top:460px;}
#t4_27{left:725px;top:790px;}
#t5_27{left:739px;top:812px;}

.s1_27{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_92t;
	color: rgb(255,255,255);
}

.s2_27{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_92x;
	color: rgb(35,31,32);
}

.s3_27{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_9fw;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts27" type="text/css" >

@font-face {
	font-family: Arial083-313_9fw;
	src: url("fonts/Arial083-313_9fw.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_92x;
	src: url("fonts/Times-New-Roman-Bold0100_92x.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_92t;
	src: url("fonts/Arial-Bold091-625_92t.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg27Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg27" style="-webkit-user-select: none;"><object width="880" height="1155" data="27/27.svg" type="image/svg+xml" id="pdf27" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_27" class="t s1_27">29</div>
<div id="t2_27" class="t s2_27">VII</div>
<div id="t3_27" class="t s3_27">=</div>
<div id="t4_27" class="t s3_27">=</div>
<div id="t5_27" class="t s3_27">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_28{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_28{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_28{left:79px;top:232px;}
#t4_28{left:79px;top:254px;}
#t5_28{left:56px;top:474px;}

.s1_28{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_9gb;
	color: rgb(255,255,255);
}

.s2_28{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_9gf;
	color: rgb(35,31,32);
}

.s3_28{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_9te;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts28" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0100_9gf;
	src: url("fonts/Times-New-Roman-Bold0100_9gf.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_9gb;
	src: url("fonts/Arial-Bold091-625_9gb.woff") format("woff");
}

@font-face {
	font-family: Arial083-313_9te;
	src: url("fonts/Arial083-313_9te.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg28Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg28" style="-webkit-user-select: none;"><object width="880" height="1155" data="28/28.svg" type="image/svg+xml" id="pdf28" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_28" class="t s1_28">30</div>
<div id="t2_28" class="t s2_28">VII</div>
<div id="t3_28" class="t s3_28">=</div>
<div id="t4_28" class="t s3_28">=</div>
<div id="t5_28" class="t s3_28">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_29{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_29{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_29{left:166px;top:156px;letter-spacing:-0.4px;}
#t4_29{left:745px;top:535px;}
#t5_29{left:748px;top:557px;}
#t6_29{left:741px;top:953px;}
#t7_29{left:718px;top:975px;}

.s1_29{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_9tv;
	color: rgb(255,255,255);
}

.s2_29{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_9tz;
	color: rgb(35,31,32);
}

.s3_29{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-_9u1;
	color: rgb(35,31,32);
}

.s4_29{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_a6e;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts29" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0116-_9u1;
	src: url("fonts/Times-New-Roman-Bold0116-_9u1.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_9tv;
	src: url("fonts/Arial-Bold091-625_9tv.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_9tz;
	src: url("fonts/Times-New-Roman-Bold0100_9tz.woff") format("woff");
}

@font-face {
	font-family: Arial083-313_a6e;
	src: url("fonts/Arial083-313_a6e.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg29Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg29" style="-webkit-user-select: none;"><object width="880" height="1155" data="29/29.svg" type="image/svg+xml" id="pdf29" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_29" class="t s1_29">31</div>
<div id="t2_29" class="t s2_29">VII</div>
<div id="t3_29" class="t s3_29">32.3.13</div>
<div id="t4_29" class="t s4_29">=</div>
<div id="t5_29" class="t s4_29">=</div>
<div id="t6_29" class="t s4_29">=</div>
<div id="t7_29" class="t s4_29">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_30{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_30{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_30{left:75px;top:303px;}
#t4_30{left:95px;top:655px;}

.s1_30{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_a6v;
	color: rgb(255,255,255);
}

.s2_30{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_a6z;
	color: rgb(35,31,32);
}

.s3_30{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_ajy;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts30" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0100_a6z;
	src: url("fonts/Times-New-Roman-Bold0100_a6z.woff") format("woff");
}

@font-face {
	font-family: Arial083-313_ajy;
	src: url("fonts/Arial083-313_ajy.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_a6v;
	src: url("fonts/Arial-Bold091-625_a6v.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg30Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg30" style="-webkit-user-select: none;"><object width="880" height="1155" data="30/30.svg" type="image/svg+xml" id="pdf30" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_30" class="t s1_30">32</div>
<div id="t2_30" class="t s2_30">VII</div>
<div id="t3_30" class="t s3_30">=</div>
<div id="t4_30" class="t s3_30">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_31{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_31{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_31{left:744px;top:195px;}
#t4_31{left:751px;top:239px;}
#t5_31{left:720px;top:283px;}

.s1_31{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_akd;
	color: rgb(255,255,255);
}

.s2_31{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_akh;
	color: rgb(35,31,32);
}

.s3_31{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_axg;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts31" type="text/css" >

@font-face {
	font-family: Arial-Bold091-625_akd;
	src: url("fonts/Arial-Bold091-625_akd.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_akh;
	src: url("fonts/Times-New-Roman-Bold0100_akh.woff") format("woff");
}

@font-face {
	font-family: Arial083-313_axg;
	src: url("fonts/Arial083-313_axg.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg31Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg31" style="-webkit-user-select: none;"><object width="880" height="1155" data="31/31.svg" type="image/svg+xml" id="pdf31" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_31" class="t s1_31">33</div>
<div id="t2_31" class="t s2_31">VII</div>
<div id="t3_31" class="t s3_31">=</div>
<div id="t4_31" class="t s3_31">=</div>
<div id="t5_31" class="t s3_31">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_32{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_32{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_32{left:55px;top:195px;}
#t4_32{left:71px;top:327px;}
#t5_32{left:65px;top:386px;}

.s1_32{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_axx;
	color: rgb(255,255,255);
}

.s2_32{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_ax_;
	color: rgb(35,31,32);
}

.s3_32{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_b8-;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts32" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0100_ax_;
	src: url("fonts/Times-New-Roman-Bold0100_ax_.woff") format("woff");
}

@font-face {
	font-family: Arial083-313_b8-;
	src: url("fonts/Arial083-313_b8-.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_axx;
	src: url("fonts/Arial-Bold091-625_axx.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg32Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg32" style="-webkit-user-select: none;"><object width="880" height="1155" data="32/32.svg" type="image/svg+xml" id="pdf32" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_32" class="t s1_32">34</div>
<div id="t2_32" class="t s2_32">VII</div>
<div id="t3_32" class="t s3_32">=</div>
<div id="t4_32" class="t s3_32">=</div>
<div id="t5_32" class="t s3_32">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_33{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_33{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_33{left:722px;top:271px;}
#t4_33{left:728px;top:887px;}

.s1_33{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_b9f;
	color: rgb(255,255,255);
}

.s2_33{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_b9j;
	color: rgb(35,31,32);
}

.s3_33{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_bmi;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts33" type="text/css" >

@font-face {
	font-family: Arial083-313_bmi;
	src: url("fonts/Arial083-313_bmi.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_b9f;
	src: url("fonts/Arial-Bold091-625_b9f.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_b9j;
	src: url("fonts/Times-New-Roman-Bold0100_b9j.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg33Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg33" style="-webkit-user-select: none;"><object width="880" height="1155" data="33/33.svg" type="image/svg+xml" id="pdf33" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_33" class="t s1_33">35</div>
<div id="t2_33" class="t s2_33">VII</div>
<div id="t3_33" class="t s3_33">=</div>
<div id="t4_33" class="t s3_33">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_34{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_34{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_34{left:96px;top:333px;}

.s1_34{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_bmz;
	color: rgb(255,255,255);
}

.s2_34{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_bn1;
	color: rgb(35,31,32);
}

.s3_34{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_b-0;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts34" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0100_bn1;
	src: url("fonts/Times-New-Roman-Bold0100_bn1.woff") format("woff");
}

@font-face {
	font-family: Arial083-313_b-0;
	src: url("fonts/Arial083-313_b-0.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_bmz;
	src: url("fonts/Arial-Bold091-625_bmz.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg34Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg34" style="-webkit-user-select: none;"><object width="880" height="1155" data="34/34.svg" type="image/svg+xml" id="pdf34" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_34" class="t s1_34">36</div>
<div id="t2_34" class="t s2_34">VII</div>
<div id="t3_34" class="t s3_34">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_35{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_35{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_35{left:721px;top:949px;}
#t4_35{left:756px;top:1059px;}
#t5_35{left:742px;top:256px;}
#t6_35{left:763px;top:313px;}

.s1_35{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_b-h;
	color: rgb(255,255,255);
}

.s2_35{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_b-l;
	color: rgb(35,31,32);
}

.s3_35{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_cbk;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts35" type="text/css" >

@font-face {
	font-family: Arial083-313_cbk;
	src: url("fonts/Arial083-313_cbk.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_b-h;
	src: url("fonts/Arial-Bold091-625_b-h.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_b-l;
	src: url("fonts/Times-New-Roman-Bold0100_b-l.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg35Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg35" style="-webkit-user-select: none;"><object width="880" height="1155" data="35/35.svg" type="image/svg+xml" id="pdf35" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_35" class="t s1_35">37</div>
<div id="t2_35" class="t s2_35">VII</div>
<div id="t3_35" class="t s3_35">=</div>
<div id="t4_35" class="t s3_35">=</div>
<div id="t5_35" class="t s3_35">=</div>
<div id="t6_35" class="t s3_35">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_36{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_36{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_36{left:76px;top:427px;}
#t4_36{left:150px;top:515px;}
#t5_36{left:101px;top:713px;}
#t6_36{left:103px;top:942px;}
#t7_36{left:91px;top:1008px;}
#t8_36{left:68px;top:1030px;}

.s1_36{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_cb_;
	color: rgb(255,255,255);
}

.s2_36{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_cc3;
	color: rgb(35,31,32);
}

.s3_36{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_cp2;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts36" type="text/css" >

@font-face {
	font-family: Arial083-313_cp2;
	src: url("fonts/Arial083-313_cp2.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_cc3;
	src: url("fonts/Times-New-Roman-Bold0100_cc3.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_cb_;
	src: url("fonts/Arial-Bold091-625_cb_.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg36Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg36" style="-webkit-user-select: none;"><object width="880" height="1155" data="36/36.svg" type="image/svg+xml" id="pdf36" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_36" class="t s1_36">38</div>
<div id="t2_36" class="t s2_36">VII</div>
<div id="t3_36" class="t s3_36">=</div>
<div id="t4_36" class="t s3_36">=</div>
<div id="t5_36" class="t s3_36">=</div>
<div id="t6_36" class="t s3_36">=</div>
<div id="t7_36" class="t s3_36">=</div>
<div id="t8_36" class="t s3_36">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_37{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_37{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_37{left:166px;top:490px;letter-spacing:-0.4px;}
#t4_37{left:724px;top:294px;}
#t5_37{left:728px;top:316px;}

.s1_37{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_cpj;
	color: rgb(255,255,255);
}

.s2_37{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_cpn;
	color: rgb(35,31,32);
}

.s3_37{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-_cpr;
	color: rgb(35,31,32);
}

.s4_37{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_d22;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts37" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0116-_cpr;
	src: url("fonts/Times-New-Roman-Bold0116-_cpr.woff") format("woff");
}

@font-face {
	font-family: Arial083-313_d22;
	src: url("fonts/Arial083-313_d22.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_cpj;
	src: url("fonts/Arial-Bold091-625_cpj.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_cpn;
	src: url("fonts/Times-New-Roman-Bold0100_cpn.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg37Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg37" style="-webkit-user-select: none;"><object width="880" height="1155" data="37/37.svg" type="image/svg+xml" id="pdf37" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_37" class="t s1_37">39</div>
<div id="t2_37" class="t s2_37">VII</div>
<div id="t3_37" class="t s3_37">32.3.14</div>
<div id="t4_37" class="t s4_37">=</div>
<div id="t5_37" class="t s4_37">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_38{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_38{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_38{left:79px;top:420px;}
#t4_38{left:88px;top:750px;}

.s1_38{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_d2j;
	color: rgb(255,255,255);
}

.s2_38{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_d2n;
	color: rgb(35,31,32);
}

.s3_38{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_dfm;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts38" type="text/css" >

@font-face {
	font-family: Arial083-313_dfm;
	src: url("fonts/Arial083-313_dfm.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_d2n;
	src: url("fonts/Times-New-Roman-Bold0100_d2n.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_d2j;
	src: url("fonts/Arial-Bold091-625_d2j.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg38Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg38" style="-webkit-user-select: none;"><object width="880" height="1155" data="38/38.svg" type="image/svg+xml" id="pdf38" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_38" class="t s1_38">40</div>
<div id="t2_38" class="t s2_38">VII</div>
<div id="t3_38" class="t s3_38">=</div>
<div id="t4_38" class="t s3_38">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_39{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_39{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_39{left:166px;top:642px;letter-spacing:-0.4px;}
#t4_39{left:717px;top:195px;}
#t5_39{left:737px;top:365px;}
#t6_39{left:731px;top:440px;}
#t7_39{left:758px;top:506px;}
#t8_39{left:732px;top:572px;}
#t9_39{left:755px;top:825px;}

.s1_39{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_dg1;
	color: rgb(255,255,255);
}

.s2_39{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_dg5;
	color: rgb(35,31,32);
}

.s3_39{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-_dg9;
	color: rgb(35,31,32);
}

.s4_39{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_dum;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts39" type="text/css" >

@font-face {
	font-family: Arial083-313_dum;
	src: url("fonts/Arial083-313_dum.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0116-_dg9;
	src: url("fonts/Times-New-Roman-Bold0116-_dg9.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_dg1;
	src: url("fonts/Arial-Bold091-625_dg1.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_dg5;
	src: url("fonts/Times-New-Roman-Bold0100_dg5.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg39Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg39" style="-webkit-user-select: none;"><object width="880" height="1155" data="39/39.svg" type="image/svg+xml" id="pdf39" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_39" class="t s1_39">41</div>
<div id="t2_39" class="t s2_39">VII</div>
<div id="t3_39" class="t s3_39">32.3.15</div>
<div id="t4_39" class="t s4_39">=</div>
<div id="t5_39" class="t s4_39">=</div>
<div id="t6_39" class="t s4_39">=</div>
<div id="t7_39" class="t s4_39">=</div>
<div id="t8_39" class="t s4_39">=</div>
<div id="t9_39" class="t s4_39">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_40{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_40{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_40{left:102px;top:375px;}
#t4_40{left:88px;top:419px;}
#t5_40{left:105px;top:881px;}
#t6_40{left:71px;top:925px;}

.s1_40{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_dv1;
	color: rgb(255,255,255);
}

.s2_40{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_dv5;
	color: rgb(35,31,32);
}

.s3_40{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_e64;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts40" type="text/css" >

@font-face {
	font-family: Arial083-313_e64;
	src: url("fonts/Arial083-313_e64.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_dv1;
	src: url("fonts/Arial-Bold091-625_dv1.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_dv5;
	src: url("fonts/Times-New-Roman-Bold0100_dv5.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg40Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg40" style="-webkit-user-select: none;"><object width="880" height="1155" data="40/40.svg" type="image/svg+xml" id="pdf40" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_40" class="t s1_40">42</div>
<div id="t2_40" class="t s2_40">VII</div>
<div id="t3_40" class="t s3_40">=</div>
<div id="t4_40" class="t s3_40">=</div>
<div id="t5_40" class="t s3_40">=</div>
<div id="t6_40" class="t s3_40">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_41{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_41{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_41{left:729px;top:416px;}
#t4_41{left:724px;top:658px;}
#t5_41{left:733px;top:680px;}
#t6_41{left:732px;top:724px;}
#t7_41{left:733px;top:790px;}
#t8_41{left:743px;top:878px;}
#t9_41{left:735px;top:966px;}

.s1_41{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_e6l;
	color: rgb(255,255,255);
}

.s2_41{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_e6p;
	color: rgb(35,31,32);
}

.s3_41{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_ejo;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts41" type="text/css" >

@font-face {
	font-family: Arial083-313_ejo;
	src: url("fonts/Arial083-313_ejo.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_e6l;
	src: url("fonts/Arial-Bold091-625_e6l.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_e6p;
	src: url("fonts/Times-New-Roman-Bold0100_e6p.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg41Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg41" style="-webkit-user-select: none;"><object width="880" height="1155" data="41/41.svg" type="image/svg+xml" id="pdf41" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_41" class="t s1_41">43</div>
<div id="t2_41" class="t s2_41">VII</div>
<div id="t3_41" class="t s3_41">=</div>
<div id="t4_41" class="t s3_41">=</div>
<div id="t5_41" class="t s3_41">=</div>
<div id="t6_41" class="t s3_41">=</div>
<div id="t7_41" class="t s3_41">=</div>
<div id="t8_41" class="t s3_41">=</div>
<div id="t9_41" class="t s3_41">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_42{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_42{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_42{left:332px;top:1008px;letter-spacing:-0.4px;}
#t4_42{left:65px;top:195px;}
#t5_42{left:78px;top:415px;}

.s1_42{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_ek3;
	color: rgb(255,255,255);
}

.s2_42{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_ek7;
	color: rgb(35,31,32);
}

.s3_42{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-_ekb;
	color: rgb(35,31,32);
}

.s4_42{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_eyo;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts42" type="text/css" >

@font-face {
	font-family: Arial083-313_eyo;
	src: url("fonts/Arial083-313_eyo.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_ek3;
	src: url("fonts/Arial-Bold091-625_ek3.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0116-_ekb;
	src: url("fonts/Times-New-Roman-Bold0116-_ekb.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_ek7;
	src: url("fonts/Times-New-Roman-Bold0100_ek7.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg42Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg42" style="-webkit-user-select: none;"><object width="880" height="1155" data="42/42.svg" type="image/svg+xml" id="pdf42" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_42" class="t s1_42">44</div>
<div id="t2_42" class="t s2_42">VII</div>
<div id="t3_42" class="t s3_42">32.3.16</div>
<div id="t4_42" class="t s4_42">=</div>
<div id="t5_42" class="t s4_42">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_43{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_43{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_43{left:742px;top:429px;}

.s1_43{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_ez3;
	color: rgb(255,255,255);
}

.s2_43{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_ez7;
	color: rgb(35,31,32);
}

.s3_43{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_fa6;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts43" type="text/css" >

@font-face {
	font-family: Arial083-313_fa6;
	src: url("fonts/Arial083-313_fa6.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_ez3;
	src: url("fonts/Arial-Bold091-625_ez3.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_ez7;
	src: url("fonts/Times-New-Roman-Bold0100_ez7.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg43Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg43" style="-webkit-user-select: none;"><object width="880" height="1155" data="43/43.svg" type="image/svg+xml" id="pdf43" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_43" class="t s1_43">45</div>
<div id="t2_43" class="t s2_43">VII</div>
<div id="t3_43" class="t s3_43">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_44{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_44{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_44{left:71px;top:229px;}
#t4_44{left:63px;top:748px;}

.s1_44{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_fan;
	color: rgb(255,255,255);
}

.s2_44{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_far;
	color: rgb(35,31,32);
}

.s3_44{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_fnq;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts44" type="text/css" >

@font-face {
	font-family: Arial-Bold091-625_fan;
	src: url("fonts/Arial-Bold091-625_fan.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_far;
	src: url("fonts/Times-New-Roman-Bold0100_far.woff") format("woff");
}

@font-face {
	font-family: Arial083-313_fnq;
	src: url("fonts/Arial083-313_fnq.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg44Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg44" style="-webkit-user-select: none;"><object width="880" height="1155" data="44/44.svg" type="image/svg+xml" id="pdf44" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_44" class="t s1_44">46</div>
<div id="t2_44" class="t s2_44">VII</div>
<div id="t3_44" class="t s3_44">=</div>
<div id="t4_44" class="t s3_44">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_45{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_45{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_45{left:764px;top:594px;}
#t4_45{left:732px;top:638px;}
#t5_45{left:744px;top:682px;}
#t6_45{left:721px;top:858px;}

.s1_45{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_fo5;
	color: rgb(255,255,255);
}

.s2_45{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_fo9;
	color: rgb(35,31,32);
}

.s3_45{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_f_8;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts45" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0100_fo9;
	src: url("fonts/Times-New-Roman-Bold0100_fo9.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_fo5;
	src: url("fonts/Arial-Bold091-625_fo5.woff") format("woff");
}

@font-face {
	font-family: Arial083-313_f_8;
	src: url("fonts/Arial083-313_f_8.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg45Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg45" style="-webkit-user-select: none;"><object width="880" height="1155" data="45/45.svg" type="image/svg+xml" id="pdf45" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_45" class="t s1_45">47</div>
<div id="t2_45" class="t s2_45">VII</div>
<div id="t3_45" class="t s3_45">=</div>
<div id="t4_45" class="t s3_45">=</div>
<div id="t5_45" class="t s3_45">=</div>
<div id="t6_45" class="t s3_45">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_46{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_46{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_46{left:92px;top:195px;}
#t4_46{left:110px;top:322px;}
#t5_46{left:86px;top:388px;}
#t6_46{left:88px;top:841px;}
#t7_46{left:94px;top:973px;}

.s1_46{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_f_p;
	color: rgb(255,255,255);
}

.s2_46{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_f_t;
	color: rgb(35,31,32);
}

.s3_46{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_gcs;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts46" type="text/css" >

@font-face {
	font-family: Arial-Bold091-625_f_p;
	src: url("fonts/Arial-Bold091-625_f_p.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_f_t;
	src: url("fonts/Times-New-Roman-Bold0100_f_t.woff") format("woff");
}

@font-face {
	font-family: Arial083-313_gcs;
	src: url("fonts/Arial083-313_gcs.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg46Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg46" style="-webkit-user-select: none;"><object width="880" height="1155" data="46/46.svg" type="image/svg+xml" id="pdf46" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_46" class="t s1_46">48</div>
<div id="t2_46" class="t s2_46">VII</div>
<div id="t3_46" class="t s3_46">=</div>
<div id="t4_46" class="t s3_46">=</div>
<div id="t5_46" class="t s3_46">=</div>
<div id="t6_46" class="t s3_46">=</div>
<div id="t7_46" class="t s3_46">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_47{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_47{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_47{left:59px;top:315px;letter-spacing:1.2px;}
#t4_47{left:158px;top:365px;letter-spacing:0.5px;}
#t5_47{left:114px;top:407px;}
#t6_47{left:593px;top:407px;}
#t7_47{left:222px;top:908px;letter-spacing:0.6px;}
#t8_47{left:751px;top:195px;}
#t9_47{left:727px;top:239px;}
#ta_47{left:759px;top:261px;}
#tb_47{left:738px;top:327px;}

.s1_47{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_gd7;
	color: rgb(255,255,255);
}

.s2_47{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_gdb;
	color: rgb(35,31,32);
}

.s3_47{
	FONT-SIZE: 79.4px;
	FONT-FAMILY: Times-New-Roman-Bold0108-_gh5;
	color: rgb(255,255,255);
}

.s4_47{
	FONT-SIZE: 79.4px;
	FONT-FAMILY: Times-New-Roman-Bold0108-_gh5;
	color: rgb(35,31,32);
}

.s5_47{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-_gdf;
	color: rgb(35,31,32);
}

.s6_47{
	FONT-SIZE: 61.1px;
	FONT-FAMILY: Arial083-313_gtp;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts47" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0100_gdb;
	src: url("fonts/Times-New-Roman-Bold0100_gdb.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0108-_gh5;
	src: url("fonts/Times-New-Roman-Bold0108-_gh5.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_gd7;
	src: url("fonts/Arial-Bold091-625_gd7.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0116-_gdf;
	src: url("fonts/Times-New-Roman-Bold0116-_gdf.woff") format("woff");
}

@font-face {
	font-family: Arial083-313_gtp;
	src: url("fonts/Arial083-313_gtp.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg47Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg47" style="-webkit-user-select: none;"><object width="880" height="1155" data="47/47.svg" type="image/svg+xml" id="pdf47" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_47" class="t s1_47">49</div>
<div id="t2_47" class="t s2_47">VII</div>
<div id="t3_47" class="t s3_47">32.4</div>
<div id="t4_47" class="t s4_47">32.4.1</div>
<div id="t5_47" class="t s5_47">[</div>
<div id="t6_47" class="t s5_47">]</div>
<div id="t7_47" class="t s3_47">32.1</div>
<div id="t8_47" class="t s6_47">=</div>
<div id="t9_47" class="t s6_47">=</div>
<div id="ta_47" class="t s6_47">=</div>
<div id="tb_47" class="t s6_47">=</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_48{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_48{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_48{left:325px;top:303px;letter-spacing:0.2px;}
#t4_48{left:351px;top:348px;}
#t5_48{left:688px;top:348px;}
#t6_48{left:388px;top:887px;letter-spacing:0.6px;}

.s1_48{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_gu5;
	color: rgb(255,255,255);
}

.s2_48{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_gu9;
	color: rgb(35,31,32);
}

.s3_48{
	FONT-SIZE: 91.7px;
	FONT-FAMILY: Times-New-Roman-Bold0125_h2q;
	color: rgb(35,31,32);
}

.s4_48{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-_gud;
	color: rgb(35,31,32);
}

.s5_48{
	FONT-SIZE: 79.4px;
	FONT-FAMILY: Times-New-Roman-Bold0108-_gy3;
	color: rgb(255,255,255);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts48" type="text/css" >

@font-face {
	font-family: Arial-Bold091-625_gu5;
	src: url("fonts/Arial-Bold091-625_gu5.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0116-_gud;
	src: url("fonts/Times-New-Roman-Bold0116-_gud.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_gu9;
	src: url("fonts/Times-New-Roman-Bold0100_gu9.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0125_h2q;
	src: url("fonts/Times-New-Roman-Bold0125_h2q.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0108-_gy3;
	src: url("fonts/Times-New-Roman-Bold0108-_gy3.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg48Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg48" style="-webkit-user-select: none;"><object width="880" height="1155" data="48/48.svg" type="image/svg+xml" id="pdf48" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_48" class="t s1_48">50</div>
<div id="t2_48" class="t s2_48">VII</div>
<div id="t3_48" class="t s3_48">32.4.2</div>
<div id="t4_48" class="t s4_48">[</div>
<div id="t5_48" class="t s4_48">]</div>
<div id="t6_48" class="t s5_48">32.2</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_49{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_49{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_49{left:159px;top:439px;letter-spacing:0.2px;}
#t4_49{left:116px;top:485px;}
#t5_49{left:591px;top:485px;}

.s1_49{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_h36;
	color: rgb(255,255,255);
}

.s2_49{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_h3a;
	color: rgb(35,31,32);
}

.s3_49{
	FONT-SIZE: 91.7px;
	FONT-FAMILY: Times-New-Roman-Bold0125_hdi;
	color: rgb(35,31,32);
}

.s4_49{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-_h3e;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts49" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0100_h3a;
	src: url("fonts/Times-New-Roman-Bold0100_h3a.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_h36;
	src: url("fonts/Arial-Bold091-625_h36.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0125_hdi;
	src: url("fonts/Times-New-Roman-Bold0125_hdi.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0116-_h3e;
	src: url("fonts/Times-New-Roman-Bold0116-_h3e.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg49Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg49" style="-webkit-user-select: none;"><object width="880" height="1155" data="49/49.svg" type="image/svg+xml" id="pdf49" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_49" class="t s1_49">51</div>
<div id="t2_49" class="t s2_49">VII</div>
<div id="t3_49" class="t s3_49">32.4.3</div>
<div id="t4_49" class="t s4_49">[</div>
<div id="t5_49" class="t s4_49">]</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_50{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_50{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_50{left:388px;top:99px;letter-spacing:0.6px;}
#t4_50{left:325px;top:407px;letter-spacing:0.2px;}
#t5_50{left:327px;top:452px;}
#t6_50{left:712px;top:452px;}

.s1_50{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_hdz;
	color: rgb(255,255,255);
}

.s2_50{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_he1;
	color: rgb(35,31,32);
}

.s3_50{
	FONT-SIZE: 79.4px;
	FONT-FAMILY: Times-New-Roman-Bold0108-_hhx;
	color: rgb(255,255,255);
}

.s4_50{
	FONT-SIZE: 91.7px;
	FONT-FAMILY: Times-New-Roman-Bold0125_hod;
	color: rgb(35,31,32);
}

.s5_50{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-_he5;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts50" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0108-_hhx;
	src: url("fonts/Times-New-Roman-Bold0108-_hhx.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0116-_he5;
	src: url("fonts/Times-New-Roman-Bold0116-_he5.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0125_hod;
	src: url("fonts/Times-New-Roman-Bold0125_hod.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_he1;
	src: url("fonts/Times-New-Roman-Bold0100_he1.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_hdz;
	src: url("fonts/Arial-Bold091-625_hdz.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg50Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg50" style="-webkit-user-select: none;"><object width="880" height="1155" data="50/50.svg" type="image/svg+xml" id="pdf50" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_50" class="t s1_50">52</div>
<div id="t2_50" class="t s2_50">VII</div>
<div id="t3_50" class="t s3_50">32.3</div>
<div id="t4_50" class="t s4_50">32.4.4</div>
<div id="t5_50" class="t s5_50">[</div>
<div id="t6_50" class="t s5_50">]</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_51{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_51{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_51{left:222px;top:199px;letter-spacing:0.6px;}
#t4_51{left:159px;top:545px;letter-spacing:0.2px;}
#t5_51{left:89px;top:590px;}
#t6_51{left:618px;top:590px;}

.s1_51{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_hov;
	color: rgb(255,255,255);
}

.s2_51{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_hoz;
	color: rgb(35,31,32);
}

.s3_51{
	FONT-SIZE: 79.4px;
	FONT-FAMILY: Times-New-Roman-Bold0108-_hst;
	color: rgb(255,255,255);
}

.s4_51{
	FONT-SIZE: 91.7px;
	FONT-FAMILY: Times-New-Roman-Bold0125_hz9;
	color: rgb(35,31,32);
}

.s5_51{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-_hp1;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts51" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0108-_hst;
	src: url("fonts/Times-New-Roman-Bold0108-_hst.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0125_hz9;
	src: url("fonts/Times-New-Roman-Bold0125_hz9.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0116-_hp1;
	src: url("fonts/Times-New-Roman-Bold0116-_hp1.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_hov;
	src: url("fonts/Arial-Bold091-625_hov.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_hoz;
	src: url("fonts/Times-New-Roman-Bold0100_hoz.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg51Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg51" style="-webkit-user-select: none;"><object width="880" height="1155" data="51/51.svg" type="image/svg+xml" id="pdf51" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_51" class="t s1_51">53</div>
<div id="t2_51" class="t s2_51">VII</div>
<div id="t3_51" class="t s3_51">32.4</div>
<div id="t4_51" class="t s4_51">32.4.5</div>
<div id="t5_51" class="t s5_51">[</div>
<div id="t6_51" class="t s5_51">]</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_52{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_52{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_52{left:388px;top:102px;letter-spacing:0.6px;}
#t4_52{left:325px;top:354px;letter-spacing:0.2px;}
#t5_52{left:330px;top:399px;}
#t6_52{left:708px;top:399px;}

.s1_52{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_hzr;
	color: rgb(255,255,255);
}

.s2_52{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_hzv;
	color: rgb(35,31,32);
}

.s3_52{
	FONT-SIZE: 79.4px;
	FONT-FAMILY: Times-New-Roman-Bold0108-_i1p;
	color: rgb(255,255,255);
}

.s4_52{
	FONT-SIZE: 91.7px;
	FONT-FAMILY: Times-New-Roman-Bold0125_i85;
	color: rgb(35,31,32);
}

.s5_52{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-_hzz;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts52" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0108-_i1p;
	src: url("fonts/Times-New-Roman-Bold0108-_i1p.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0116-_hzz;
	src: url("fonts/Times-New-Roman-Bold0116-_hzz.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0125_i85;
	src: url("fonts/Times-New-Roman-Bold0125_i85.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_hzv;
	src: url("fonts/Times-New-Roman-Bold0100_hzv.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_hzr;
	src: url("fonts/Arial-Bold091-625_hzr.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg52Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg52" style="-webkit-user-select: none;"><object width="880" height="1155" data="52/52.svg" type="image/svg+xml" id="pdf52" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_52" class="t s1_52">54</div>
<div id="t2_52" class="t s2_52">VII</div>
<div id="t3_52" class="t s3_52">32.5</div>
<div id="t4_52" class="t s4_52">32.4.6</div>
<div id="t5_52" class="t s5_52">[</div>
<div id="t6_52" class="t s5_52">]</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_53{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_53{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_53{left:222px;top:428px;letter-spacing:0.6px;}
#t4_53{left:159px;top:696px;letter-spacing:0.2px;}
#t5_53{left:209px;top:741px;}
#t6_53{left:498px;top:741px;}

.s1_53{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_i8n;
	color: rgb(255,255,255);
}

.s2_53{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_i8r;
	color: rgb(35,31,32);
}

.s3_53{
	FONT-SIZE: 79.4px;
	FONT-FAMILY: Times-New-Roman-Bold0108-_icl;
	color: rgb(255,255,255);
}

.s4_53{
	FONT-SIZE: 91.7px;
	FONT-FAMILY: Times-New-Roman-Bold0125_ij1;
	color: rgb(35,31,32);
}

.s5_53{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-_i8v;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts53" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0116-_i8v;
	src: url("fonts/Times-New-Roman-Bold0116-_i8v.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0108-_icl;
	src: url("fonts/Times-New-Roman-Bold0108-_icl.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_i8n;
	src: url("fonts/Arial-Bold091-625_i8n.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_i8r;
	src: url("fonts/Times-New-Roman-Bold0100_i8r.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0125_ij1;
	src: url("fonts/Times-New-Roman-Bold0125_ij1.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg53Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg53" style="-webkit-user-select: none;"><object width="880" height="1155" data="53/53.svg" type="image/svg+xml" id="pdf53" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_53" class="t s1_53">55</div>
<div id="t2_53" class="t s2_53">VII</div>
<div id="t3_53" class="t s3_53">32.6</div>
<div id="t4_53" class="t s4_53">32.4.7</div>
<div id="t5_53" class="t s5_53">[</div>
<div id="t6_53" class="t s5_53">]</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_54{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_54{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_54{left:388px;top:760px;letter-spacing:0.6px;}

.s1_54{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_ijj;
	color: rgb(255,255,255);
}

.s2_54{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_ijn;
	color: rgb(35,31,32);
}

.s3_54{
	FONT-SIZE: 79.4px;
	FONT-FAMILY: Times-New-Roman-Bold0108-_ind;
	color: rgb(255,255,255);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts54" type="text/css" >

@font-face {
	font-family: Arial-Bold091-625_ijj;
	src: url("fonts/Arial-Bold091-625_ijj.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_ijn;
	src: url("fonts/Times-New-Roman-Bold0100_ijn.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0108-_ind;
	src: url("fonts/Times-New-Roman-Bold0108-_ind.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg54Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg54" style="-webkit-user-select: none;"><object width="880" height="1155" data="54/54.svg" type="image/svg+xml" id="pdf54" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_54" class="t s1_54">56</div>
<div id="t2_54" class="t s2_54">VII</div>
<div id="t3_54" class="t s3_54">32.7</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_55{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_55{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_55{left:159px;top:90px;letter-spacing:0.2px;}
#t4_55{left:162px;top:135px;}
#t5_55{left:545px;top:135px;}
#t6_55{left:222px;top:797px;letter-spacing:0.6px;}

.s1_55{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_iu7;
	color: rgb(255,255,255);
}

.s2_55{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_iub;
	color: rgb(35,31,32);
}

.s3_55{
	FONT-SIZE: 91.7px;
	FONT-FAMILY: Times-New-Roman-Bold0125_j2n;
	color: rgb(35,31,32);
}

.s4_55{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-_iuf;
	color: rgb(35,31,32);
}

.s5_55{
	FONT-SIZE: 79.4px;
	FONT-FAMILY: Times-New-Roman-Bold0108-_iy5;
	color: rgb(255,255,255);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts55" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0100_iub;
	src: url("fonts/Times-New-Roman-Bold0100_iub.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0108-_iy5;
	src: url("fonts/Times-New-Roman-Bold0108-_iy5.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0125_j2n;
	src: url("fonts/Times-New-Roman-Bold0125_j2n.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_iu7;
	src: url("fonts/Arial-Bold091-625_iu7.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0116-_iuf;
	src: url("fonts/Times-New-Roman-Bold0116-_iuf.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg55Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg55" style="-webkit-user-select: none;"><object width="880" height="1155" data="55/55.svg" type="image/svg+xml" id="pdf55" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_55" class="t s1_55">57</div>
<div id="t2_55" class="t s2_55">VII</div>
<div id="t3_55" class="t s3_55">32.4.8</div>
<div id="t4_55" class="t s4_55">[</div>
<div id="t5_55" class="t s4_55">]</div>
<div id="t6_55" class="t s5_55">32.8</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_56{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_56{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_56{left:325px;top:337px;letter-spacing:0.2px;}
#t4_56{left:324px;top:383px;}
#t5_56{left:715px;top:383px;}

.s1_56{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_j33;
	color: rgb(255,255,255);
}

.s2_56{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_j37;
	color: rgb(35,31,32);
}

.s3_56{
	FONT-SIZE: 91.7px;
	FONT-FAMILY: Times-New-Roman-Bold0125_jdf;
	color: rgb(35,31,32);
}

.s4_56{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-_j3b;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts56" type="text/css" >

@font-face {
	font-family: Arial-Bold091-625_j33;
	src: url("fonts/Arial-Bold091-625_j33.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0125_jdf;
	src: url("fonts/Times-New-Roman-Bold0125_jdf.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0116-_j3b;
	src: url("fonts/Times-New-Roman-Bold0116-_j3b.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_j37;
	src: url("fonts/Times-New-Roman-Bold0100_j37.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg56Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg56" style="-webkit-user-select: none;"><object width="880" height="1155" data="56/56.svg" type="image/svg+xml" id="pdf56" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_56" class="t s1_56">58</div>
<div id="t2_56" class="t s2_56">VII</div>
<div id="t3_56" class="t s3_56">32.4.9</div>
<div id="t4_56" class="t s4_56">[</div>
<div id="t5_56" class="t s4_56">]</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_57{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_57{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_57{left:222px;top:199px;letter-spacing:0.6px;}
#t4_57{left:159px;top:421px;letter-spacing:0.2px;}
#t5_57{left:145px;top:466px;}
#t6_57{left:562px;top:466px;}

.s1_57{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_jdw;
	color: rgb(255,255,255);
}

.s2_57{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_jd-;
	color: rgb(35,31,32);
}

.s3_57{
	FONT-SIZE: 79.4px;
	FONT-FAMILY: Times-New-Roman-Bold0108-_jhu;
	color: rgb(255,255,255);
}

.s4_57{
	FONT-SIZE: 91.7px;
	FONT-FAMILY: Times-New-Roman-Bold0125_joa;
	color: rgb(35,31,32);
}

.s5_57{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-_je2;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts57" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0108-_jhu;
	src: url("fonts/Times-New-Roman-Bold0108-_jhu.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0116-_je2;
	src: url("fonts/Times-New-Roman-Bold0116-_je2.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0125_joa;
	src: url("fonts/Times-New-Roman-Bold0125_joa.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_jdw;
	src: url("fonts/Arial-Bold091-625_jdw.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_jd-;
	src: url("fonts/Times-New-Roman-Bold0100_jd-.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg57Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg57" style="-webkit-user-select: none;"><object width="880" height="1155" data="57/57.svg" type="image/svg+xml" id="pdf57" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_57" class="t s1_57">59</div>
<div id="t2_57" class="t s2_57">VII</div>
<div id="t3_57" class="t s3_57">32.9</div>
<div id="t4_57" class="t s4_57">32.4.10</div>
<div id="t5_57" class="t s5_57">[</div>
<div id="t6_57" class="t s5_57">]</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_58{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_58{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_58{left:388px;top:798px;letter-spacing:0.5px;}

.s1_58{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_jos;
	color: rgb(255,255,255);
}

.s2_58{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_jow;
	color: rgb(35,31,32);
}

.s3_58{
	FONT-SIZE: 79.4px;
	FONT-FAMILY: Times-New-Roman-Bold0108-_jsm;
	color: rgb(255,255,255);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts58" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0108-_jsm;
	src: url("fonts/Times-New-Roman-Bold0108-_jsm.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_jos;
	src: url("fonts/Arial-Bold091-625_jos.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_jow;
	src: url("fonts/Times-New-Roman-Bold0100_jow.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg58Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg58" style="-webkit-user-select: none;"><object width="880" height="1155" data="58/58.svg" type="image/svg+xml" id="pdf58" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_58" class="t s1_58">60</div>
<div id="t2_58" class="t s2_58">VII</div>
<div id="t3_58" class="t s3_58">32.10</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_59{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_59{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_59{left:159px;top:167px;letter-spacing:-0.5px;}
#t4_59{left:167px;top:212px;}
#t5_59{left:540px;top:212px;}

.s1_59{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_jzg;
	color: rgb(255,255,255);
}

.s2_59{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_jzk;
	color: rgb(35,31,32);
}

.s3_59{
	FONT-SIZE: 91.7px;
	FONT-FAMILY: Times-New-Roman-Bold0125_k62;
	color: rgb(35,31,32);
}

.s4_59{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-_jzo;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts59" type="text/css" >

@font-face {
	font-family: Arial-Bold091-625_jzg;
	src: url("fonts/Arial-Bold091-625_jzg.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_jzk;
	src: url("fonts/Times-New-Roman-Bold0100_jzk.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0125_k62;
	src: url("fonts/Times-New-Roman-Bold0125_k62.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0116-_jzo;
	src: url("fonts/Times-New-Roman-Bold0116-_jzo.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg59Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg59" style="-webkit-user-select: none;"><object width="880" height="1155" data="59/59.svg" type="image/svg+xml" id="pdf59" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_59" class="t s1_59">61</div>
<div id="t2_59" class="t s2_59">VII</div>
<div id="t3_59" class="t s3_59">32.4.11</div>
<div id="t4_59" class="t s4_59">[</div>
<div id="t5_59" class="t s4_59">]</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_60{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_60{left:134px;top:39px;letter-spacing:-0.2px;}

.s1_60{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_k6j;
	color: rgb(255,255,255);
}

.s2_60{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_k6n;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts60" type="text/css" >

@font-face {
	font-family: Arial-Bold091-625_k6j;
	src: url("fonts/Arial-Bold091-625_k6j.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_k6n;
	src: url("fonts/Times-New-Roman-Bold0100_k6n.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg60Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg60" style="-webkit-user-select: none;"><object width="880" height="1155" data="60/60.svg" type="image/svg+xml" id="pdf60" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_60" class="t s1_60">62</div>
<div id="t2_60" class="t s2_60">VII</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_61{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_61{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_61{left:222px;top:101px;letter-spacing:-0.5px;}
#t4_61{left:159px;top:443px;letter-spacing:0.2px;}
#t5_61{left:95px;top:488px;}
#t6_61{left:612px;top:488px;}

.s1_61{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_ke0;
	color: rgb(255,255,255);
}

.s2_61{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_ke4;
	color: rgb(35,31,32);
}

.s3_61{
	FONT-SIZE: 79.4px;
	FONT-FAMILY: Times-New-Roman-Bold0108-_kh-;
	color: rgb(255,255,255);
}

.s4_61{
	FONT-SIZE: 91.7px;
	FONT-FAMILY: Times-New-Roman-Bold0125_kog;
	color: rgb(35,31,32);
}

.s5_61{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-_ke8;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts61" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0125_kog;
	src: url("fonts/Times-New-Roman-Bold0125_kog.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_ke0;
	src: url("fonts/Arial-Bold091-625_ke0.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_ke4;
	src: url("fonts/Times-New-Roman-Bold0100_ke4.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0108-_kh-;
	src: url("fonts/Times-New-Roman-Bold0108-_kh-.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0116-_ke8;
	src: url("fonts/Times-New-Roman-Bold0116-_ke8.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg61Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg61" style="-webkit-user-select: none;"><object width="880" height="1155" data="61/61.svg" type="image/svg+xml" id="pdf61" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_61" class="t s1_61">63</div>
<div id="t2_61" class="t s2_61">VII</div>
<div id="t3_61" class="t s3_61">32.11</div>
<div id="t4_61" class="t s4_61">32.4.12</div>
<div id="t5_61" class="t s5_61">[</div>
<div id="t6_61" class="t s5_61">]</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_62{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_62{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_62{left:388px;top:103px;letter-spacing:0.5px;}
#t4_62{left:324px;top:372px;letter-spacing:0.2px;}
#t5_62{left:292px;top:417px;}
#t6_62{left:747px;top:417px;}

.s1_62{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_koy;
	color: rgb(255,255,255);
}

.s2_62{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_kp0;
	color: rgb(35,31,32);
}

.s3_62{
	FONT-SIZE: 79.4px;
	FONT-FAMILY: Times-New-Roman-Bold0108-_ksw;
	color: rgb(255,255,255);
}

.s4_62{
	FONT-SIZE: 91.7px;
	FONT-FAMILY: Times-New-Roman-Bold0125_kzc;
	color: rgb(35,31,32);
}

.s5_62{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-_kp4;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts62" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0125_kzc;
	src: url("fonts/Times-New-Roman-Bold0125_kzc.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_koy;
	src: url("fonts/Arial-Bold091-625_koy.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_kp0;
	src: url("fonts/Times-New-Roman-Bold0100_kp0.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0108-_ksw;
	src: url("fonts/Times-New-Roman-Bold0108-_ksw.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0116-_kp4;
	src: url("fonts/Times-New-Roman-Bold0116-_kp4.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg62Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg62" style="-webkit-user-select: none;"><object width="880" height="1155" data="62/62.svg" type="image/svg+xml" id="pdf62" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_62" class="t s1_62">64</div>
<div id="t2_62" class="t s2_62">VII</div>
<div id="t3_62" class="t s3_62">32.12</div>
<div id="t4_62" class="t s4_62">32.4.13</div>
<div id="t5_62" class="t s5_62">[</div>
<div id="t6_62" class="t s5_62">]</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_63{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_63{left:782px;top:37px;letter-spacing:-0.2px;}

.s1_63{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_kzu;
	color: rgb(255,255,255);
}

.s2_63{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_kzy;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts63" type="text/css" >

@font-face {
	font-family: Arial-Bold091-625_kzu;
	src: url("fonts/Arial-Bold091-625_kzu.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_kzy;
	src: url("fonts/Times-New-Roman-Bold0100_kzy.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg63Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg63" style="-webkit-user-select: none;"><object width="880" height="1155" data="63/63.svg" type="image/svg+xml" id="pdf63" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_63" class="t s1_63">65</div>
<div id="t2_63" class="t s2_63">VII</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_64{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_64{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_64{left:388px;top:197px;letter-spacing:0.5px;}
#t4_64{left:324px;top:748px;letter-spacing:0.2px;}
#t5_64{left:295px;top:793px;}
#t6_64{left:743px;top:793px;}
#t7_64{left:644px;top:1032px;}

.s1_64{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_l5b;
	color: rgb(255,255,255);
}

.s2_64{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_l5f;
	color: rgb(35,31,32);
}

.s3_64{
	FONT-SIZE: 79.4px;
	FONT-FAMILY: Times-New-Roman-Bold0108-_l99;
	color: rgb(255,255,255);
}

.s4_64{
	FONT-SIZE: 91.7px;
	FONT-FAMILY: Times-New-Roman-Bold0125_lfv;
	color: rgb(35,31,32);
}

.s5_64{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-_l5j;
	color: rgb(35,31,32);
}

.s6_64{
	FONT-SIZE: 79.4px;
	FONT-FAMILY: Times-New-Roman0108-313_l9d;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts64" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0116-_l5j;
	src: url("fonts/Times-New-Roman-Bold0116-_l5j.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_l5b;
	src: url("fonts/Arial-Bold091-625_l5b.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman0108-313_l9d;
	src: url("fonts/Times-New-Roman0108-313_l9d.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0125_lfv;
	src: url("fonts/Times-New-Roman-Bold0125_lfv.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_l5f;
	src: url("fonts/Times-New-Roman-Bold0100_l5f.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0108-_l99;
	src: url("fonts/Times-New-Roman-Bold0108-_l99.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg64Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg64" style="-webkit-user-select: none;"><object width="880" height="1155" data="64/64.svg" type="image/svg+xml" id="pdf64" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_64" class="t s1_64">66</div>
<div id="t2_64" class="t s2_64">VII</div>
<div id="t3_64" class="t s3_64">32.13</div>
<div id="t4_64" class="t s4_64">32.4.14</div>
<div id="t5_64" class="t s5_64">[</div>
<div id="t6_64" class="t s5_64">]</div>
<div id="t7_64" class="t s6_64">±</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_65{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_65{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_65{left:442px;top:260px;}
#t4_65{left:405px;top:290px;}
#t5_65{left:223px;top:572px;letter-spacing:0.5px;}
#t6_65{left:437px;top:615px;}
#t7_65{left:169px;top:692px;}
#t8_65{left:576px;top:692px;}
#t9_65{left:159px;top:803px;letter-spacing:0.2px;}
#ta_65{left:119px;top:848px;}
#tb_65{left:588px;top:848px;}

.s1_65{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_lgb;
	color: rgb(255,255,255);
}

.s2_65{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_lgf;
	color: rgb(35,31,32);
}

.s3_65{
	FONT-SIZE: 79.4px;
	FONT-FAMILY: Times-New-Roman0108-313_lkd;
	color: rgb(35,31,32);
}

.s4_65{
	FONT-SIZE: 79.4px;
	FONT-FAMILY: Times-New-Roman-Bold0108-_lk9;
	color: rgb(255,255,255);
}

.s5_65{
	FONT-SIZE: 91.7px;
	FONT-FAMILY: Times-New-Roman-Bold0125_lqv;
	color: rgb(35,31,32);
}

.s6_65{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-_lgj;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts65" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0108-_lk9;
	src: url("fonts/Times-New-Roman-Bold0108-_lk9.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_lgb;
	src: url("fonts/Arial-Bold091-625_lgb.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_lgf;
	src: url("fonts/Times-New-Roman-Bold0100_lgf.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman0108-313_lkd;
	src: url("fonts/Times-New-Roman0108-313_lkd.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0125_lqv;
	src: url("fonts/Times-New-Roman-Bold0125_lqv.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0116-_lgj;
	src: url("fonts/Times-New-Roman-Bold0116-_lgj.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg65Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg65" style="-webkit-user-select: none;"><object width="880" height="1155" data="65/65.svg" type="image/svg+xml" id="pdf65" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_65" class="t s1_65">67</div>
<div id="t2_65" class="t s2_65">VII</div>
<div id="t3_65" class="t s3_65">±</div>
<div id="t4_65" class="t s3_65">±</div>
<div id="t5_65" class="t s4_65">32.14</div>
<div id="t6_65" class="t s3_65">±</div>
<div id="t7_65" class="t s3_65">±</div>
<div id="t8_65" class="t s3_65">±</div>
<div id="t9_65" class="t s5_65">32.4.15</div>
<div id="ta_65" class="t s6_65">[</div>
<div id="tb_65" class="t s6_65">]</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_66{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_66{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_66{left:503px;top:153px;}
#t4_66{left:745px;top:192px;}
#t5_66{left:442px;top:251px;}
#t6_66{left:767px;top:251px;}
#t7_66{left:684px;top:379px;}
#t8_66{left:572px;top:577px;}
#t9_66{left:388px;top:722px;letter-spacing:0.5px;}

.s1_66{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_lrb;
	color: rgb(255,255,255);
}

.s2_66{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_lrf;
	color: rgb(35,31,32);
}

.s3_66{
	FONT-SIZE: 79.4px;
	FONT-FAMILY: Times-New-Roman0108-313_lv9;
	color: rgb(35,31,32);
}

.s4_66{
	FONT-SIZE: 79.4px;
	FONT-FAMILY: Times-New-Roman-Bold0108-_lv5;
	color: rgb(255,255,255);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts66" type="text/css" >

@font-face {
	font-family: Times-New-Roman0108-313_lv9;
	src: url("fonts/Times-New-Roman0108-313_lv9.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0108-_lv5;
	src: url("fonts/Times-New-Roman-Bold0108-_lv5.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_lrf;
	src: url("fonts/Times-New-Roman-Bold0100_lrf.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_lrb;
	src: url("fonts/Arial-Bold091-625_lrb.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg66Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg66" style="-webkit-user-select: none;"><object width="880" height="1155" data="66/66.svg" type="image/svg+xml" id="pdf66" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_66" class="t s1_66">68</div>
<div id="t2_66" class="t s2_66">VII</div>
<div id="t3_66" class="t s3_66">±</div>
<div id="t4_66" class="t s3_66">±</div>
<div id="t5_66" class="t s3_66">±</div>
<div id="t6_66" class="t s3_66">±</div>
<div id="t7_66" class="t s3_66">±</div>
<div id="t8_66" class="t s3_66">±</div>
<div id="t9_66" class="t s4_66">32.15</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_67{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_67{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_67{left:159px;top:90px;letter-spacing:0.2px;}
#t4_67{left:169px;top:135px;}
#t5_67{left:538px;top:135px;}
#t6_67{left:491px;top:246px;}
#t7_67{left:529px;top:472px;}
#t8_67{left:289px;top:502px;}
#t9_67{left:281px;top:698px;}
#ta_67{left:257px;top:902px;}

.s1_67{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_m03;
	color: rgb(255,255,255);
}

.s2_67{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_m07;
	color: rgb(35,31,32);
}

.s3_67{
	FONT-SIZE: 91.7px;
	FONT-FAMILY: Times-New-Roman-Bold0125_m8v;
	color: rgb(35,31,32);
}

.s4_67{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-_m0b;
	color: rgb(35,31,32);
}

.s5_67{
	FONT-SIZE: 79.4px;
	FONT-FAMILY: Times-New-Roman0108-313_m41;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts67" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0125_m8v;
	src: url("fonts/Times-New-Roman-Bold0125_m8v.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_m07;
	src: url("fonts/Times-New-Roman-Bold0100_m07.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_m03;
	src: url("fonts/Arial-Bold091-625_m03.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0116-_m0b;
	src: url("fonts/Times-New-Roman-Bold0116-_m0b.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman0108-313_m41;
	src: url("fonts/Times-New-Roman0108-313_m41.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg67Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg67" style="-webkit-user-select: none;"><object width="880" height="1155" data="67/67.svg" type="image/svg+xml" id="pdf67" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_67" class="t s1_67">69</div>
<div id="t2_67" class="t s2_67">VII</div>
<div id="t3_67" class="t s3_67">32.4.16</div>
<div id="t4_67" class="t s4_67">[</div>
<div id="t5_67" class="t s4_67">]</div>
<div id="t6_67" class="t s5_67">±</div>
<div id="t7_67" class="t s5_67">±</div>
<div id="t8_67" class="t s5_67">±</div>
<div id="t9_67" class="t s5_67">±</div>
<div id="ta_67" class="t s5_67">±</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_68{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_68{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_68{left:302px;top:85px;}
#t4_68{left:388px;top:229px;letter-spacing:0.5px;}
#t5_68{left:526px;top:271px;}
#t6_68{left:269px;top:461px;letter-spacing:-0.1px;}
#t7_68{left:264px;top:508px;}
#t8_68{left:264px;top:546px;}
#t9_68{left:264px;top:585px;}
#ta_68{left:264px;top:623px;}
#tb_68{left:272px;top:703px;letter-spacing:0.1px;}

.s1_68{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_m9c;
	color: rgb(255,255,255);
}

.s2_68{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_m9g;
	color: rgb(35,31,32);
}

.s3_68{
	FONT-SIZE: 79.4px;
	FONT-FAMILY: Times-New-Roman0108-313_mdi;
	color: rgb(35,31,32);
}

.s4_68{
	FONT-SIZE: 79.4px;
	FONT-FAMILY: Times-New-Roman-Bold0108-_mda;
	color: rgb(255,255,255);
}

.s5_68{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-_m9k;
	color: rgb(255,255,255);
}

.s6_68{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Times-New-Roman091-625_mde;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts68" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0108-_mda;
	src: url("fonts/Times-New-Roman-Bold0108-_mda.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_m9c;
	src: url("fonts/Arial-Bold091-625_m9c.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman091-625_mde;
	src: url("fonts/Times-New-Roman091-625_mde.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_m9g;
	src: url("fonts/Times-New-Roman-Bold0100_m9g.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman0108-313_mdi;
	src: url("fonts/Times-New-Roman0108-313_mdi.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0116-_m9k;
	src: url("fonts/Times-New-Roman-Bold0116-_m9k.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg68Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg68" style="-webkit-user-select: none;"><object width="880" height="1155" data="68/68.svg" type="image/svg+xml" id="pdf68" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_68" class="t s1_68">70</div>
<div id="t2_68" class="t s2_68">VII</div>
<div id="t3_68" class="t s3_68">±</div>
<div id="t4_68" class="t s4_68">32.16</div>
<div id="t5_68" class="t s3_68">±</div>
<div id="t6_68" class="t s5_68">32.5</div>
<div id="t7_68" class="t s6_68">1.</div>
<div id="t8_68" class="t s6_68">2.</div>
<div id="t9_68" class="t s6_68">3.</div>
<div id="ta_68" class="t s6_68">4.</div>
<div id="tb_68" class="t s5_68">32.6 </div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_69{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_69{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_69{left:107px;top:101px;letter-spacing:0.1px;}
#t4_69{left:55px;top:145px;letter-spacing:-0.2px;}
#t5_69{left:55px;top:299px;letter-spacing:-0.2px;}
#t6_69{left:55px;top:452px;letter-spacing:-0.2px;}
#t7_69{left:55px;top:635px;letter-spacing:-0.2px;}
#t8_69{left:55px;top:819px;letter-spacing:-0.2px;}
#t9_69{left:55px;top:972px;letter-spacing:-0.2px;}

.s1_69{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_mkg;
	color: rgb(255,255,255);
}

.s2_69{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_mkk;
	color: rgb(35,31,32);
}

.s3_69{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-_mko;
	color: rgb(255,255,255);
}

.s4_69{
	FONT-SIZE: 91.7px;
	FONT-FAMILY: Times-New-Roman-Bold0125_mus;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts69" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0116-_mko;
	src: url("fonts/Times-New-Roman-Bold0116-_mko.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_mkg;
	src: url("fonts/Arial-Bold091-625_mkg.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0125_mus;
	src: url("fonts/Times-New-Roman-Bold0125_mus.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_mkk;
	src: url("fonts/Times-New-Roman-Bold0100_mkk.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg69Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg69" style="-webkit-user-select: none;"><object width="880" height="1155" data="69/69.svg" type="image/svg+xml" id="pdf69" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_69" class="t s1_69">71</div>
<div id="t2_69" class="t s2_69">VII</div>
<div id="t3_69" class="t s3_69">32.7</div>
<div id="t4_69" class="t s4_69">32.1</div>
<div id="t5_69" class="t s4_69">32.2</div>
<div id="t6_69" class="t s4_69">32.3</div>
<div id="t7_69" class="t s4_69">32.4</div>
<div id="t8_69" class="t s4_69">32.5</div>
<div id="t9_69" class="t s4_69">32.6</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_70{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_70{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_70{left:221px;top:175px;letter-spacing:-0.2px;}
#t4_70{left:221px;top:388px;letter-spacing:-0.2px;}
#t5_70{left:221px;top:575px;letter-spacing:-0.2px;}
#t6_70{left:221px;top:699px;letter-spacing:-0.3px;}
#t7_70{left:221px;top:823px;letter-spacing:-1.4px;}
#t8_70{left:221px;top:1006px;letter-spacing:-0.3px;}

.s1_70{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_mv8;
	color: rgb(255,255,255);
}

.s2_70{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_mvc;
	color: rgb(35,31,32);
}

.s3_70{
	FONT-SIZE: 91.7px;
	FONT-FAMILY: Times-New-Roman-Bold0125_n22;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts70" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0100_mvc;
	src: url("fonts/Times-New-Roman-Bold0100_mvc.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_mv8;
	src: url("fonts/Arial-Bold091-625_mv8.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0125_n22;
	src: url("fonts/Times-New-Roman-Bold0125_n22.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg70Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg70" style="-webkit-user-select: none;"><object width="880" height="1155" data="70/70.svg" type="image/svg+xml" id="pdf70" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_70" class="t s1_70">72</div>
<div id="t2_70" class="t s2_70">VII</div>
<div id="t3_70" class="t s3_70">32.7</div>
<div id="t4_70" class="t s3_70">32.8</div>
<div id="t5_70" class="t s3_70">32.9</div>
<div id="t6_70" class="t s3_70">32.10</div>
<div id="t7_70" class="t s3_70">32.11</div>
<div id="t8_70" class="t s3_70">32.12</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_71{left:797px;top:1098px;letter-spacing:5.2px;}
#t2_71{left:782px;top:37px;letter-spacing:-0.2px;}
#t3_71{left:55px;top:234px;letter-spacing:-0.3px;}
#t4_71{left:55px;top:596px;letter-spacing:-0.3px;}
#t5_71{left:55px;top:750px;letter-spacing:-0.3px;}
#t6_71{left:55px;top:873px;letter-spacing:-0.3px;}

.s1_71{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_n2j;
	color: rgb(255,255,255);
}

.s2_71{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_n2n;
	color: rgb(35,31,32);
}

.s3_71{
	FONT-SIZE: 91.7px;
	FONT-FAMILY: Times-New-Roman-Bold0125_nbd;
	color: rgb(35,31,32);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts71" type="text/css" >

@font-face {
	font-family: Arial-Bold091-625_n2j;
	src: url("fonts/Arial-Bold091-625_n2j.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0100_n2n;
	src: url("fonts/Times-New-Roman-Bold0100_n2n.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0125_nbd;
	src: url("fonts/Times-New-Roman-Bold0125_nbd.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg71Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg71" style="-webkit-user-select: none;"><object width="880" height="1155" data="71/71.svg" type="image/svg+xml" id="pdf71" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_71" class="t s1_71">73</div>
<div id="t2_71" class="t s2_71">VII</div>
<div id="t3_71" class="t s3_71">32.13</div>
<div id="t4_71" class="t s3_71">32.14</div>
<div id="t5_71" class="t s3_71">32.15</div>
<div id="t6_71" class="t s3_71">32.16</div>

<!-- End text definitions -->


</div>

<div id="jpedal" style="overflow: hidden; position: relative; background-color: white; width: 880px; height: 1155px;">

<!-- Begin shared CSS values -->
<style class="shared-css" type="text/css" >
.t {
	-webkit-transform-origin: top left;
	-moz-transform-origin: top left;
	-o-transform-origin: top left;
	-ms-transform-origin: top left;
	-webkit-transform: scale(0.25);
	-moz-transform: scale(0.25);
	-o-transform: scale(0.25);
	-ms-transform: scale(0.25);
	z-index: 2;
	position: absolute;
	white-space: pre;
	overflow: visible;
}
</style>
<!-- End shared CSS values -->


<!-- Begin inline CSS -->
<style type="text/css" >

#t1_72{left:59px;top:1098px;letter-spacing:5.3px;}
#t2_72{left:134px;top:39px;letter-spacing:-0.2px;}
#t3_72{left:225px;top:91px;letter-spacing:0.1px;}
#t4_72{left:225px;top:887px;letter-spacing:-0.1px;}

.s1_72{
	FONT-SIZE: 67.2px;
	FONT-FAMILY: Arial-Bold091-625_nbu;
	color: rgb(255,255,255);
}

.s2_72{
	FONT-SIZE: 73.3px;
	FONT-FAMILY: Times-New-Roman-Bold0100_nby;
	color: rgb(35,31,32);
}

.s3_72{
	FONT-SIZE: 85.6px;
	FONT-FAMILY: Times-New-Roman-Bold0116-_nc0;
	color: rgb(255,255,255);
}

</style>
<!-- End inline CSS -->

<!-- Begin embedded font definitions -->
<style id="fonts72" type="text/css" >

@font-face {
	font-family: Times-New-Roman-Bold0100_nby;
	src: url("fonts/Times-New-Roman-Bold0100_nby.woff") format("woff");
}

@font-face {
	font-family: Times-New-Roman-Bold0116-_nc0;
	src: url("fonts/Times-New-Roman-Bold0116-_nc0.woff") format("woff");
}

@font-face {
	font-family: Arial-Bold091-625_nbu;
	src: url("fonts/Arial-Bold091-625_nbu.woff") format("woff");
}

</style>
<!-- End embedded font definitions -->

<!-- Begin page background -->
<div id="pg72Overlay" style="width:100%; height:100%; position:absolute; z-index:1; background-color:rgba(0,0,0,0); -webkit-user-select: none;"></div>
<div id="pg72" style="-webkit-user-select: none;"><object width="880" height="1155" data="72/72.svg" type="image/svg+xml" id="pdf72" style="width:880px; height:1155px; -moz-transform:scale(1); z-index: 0;"></object></div>
<!-- End page background -->


<!-- Begin text definitions (Positioned/styled in CSS) -->
<div id="t1_72" class="t s1_72">74</div>
<div id="t2_72" class="t s2_72">VII</div>
<div id="t3_72" class="t s3_72">32.8</div>
<div id="t4_72" class="t s3_72">32.9</div>

<!-- End text definitions -->


</div>
