@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:bold;
	src : url("../Fonts/wcb.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:normal;
	src : url("../Fonts/wcn.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}

html, body {
	font-family:"Walkman-Chanakya-905";
}

body {
font-size:120%;
line-height:150%;
padding:2%;
text-align:justify;
}

* {
margin:0;
padding:0;
}
p
{
	text-align:justify;
}
h4
{
color:white;
font-size:1.5em;
background:#ce1337;
padding:10px;
}
h2
{
color:black;
font-size:1.5em;

}
/* Concept Heading */
.ConceptHeading, .chapterheading, .subHeading
{
color:#ce1337;
font-size:1.3em;
font-weight:bold;
margin-top:20px;
}
/* Sub Heading */
.SubHeading, .topicheading, .chapterNumber
{
color:black;
font-size:1.1em;
font-weight:bold;
margin-top:40px;
}
/* Sub Heading 2*/
.SubHeading2
{
color:black;
font-size:1em;
font-weight:bold;
}
.lining_box
{
border:1px solid black;
padding:5px;
}


.center {
	text-align: center;
}

.excercise, .orangishyellowbox {
text-transform:uppercase;
font-weight:bold;
color:black;
background-color:#FF9900;
padding:15px;
}
.box2, .purplebox{
background-color:#C3C3E5;
padding:15px;
font-size:0.9em;
}
.lightpeach{
background-color:#EFEBD6;
padding:15px;
font-size:0.9em;
}
.lightblue{
background-color:#C1DAD6;
padding:15px;
font-size:0.9em;
}
.lightpurple{
background-color:#ECE0F8;
padding:15px;
font-size:0.9em;
}
.seagreen{
background-color:#DCF0F7;
padding:15px;
font-size:0.9em;
}
.lightmagenta{
background-color:#D5A4D9;
padding:15px;
font-size:0.9em;
}
.brown{
background-color:#D2AF95;
padding:15px;
font-size:0.9em;
}
.lightgreen{
background-color:#9CCCBD;
padding:15px;
font-size:0.9em;
}

.ques
{
text-transform:uppercase;
font-weight:bold;
color:black;
}
.silverbox{
background-color:#CCCCCC;
padding:15px;
font-size:0.9em;
}
.box1, .bluebox{
background-color:#ACD1E9;
padding:15px;
font-size:0.9em;
}

.activityBox, .peachbox{
background-color:rgba(252, 187, 118, 0.4);
padding: 15px;
font-size:0.9em;
}
.newWordsBox
{background-color:#F3E2A9;
padding: 15px;
font-size:0.9em;
}

.greenbox{
background-color:#CEDFCE;
padding: 15px;
font-size:0.9em;
}
.source, .lightpinkishbox, .box{
background-color:#FFCCCB;
padding: 15px;
font-size:0.9em;
}

.pinkbox{
background-color:rgba(206, 19, 55, 0.4);
padding: 15px;
font-size:0.9em;
}


.image {
text-align:center;
}
.author {
text-align:right;
}

.chapterHeading {
font-size:160%;
color: gray;
margin-bottom:20px;
}

.chapterNumber {
	font-size: 125%;
}

.subHeading {
color:#ce1337;
font-size:125%;
}

.center {
	text-align: center;
}

.excercise {

font-weight:bold;
margin:1% 0%;
}

.box{
background-color:rgba(3, 78, 162, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}

.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}

.newWordsBox{
background-color:rgba(252, 187, 118, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}
img
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:50%;
}
.img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:auto;
}
ul
{
	margin-left:45px;
}
.caption
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
	text-align:center;
}
p
{
	margin-top:10px;
}

.footer
{
	display:none;
}
table td
{
	padding:10px;
}
.conc
{
	color:#006699;
}
.englishMeaning, .english
{
	font-family:arial;
	font-size:0.8em;
}
.right
{
	display:inline;
	float:right;
	clear:both;
}
.bold
{
	font-size:115%;
	font-family: Walkman-Chanakya-905;
		font-weight:bold;
}
.italic
{
	font-weight:bold;
	font-size:100%;
	color:#03C;
}
.center
{
	text-align:center;
}
.right
{
	text-align:right;
}
.background
{
	background:#999;
	font-weight:bold;
	
}
.superscript{
position:relative;
top:-15%;
font-size: 85%;
font-family:Arial, Helvetica, sans-serif;
}

.subscript{
position:relative;
bottom:-25%;
font-size: 85%;
font-family:Arial, Helvetica, sans-serif;
}
.work
{
	font-size:105% ;
}
div.layout
{
  text-align: center;
}
div.chapter_pos
{
  text-align: center;
  width: 95%;
  position:absolute;
	top:50%;
	font-weight:bold;
	font-size:28px;
	color:#fff;
}
div.chapter_pos div
{
	background:#266A2E;
	padding:10px;
	width:30%;
	margin:auto;
opacity:0.9;
}
div.chapter_pos div span
{
	font-size:0.7em;
	color:#aeaeae;
	font-weight:normal;
}
.cover_img_small
{
width:50%;
}
@media only screen and (max-width: 767px) {

div.chapter_pos

{
top:50%;
font-size:1em;
}
div.chapter_pos div

{
width:70%;
}
.cover_img_small
{
width:90%;
}
}
#prelims .para-style-override-19, #prelims .para-style-override-22
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color: rgb(236, 0, 140); 
	font-size: 1.67em; 
	font-weight: bold;
}
#prelims .char-style-override-3
{
	font-style:italic;
}
#prelims .subheading
{
	
	color:#b55414;
}
.underline_txt
{
font-decoration:underline;
}
.bold_txt
{
font-weight:bold;
}
.center_element
{
margin:auto;
}
.italics_txt
{
font-style:italic;
}
.block_element
{
display:block;
}
.img_rt
{
float:right;
clear:both;
}
.img_lft
{
float:left;
}