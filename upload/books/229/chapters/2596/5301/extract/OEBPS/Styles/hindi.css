@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:bold;
	src : url("../Fonts/wcb.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:normal;
	src : url("../Fonts/wcn.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}

html, body {
	font-family:"Walkman-Chanakya-905";
}

body {
	font-size:120%;
	line-height:150%;
	padding:2%;
	text-align:justify;
}

.chapterHeading {
	text-align:left;
	font-size:170%;
	
	color:#000;
	
}

.chapterNumber {
	font-size:120% ;
	font-weight:bold;
	text-align:left;
}



.meaning {
	font-size:95%;
}

.image {
	text-align:center;
}

.activity {
	background: #e1def0;
	padding:2%;
}

.englishMeaning{
	font-family:Arial, Helvetica, sans-serif;
	font-size:80%;
}
.bold
{
	
	font-family: Walkman-Chanakya-905;
		font-weight:bold;
}
.italic
{
	font-weight:bold;
	font-size:100%;
	color:#03C;
}
.center
{
	text-align:center;
}
.right
{
	text-align:right;
}
.background
{
	background:#999;
	font-weight:bold;
	
}
.superscript{
position:relative;
top:-15%;
font-size: 85%;
font-family:Arial, Helvetica, sans-serif;
}

.subscript{
position:relative;
bottom:-25%;
font-size: 85%;
font-family:Arial, Helvetica, sans-serif;
}
.work
{
	font-size:105% ;
}
div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 96%;

position:absolute;

top:10%;
line-height:110%;
font-weight:bold;

font-size:180%;

color:#fff;

}

div.chapter_pos div

{

background:#795227;

padding:10px;

width:40%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
.activitybox2{
background-color:#F4A460;
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}

img
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:50%;
}
.img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:80%;
}
ul

{

margin-left:45px;

}

.caption

{

font-style: italic;

font-size: 0.83em;

color: #4D4D4D;

text-align:center;

}

p

{

margin-top:10px;

}

/* Chapter number */
h4
{
color:#d1640f;
font-size:1.3em;
}

.SubSubHeading2
{
color:#663300;
font-size:1.1em;
font-weight:bold;
}

.SubHeading
{
color:#800000;
font-size:1.3em;
font-weight:bold;
}
.boxhead
{
color:#C4994F;
font-size:1.2em;
font-weight:bold;
}

.lining_box1
{
border:2px solid #800000;
padding:15px;
border-radius:15px;

}

h2

{
color:#FFFFFF;
font-size:1.5em;
padding:10px;
}

.SubHeading1
{
color:#909;
font-size:1.1em;
font-weight:bold;
}

.lining_box
{
border:2px solid #C4994F;
padding:15px;
border-radius:15px;
}

.box
{
background-color:rgba(3, 78, 162, 0.4);
padding:15px
}

.footer

{

display:none;

}

table td

{

padding:10px;

}
.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}


@media only screen and (max-width: 767px) {

div.chapter_pos

{

text-align: center;
position:absolute;

top:40%;
line-height:100%;
font-weight:bold;

font-size:0.8em;

color:#fff;

}

div.chapter_pos div span

{

font-size:0.5em;

color:#eaeaea;

font-weight:normal;

}
   .infoBox {
	width: 50%;
	margin: 2% auto;
	padding: 2%;
    border:4px solid #C4C5C8; 
}

}
#prelims .para-style-override-17
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color: rgb(236, 0, 140); 
	font-size: 1.67em; 
	font-weight: bold;
}
#prelims .char-style-override-2
{
	font-style:italic;
}
#prelims .subheading
{
	
	color:#b55414;
}
.cover_img_small
{
width:50%;
}
@media only screen and (max-width: 767px) {
div.chapter_pos
{
top:10%;
font-size:1em;
}
div.chapter_pos div
{
width:70%;
}
.cover_img_small
{
width:90%;
}