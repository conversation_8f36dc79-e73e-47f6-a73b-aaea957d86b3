
body {
font-size:120%;
line-height:150%;
padding:2%;
text-align:justify;
font-family:"Walkman-Chanakya-905";
}
* {
margin:0;
padding:0;
}
@font-face {

font-family:"Walkman-Chanakya-905";

font-style:normal;

font-weight:bold;

src : url("../Fonts/wcb.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:italic;

font-weight:bold;

src : url("../Fonts/wcbi.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:oblique;

font-weight:bold;

src : url("../Fonts/wcbi.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:normal;

font-weight:normal;

src : url("../Fonts/wcn.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:italic;

font-weight:normal;

src : url("../Fonts/wcni.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:oblique;

font-weight:normal;

src : url("../Fonts/wcni.ttf");

}
.Hindi-Runing-Text
{
     font-family: "Walkman-Chanakya-905";
	font-size:120%;
	line-height:150%;
}
body#hindi .char-style-override-1
{
	font-family:arial;
}
/* Hightlisght Boxes */
.Box-Matter1{
background-color:rgb(242,228,206);
padding: 15px;
font-size:0.9em;
line-height:150%;
}
.akd-1, .Box-Matter, .Mid-day-meal{
background-color:rgb(242,228,206);
padding: 15px;
line-height:150%;
border-top:2px solid rgb(201,90,55);
}
.brown{
color:#B54934;
font-size:1.5em;
}

.caption, .bold-and-italic-hindi, .Fig-caption
{
	font-style: italic; 
	font-size: 0.83em; 
	color:rgb(41,187,242);
font-weight:bold;
}
.CharOverride-4{

}

.englishMeaning, .CharOverride-26, .CharOverride-50, .CharOverride-33, .CharOverride-9, .CharOverride-16, .CharOverride-23, .CharOverride-6, .CharOverride-27, .CharOverride-10, .CharOverride-13, .CharOverride-8
{
	font-family:arial;
}

/* Chapter Name */
h1
{
padding:10px;
color:#000;
font-size: 1.5em; 
}

.sub, .CharOverride-53 {
    vertical-align: sub;
    font-size: smaller;
}
.sup, .CharOverride-30 {
    vertical-align: super;
    font-size: smaller;
}

.shadowbox
{
	border:2px solid #000;
	box-shadow: 8px 8px #CCCDCE;
	padding:6px;
}

.borderbox
{
	padding:8px;
    background:#EBECEC;
}

.textmsg
{
	color:#00B8F1;
}

#textmsgbox
{
	color:#fff;
	background:#00B8F1;
	font-size:22px;
	padding:2px;
}


.borderbox1
{
	padding:8px;
    background:#A9E8FA;
}


.borderbox2
{
	padding:8px;
    background:#F2FBFE;
	border: 2px solid #A9DDF4;
	border-radius:8px;
}

.borderboxround
{
	border:2px solid #74767A;
	padding:8px;
	border-radius:15px;
}

h2
{
color:#fff;
font-size: 1.8em; 
padding:4px;
}
h4
{
color:  rgb(0, 157, 221);
font-weight:bold;
font-size: 1.1em; 
}
.CharOverride-12
{

}
.yellow_box
{
background:#FFE196 ;
padding:10px;
border: 1px solid #FFB752;
border-radius: 5px 20px;

}
.green_box
{
background:#ECEB94 ;
padding:10px;
border: 1px solid #C7DC66;
border-radius: 5px 20px;

}
.skin_box
{
background:#F4E8DB;
padding:10px;
}
.blue_box
{
background:#8EC7E9 ;
padding:10px;
}
.pink_box
{
background:#DEC2D2 ;
padding:15px;
}
.pink_line
{
border: 3px solid #DEC2D2;
padding:15px;
}
.Box-design
{
position:relative;
}
.yellow-up
{
color:#fff;
background:#95CB45;
display:inline;
position:absolute;
top:-30px;
padding:5px;
border:1px solid #95CB45;
border-radius: 5px 20px;
}
.green-up
{
color:#fff;
background:#FF2D16;
display:inline;
position:absolute;
top:-30px;
padding:5px;
border:1px solid #FF2D16;
border-radius: 5px 20px;
}
.blue-up
{
color:#fff;
background:#FF744B;
display:inline;
position:absolute;
top:-30px;
padding:5px;
border:1px solid #FF744B;
border-radius: 5px 20px;
}
.white_box{
background-color:#fff;
padding: 15px;
box-shadow: 0px 5px 10px #888888;
border:1px solid #F5F5F5;
width:90%;
margin:auto;
}
.exercise
{
color:#000;
background:#FFC734;
padding:5px;
border:1px solid #000;
border-radius: 5px 20px;
width:30%;
margin:auto;
font-size:1.3em;
}

.box12
{
background: #FEE3AC;
border: 10px double #FFF200 ;
padding:10px;
background-clip: padding-box;
margin:auto;
}

.keyword
{
background:#FAB289 ;
border:5px solid #99DFF9 ;
border-radius: 20px;
padding:10px;
width:20%;
margin:auto;
}
.pink
{
color: rgb(236, 0, 140);
}
.abhyas{
color:#fff;
background:#D7B3C8;
padding:5px; 
}
.yellow
{
background:#FFFCB3 ;
margin:auto;
padding:10px;
width:20%;
}
.blue{
background:#8ECCEC ;
margin:auto;
padding:10px;
width:20%;
}
.box1{
background-color:#FAC968;
padding: 15px;
line-height:150%;

border-top:6px solid #ED1C24;
border-bottom:6px solid #ED1C24;

}
.dot_box
{
border:3px dotted #0590C9 ;
padding:20px;

}
.img_lft
{
float:left;
margin: 10px;
}
.img_ryt
{
float: right;
margin: 10px;
}
.purple
{
background:#E8C8E7;
color:#A0138E;
padding:10px;
font-weight:bold;
font-size:1.3em;
}
.myimg
{
border: 1px solid;
}

/* Chapter number */
h3
{
color:#009DDD;
font-size:1.3em;
font-weight:bold;
}
/* Concept Heading */
.ConceptHeading, .Heading, .Heading-hindi, .Example-style,  .kyun, .Box-item-Heading
{
color:#333;
font-size:1.3em;
font-weight:bold;
margin-top:20px;
}
/* Sub Heading */
.SubHeading, .Heading-2-hindi, .box-text-heading-hindi, .Box-text-heading-style, .Heading-2, .Heading-1, .subhead
{
color:#0094D9;
font-size:1.3em;
font-weight:bold;
margin-top:20px;
}
/* Sub Heading 2*/
.SubHeading2, .Sub-Heading, .CharOverride-38
{
color:#0094D9;
font-size:1.1em;
font-weight:bold;
}
.
{
vertical-align:sub;
font-size:smaller;
}

{
vertical-align: super;
font-size:smaller;
}
.lining_box
{
border:2px solid #000;
border-radius:5px;
padding:10px;
}
.lining_box3
{
border:2px solid #000;
padding:10px;
padding:10px;
}


.tableclass
{
 border:1px solid #000;
 box-shadow: 5px 5px #CCCDCE;
 padding:10px;
}


.lining_box1
{
border-top:2px solid rgb(115,54,158);
border-bottom:2px solid rgb(115,54,158);
border-left:2px solid #fff;
border-right:2px solid #fff;
}
.lining_box2
{
border:2px solid #000;
border-radius:5px;
padding:15px;
}
p
{
	margin-top:10px;
}
.CharOverride-18
{
font-weight:bold;
}

.englishMeaning, .CharOverride-11
{
	font-family:arial;
font-size:0.8em;
}

.char-style-override-25, .char-style-override-26
{
	
}
.CharOverride-32
{
color:#00B8F1;
font-weight:bold;
}
table  , table td, table th
{
		border-collapse: collapse;
		border:1px solid #000;
	padding:5px;
	//background:#fff;
margin:auto;
}

  p.resize img, .resize img
{

position:relative;
top:20px;
}
p.resize1 img, .resize1 img
{
position:relative;
top:15px;
}
p.resize2 img, .resize2 img, img.resize2
{

position:relative;
top:15px;
}
p.resize3 img, .resize3 img
{
height:75px;
position:relative;
top:25px;
} 
li
{
	margin-left:35px;
}

.color-hindi
{
	color:#41924B;
}
.Italic
{
	font-style:italic;
}
.activity-style, .Activity-Heading
{
	color:#000;
	font-weight:bold;
	font-size:1.1em;
text-transform:uppercase;
}
table td.Cell-Style-Head-english
{
	background:#fff;
	color:#fff;
	font-weight:bold;
}
.clear
{
	clear:both;
}
.golden
{
	color:#706627;
	font-weight:bold;
}
img
{
margin-left: auto;
margin-right: auto;
max-width:90%;
margin:5px;
}
.cover_img_small
{
width:50%;
}
div.layout

{

text-align: center;

}

div.chapter_pos


{


text-align: center;

margin-left:3%;

width: 90%;


position:absolute;


top:60%;


font-weight:bold;


font-size:28px;


color:#000;


}


div.chapter_pos div


{


background:#EEF887;


padding:10px;


width:40%;


margin:auto;

opacity:0.9;


}


div.chapter_pos div span


{


font-size:0.7em;


color:#000;


font-weight:normal;


}

@media only screen and (max-width: 767px) {


div.chapter_pos


{


font-size:0.8em;

line-height:120%;

top:50%;

}


div.chapter_pos div span


{


font-size:0.7em;


}

}

