body {
font-size:120%;
line-height:150%;
padding:2%;
text-align:justify;
font-family:"Walkman-Chanakya-905";

font-style:normal;


}
* {
margin:0;
padding:0;
}
@font-face {

font-family:"Walkman-Chanakya-905";

font-style:normal;

font-weight:bold;

src : url("../Fonts/wcb.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:italic;

font-weight:bold;

src : url("../Fonts/wcbi.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:oblique;

font-weight:bold;

src : url("../Fonts/wcbi.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:normal;

font-weight:normal;

src : url("../Fonts/wcn.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:italic;

font-weight:normal;

src : url("../Fonts/wcni.ttf");

}

@font-face {

font-family:"Walkman-Chanakya-905";

font-style:oblique;

font-weight:normal;

src : url("../Fonts/wcni.ttf");

}
body#hindi, div#hindi, body#eng-hin .hindi-chracter-style
{
font-family: "Walkman-Chanakya-905";
font-size:120%;
line-height:150%;
}
body#hindi .char-style-override-1
{
font-family:arial;
}
/* Hightlisght Boxes */
.box, .akd-1{
background-color:rgb(234,241,202);
padding: 15px;
font-size:0.9em;
line-height:150%;
}
img
{
margin-left: auto;
margin-right: auto;
max-width:70%;
margin:5px;
}
h4, .caption
{
font-style: italic;
font-size: 0.83em;
color: #4D4D4D;
}
/* Chapter Name */
.abc{
color:#00B8F1;
font-size:1.5em

}
h1
{
color:#AF2A20;
font-size: 1.3em;
background:#FFB974;
padding:5px;

}

.header
{
color:#A84320;
font-size: 1.5em;
background:#A1DDEF;
padding:5px;
border:4px solid #E0E0E1;
}
h2
{
color:#33c1ff ;
font-size: 1.3em;
}
.font
{
color:#33c1ff ;
font-size: 1.3em;
}
h5
{
padding:5px;
background:#E4B3C7 ;
font-size: 1.3em;
}
h6{
color:#33c1ff;
font-weight:bold;
font-size: 1.1em;
}
/* Chapter number */
h3
{
color: #AF2A20;;
font-size: 1.3em;
}
.CharOverride-7{

color:#00B8F1;
font-weight:bold;
}
.CharOverride-9{
color:#00B8F1;

}
.CharOverride-38{
color:#00B8F1;
}
/* Concept Heading */
.ConceptHeading, .Heading, .Heading-hindi, .Example-style
{
color:rgb(54,182,75);
font-size:1.3em;
font-weight:bold;
margin-top:20px;
}
/* Sub Heading */
.SubHeading, .Heading-2-hindi, .box-text-heading-hindi, .Heading-2, .Box-text-heading-style
{
color:rgb(54,182,75);
font-size:1em;
font-weight:bold;

}
/* Sub Heading 2*/
.SubHeading2
{
color:#CC0000;
font-size:1.1em;
font-weight:bold;
}
.lining_box
{
border:2px solid #c11667 ;
padding:15px;
border-radius:15px;
background: #f6bed9 ;
}
.lining_box6
{
border:4px solid #A5D04A;
padding:15px;
border-radius:15px;

}
.lining_box7
{
border:4px solid #00B8F1;
padding:15px;
border-radius:15px;

}
.color
{

padding:15px;
color:#FF7B29;

}
.color1
{

padding:15px;
color:#D0247F;

}


.lining_box4
{
border:4px solid #FF5872 ;
padding:15px;
border-radius:15px;

}
.lining_box3
{
border:4px solid #E0A2CB ;
padding:15px;
border-radius:15px;

}
.lining_box1
{
border:4px solid #FFC734 ;
padding:15px;
border-radius:15px;

}
.lining_box2
{
border:2px solid #c11667 ;
padding:15px;
border-radius:15px;
background: #D6DFD4 ;
}
.lining_box5
{
border:2px solid #c11667 ;
padding:15px;
border-radius:15px;
background:#EBD498 ;
}
.img_ryt
{
float:right;
}
.img_lft
{
float:left;
}

p
{
margin-top:10px;
}
.bold, .bold-hinid, .Example-head-2, .heading-3, .bold---italic-english
{
font-weight:bold;
color:#35B8F1;
}
.bold---italic-english
{
font-style:italic;
}
.Colour-Bullets
{
font-size:70%;
color:rgb(96,145,96);
}

.englishMeaning
{
font-family:arial;
font-size:0.8em;
}
.char-style-override-25, .char-style-override-26
{
COLOR:rgb(251,53,155);
}
table, table td
{
border-collapse: collapse;
border:1px solid #A3D4B7;
padding:5px;
}
li
{
margin-left:35px;
}
.Box-text-style-bullet-2
{
margin-left:60px;
}
.color-hindi
{
color:#41924B;
}
.Italic
{
font-style:italic;
}
.activity-style
{
color:#706627;
font-weight:bold;
font-size:1.3em;
}
table td.Cell-Style-Head-english
{
background:#72BF92;
color:#fff;
font-weight:bold;
}
.clear
{
clear:both;
}
.golden
{
color:#706627;
font-weight:bold;
}
.cover_img_small

{
width:50%;
}
@media only screen and (max-width: 767px) {
.cover_img_small
{
width:90%;
}
}
