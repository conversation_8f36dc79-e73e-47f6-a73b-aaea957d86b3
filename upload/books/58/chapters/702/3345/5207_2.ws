<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title>Cover</title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000001.jpg');"> </div>
    <div class="pos fs0" style="left: 193px; top: 197px; color: #221F1F;">2</div>
    <div class="pos fs1" style="left: 294px; top: 175px; color: #221F1F;">Fun with Numbers</div>
    <div class="pos fs2 " style="left: 161px; top: 944px; color: #221F1F;">
      <div class="just">Radhika, Gauri, Vicky, Indra and Sunil were collecting<span style="font-family: 'akoeko_bookman_lightitalic';"> Imli</span></div>
    </div>
    <div class="pos fs2" style="left: 161px; top: 983px; color: #221F1F;">(tamarind) seeds.</div>
    <div class="pos fs4" style="left: 161px; top: 1036px; color: #EB008B;">✤</div>
    <div class="pos fs2" style="left: 296px; top: 1033px; color: #221F1F;">collected the most seeds.</div>
    <div class="pos fs2 " style="left: 161px; top: 1082px; color: #221F1F;">
      <div class="just"><span style="color: #EB008B; font-family: 'akoelp_zapfdingbats';">✤</span> Sunil will collect</div>
    </div>
    <div class="pos fs2 " style="left: 161px; top: 1132px; color: #221F1F;">
      <div class="just"><span style="color: #EB008B; font-family: 'akoelp_zapfdingbats';">✤</span> If Radhika gets 6 more seeds, she will have</div>
    </div>
    <div class="pos fs2" style="left: 295px; top: 1232px; color: #221F1F;">needs 3 more seeds to have 50.</div>
    <div class="pos fs2" style="left: 525px; top: 1082px; color: #221F1F;">more seeds to be equal to Vicky.</div>
    <div class="pos fs2" style="left: 873px; top: 1132px; color: #221F1F;">.</div>
    <div class="pos fs2 " style="left: 161px; top: 1182px; color: #221F1F;">
      <div class="just"><span style="color: #EB008B; font-family: 'akoelp_zapfdingbats';">✤</span> How many children have more than 40 seeds?</div>
    </div>
    <div class="pos fs4" style="left: 161px; top: 1235px; color: #EB008B;">✤</div>
    <div class="pos fs4" style="left: 161px; top: 1282px; color: #EB008B;" id="w1x">✤</div>
    <div class="pos fs2" style="left: 196px; top: 1282px; color: #221F1F;" id="w2x">Sunil</div>
    <div class="pos fs2" style="left: 274px; top: 1282px; color: #221F1F;" id="w3x">has</div>
    <div class="pos fs2" style="left: 330px; top: 1282px; color: #221F1F;" id="w4x">2</div>
    <div class="pos fs2" style="left: 355px; top: 1282px; color: #221F1F;" id="w5x">seeds</div>
    <div class="pos fs2" style="left: 436px; top: 1282px; color: #221F1F;" id="w6x">less</div>
    <div class="pos fs2" style="left: 494px; top: 1282px; color: #221F1F;" id="w7x">than</div>
    <div class="pos fs2" style="left: 564px; top: 1282px; color: #221F1F;" id="w8x">40</div>
    <div class="pos fs2" style="left: 606px; top: 1282px; color: #221F1F;" id="w9x">and</div>
    <div class="pos fs2" style="left: 196px; top: 1320px; color: #221F1F;">than 40.</div>
    <div class="pos fs5" style="left: 566px; top: 1407px; color: #221F1F;">13</div>
    <div class="pos fs5" style="left: 567px; top: 1411px; color: #221F1F;">13</div>
    <div class="pos fs2" style="left: 761px; top: 1282px; color: #221F1F;">has 2 seeds more</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000002.jpg');"> </div>
    <div class="pos fs6" style="left: 222px; top: 155px; color: #221F1F;">Dot Game</div>
    <div class="pos fit fs2" style="left: 222px; top: 207px; width: 828px; color: #221F1F;">
      <span class="just">Guess the number of dots in the circle. Now count and check</span>
    </div>
    <div class="pos fit fs2" style="left: 222px; top: 246px; width: 828px; color: #221F1F;">
      <span class="just">your guess. Play this game with your friends by making circles.</span>
    </div>
    <div class="pos fs2" style="left: 222px; top: 284px; color: #221F1F;">See who can guess best.</div>
    <div class="pos fs7" style="left: 505px; top: 360px; color: #221F1F;">Can you guess</div>
    <div class="pos fs7" style="left: 503px; top: 394px; color: #221F1F;">the number of</div>
    <div class="pos fs7" style="left: 511px; top: 429px; color: #221F1F;">dots on me ?</div>
    <div class="pos fs8" style="left: 238px; top: 1225px; color: #221F1F;">Children need interesting exercises to help them with visual estimation of numbers – of things</div>
    <div class="pos fs8" style="left: 238px; top: 1251px; color: #221F1F;">arranged randomly and in symmetrical groups. Teachers could use other instances, such as</div>
    <div class="pos fs8" style="left: 238px; top: 1279px; color: #221F1F;">bundles of leaves sold in the market, the school assembly, designs on mats, etc. to make them</div>
    <div class="pos fs8" style="left: 238px; top: 1305px; color: #221F1F;">guess and estimate different numbers. In this book an ant</div>
    <div class="pos fs8" style="left: 238px; top: 1332px; color: #221F1F;">that a guess or estimate has to be made.</div>
    <div class="pos fs5" style="left: 625px; top: 1412px; color: #221F1F;">14</div>
    <div class="pos fs8" style="left: 238px; top: 1305px; color: #221F1F;">has been used to show the child</div>
    <div class="pos fs7" style="left: 856px; top: 370px; -webkit-transform: rotate(-15.0deg); color: #221F1F;">And on me?</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000003.jpg');"> </div>
    <div class="pos fs6" style="left: 161px; top: 153px; color: #221F1F;">Dhoni's Century</div>
    <div class="pos fs2" style="left: 161px; top: 206px; color: #221F1F;">One-day match between India and South Africa in</div>
    <div class="pos fs2" style="left: 161px; top: 244px; color: #221F1F;">Guwahati......., India batting first......</div>
    <div class="pos fs9" style="left: 834px; top: 521px; color: #221F1F;">Has he completed</div>
    <div class="pos fs9" style="left: 857px; top: 544px; color: #221F1F;">his century?</div>
    <div class="pos fs6" style="left: 163px; top: 791px; color: #221F1F;">Fill in the blanks:</div>
    <div class="pos fs2" style="left: 163px; top: 843px; color: #221F1F;" id="w1x">Dhoni</div>
    <div class="pos fs2" style="left: 252px; top: 843px; color: #221F1F;" id="w2x">scored</div>
    <div class="pos fs2" style="left: 357px; top: 843px; color: #221F1F;" id="w3x">96</div>
    <div class="pos fs2" style="left: 400px; top: 843px; color: #221F1F;" id="w4x">+</div>
    <div class="pos fs2" style="left: 501px; top: 843px; color: #221F1F;">=</div>
    <div class="pos fs2" style="left: 633px; top: 843px; color: #221F1F;">runs.</div>
    <div class="pos fs2" style="left: 163px; top: 893px; color: #221F1F;">How many runs do these players need to complete a century?</div>
    <div class="pos fs10" style="left: 409px; top: 954px; color: #221F1F;">Runs scored</div>
    <div class="pos fs10" style="left: 609px; top: 954px; color: #221F1F;">Runs needed to</div>
    <div class="pos fs10" style="left: 591px; top: 993px; color: #221F1F;">complete a century</div>
    <div class="pos fit fs11" style="left: 282px; top: 1024px; width: 91px; color: #221F1F;">
      <span class="just">Player 1</span>
    </div>
    <div class="pos fit fs11" style="left: 282px; top: 1074px; width: 91px; color: #221F1F;">
      <span class="just">Player 2</span>
    </div>
    <div class="pos fit fs11" style="left: 282px; top: 1124px; width: 91px; color: #221F1F;">
      <span class="just">Player 3</span>
    </div>
    <div class="pos fit fs11" style="left: 282px; top: 1173px; width: 91px; color: #221F1F;">
      <span class="just">Player 4</span>
    </div>
    <div class="pos fs11" style="left: 460px; top: 1024px; color: #221F1F;">93</div>
    <div class="pos fs11" style="left: 460px; top: 1074px; color: #221F1F;">97</div>
    <div class="pos fs11" style="left: 460px; top: 1124px; color: #221F1F;">89</div>
    <div class="pos fs11" style="left: 460px; top: 1173px; color: #221F1F;">99</div>
    <div class="pos fs8" style="left: 184px; top: 1246px; color: #221F1F;">Numbers are understood not by reciting them in order but by making associations in familiar</div>
    <div class="pos fs8" style="left: 184px; top: 1274px; color: #221F1F;">contexts. Here the idea of a "century" of runs is used. Teachers could add other examples from</div>
    <div class="pos fs8" style="left: 184px; top: 1300px; color: #221F1F;">children's lives to think about 3-digit numbers. Encourage them to speak about large numbers</div>
    <div class="pos fs8" style="left: 184px; top: 1327px; color: #221F1F;">even if they cannot read or write them.</div>
    <div class="pos fs5" style="left: 566px; top: 1411px; color: #221F1F;">15</div>
    <div class="pos fs9" style="left: 142px; top: 428px; -webkit-transform: rotate(-30.0deg); color: #221F1F;">Dhoni on 96 ..... Only one</div>
    <div class="pos fit fs9" style="left: 142px; top: 458px; -webkit-transform: rotate(-30.0deg); width: 263px; color: #221F1F;">
      <span class="just">ball left.....will he complete his</span>
    </div>
    <div class="pos fit fs9" style="left: 155px; top: 481px; -webkit-transform: rotate(-30.0deg); width: 263px; color: #221F1F;">
      <span class="just">century?........ And look at this !</span>
    </div>
    <div class="pos fs9" style="left: 170px; top: 507px; -webkit-transform: rotate(-30.0deg); color: #221F1F;">What a marvellous six !</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000004.jpg');"> </div>
    <div class="pos fs12" style="left: 605px; top: 343px; color: #221F1F;">99</div>
    <div class="pos fs13" style="left: 773px; top: 345px; color: #221F1F;">100 101 102</div>
    <div class="pos fs6" style="left: 218px; top: 436px; color: #221F1F;">Fill in the Blanks:</div>
    <div class="pos fs14" style="left: 342px; top: 496px; color: #221F1F;">99-112</div>
    <div class="pos fs10" style="left: 253px; top: 546px; color: #221F1F;">Number</div>
    <div class="pos fs10" style="left: 239px; top: 581px; color: #221F1F;">(in figures)</div>
    <div class="pos fs11" style="left: 268px; top: 644px; color: #221F1F;">99</div>
    <div class="pos fs10" style="left: 435px; top: 546px; color: #221F1F;">Number</div>
    <div class="pos fs10" style="left: 426px; top: 581px; color: #221F1F;">(in words)</div>
    <div class="pos fs11" style="left: 381px; top: 644px; color: #221F1F;">Ninety-nine</div>
    <div class="pos fs11" style="left: 268px; top: 694px; color: #221F1F;" id="w1x">100</div>
    <div class="pos fs11" style="left: 372px; top: 694px; color: #221F1F;" id="w2x">One</div>
    <div class="pos fs11" style="left: 426px; top: 694px; color: #221F1F;" id="w3x">hundred</div>
    <div class="pos fs11" style="left: 268px; top: 744px; color: #221F1F;" id="w4x">101</div>
    <div class="pos fs11" style="left: 372px; top: 744px; color: #221F1F;" id="w5x">One</div>
    <div class="pos fs11" style="left: 425px; top: 744px; color: #221F1F;" id="w6x">hundred</div>
    <div class="pos fs11" style="left: 530px; top: 744px; color: #221F1F;" id="w7x">one</div>
    <div class="pos fs11" style="left: 268px; top: 794px; color: #221F1F;">102</div>
    <div class="pos fs11" style="left: 268px; top: 844px; color: #221F1F;" id="w8x">103</div>
    <div class="pos fs11" style="left: 372px; top: 844px; color: #221F1F;" id="w9x">One</div>
    <div class="pos fs11" style="left: 425px; top: 844px; color: #221F1F;" id="w10x">hundred</div>
    <div class="pos fs11" style="left: 529px; top: 844px; color: #221F1F;" id="w11x">three</div>
    <div class="pos fs11" style="left: 268px; top: 893px; color: #221F1F;" id="w12x">104</div>
    <div class="pos fs11" style="left: 372px; top: 893px; color: #221F1F;" id="w13x">One</div>
    <div class="pos fs11" style="left: 425px; top: 893px; color: #221F1F;" id="w14x">hundred</div>
    <div class="pos fs11" style="left: 530px; top: 893px; color: #221F1F;" id="w15x">four</div>
    <div class="pos fs11" style="left: 372px; top: 943px; color: #221F1F;">One hundred five</div>
    <div class="pos fs11" style="left: 268px; top: 993px; color: #221F1F;" id="w16x">106</div>
    <div class="pos fs11" style="left: 372px; top: 993px; color: #221F1F;" id="w17x">One</div>
    <div class="pos fs11" style="left: 425px; top: 993px; color: #221F1F;" id="w18x">hundred</div>
    <div class="pos fs11" style="left: 530px; top: 993px; color: #221F1F;" id="w19x">six</div>
    <div class="pos fs11" style="left: 268px; top: 1043px; color: #221F1F;">107</div>
    <div class="pos fs11" style="left: 372px; top: 1093px; color: #221F1F;">One hundred eight</div>
    <div class="pos fs11" style="left: 268px; top: 1143px; color: #221F1F;" id="w20x">109</div>
    <div class="pos fs11" style="left: 372px; top: 1143px; color: #221F1F;" id="w21x">One</div>
    <div class="pos fs11" style="left: 426px; top: 1143px; color: #221F1F;" id="w22x">hundred</div>
    <div class="pos fs11" style="left: 531px; top: 1143px; color: #221F1F;" id="w23x">nine</div>
    <div class="pos fs11" style="left: 268px; top: 1192px; color: #221F1F;" id="w24x">110</div>
    <div class="pos fs11" style="left: 372px; top: 1192px; color: #221F1F;" id="w25x">One</div>
    <div class="pos fs11" style="left: 425px; top: 1192px; color: #221F1F;" id="w26x">hundred</div>
    <div class="pos fs11" style="left: 530px; top: 1192px; color: #221F1F;" id="w27x">ten</div>
    <div class="pos fs11" style="left: 268px; top: 1242px; color: #221F1F;" id="w28x">111</div>
    <div class="pos fs11" style="left: 372px; top: 1242px; color: #221F1F;" id="w29x">One</div>
    <div class="pos fs11" style="left: 425px; top: 1242px; color: #221F1F;" id="w30x">hundred</div>
    <div class="pos fs11" style="left: 529px; top: 1242px; color: #221F1F;" id="w31x">eleven</div>
    <div class="pos fit fs11" style="left: 372px; top: 1292px; width: 226px; color: #221F1F;">
      <span class="just">One hundred twelve</span>
    </div>
    <div class="pos fs14" style="left: 722px; top: 496px; color: #221F1F;">195-206</div>
    <div class="pos fs10" style="left: 648px; top: 546px; color: #221F1F;">Number</div>
    <div class="pos fs10" style="left: 635px; top: 581px; color: #221F1F;">(in figures)</div>
    <div class="pos fs10" style="left: 874px; top: 546px; color: #221F1F;">Number</div>
    <div class="pos fs10" style="left: 859px; top: 581px; color: #221F1F;">(in words)</div>
    <div class="pos fs11" style="left: 667px; top: 646px; color: #221F1F;" id="w32x">195</div>
    <div class="pos fs11" style="left: 776px; top: 646px; color: #221F1F;" id="w33x">One</div>
    <div class="pos fs11" style="left: 829px; top: 646px; color: #221F1F;" id="w34x">hundred</div>
    <div class="pos fs11" style="left: 934px; top: 646px; color: #221F1F;" id="w35x">ninety-five</div>
    <div class="pos fs11" style="left: 667px; top: 696px; color: #221F1F;" id="w36x">196</div>
    <div class="pos fs11" style="left: 776px; top: 696px; color: #221F1F;" id="w37x">One</div>
    <div class="pos fs11" style="left: 830px; top: 696px; color: #221F1F;" id="w38x">hundred</div>
    <div class="pos fs11" style="left: 934px; top: 696px; color: #221F1F;" id="w39x">ninety-six</div>
    <div class="pos fs11" style="left: 667px; top: 746px; color: #221F1F;" id="w40x">197</div>
    <div class="pos fs11" style="left: 776px; top: 746px; color: #221F1F;" id="w41x">One</div>
    <div class="pos fs11" style="left: 829px; top: 746px; color: #221F1F;" id="w42x">hundred</div>
    <div class="pos fs11" style="left: 933px; top: 746px; color: #221F1F;" id="w43x">ninety-seven</div>
    <div class="pos fs11" style="left: 667px; top: 795px; color: #221F1F;" id="w44x">198</div>
    <div class="pos fs11" style="left: 776px; top: 795px; color: #221F1F;" id="w45x">One</div>
    <div class="pos fs11" style="left: 830px; top: 795px; color: #221F1F;" id="w46x">hundred</div>
    <div class="pos fs11" style="left: 936px; top: 795px; color: #221F1F;" id="w47x">ninety-eight</div>
    <div class="pos fs11" style="left: 776px; top: 845px; color: #221F1F;">One hundred ninety-nine</div>
    <div class="pos fs11" style="left: 667px; top: 895px; color: #221F1F;" id="w48x">200</div>
    <div class="pos fs11" style="left: 776px; top: 895px; color: #221F1F;" id="w49x">Two</div>
    <div class="pos fs11" style="left: 829px; top: 895px; color: #221F1F;" id="w50x">hundred</div>
    <div class="pos fs11" style="left: 667px; top: 945px; color: #221F1F;" id="w51x">201</div>
    <div class="pos fs11" style="left: 776px; top: 945px; color: #221F1F;" id="w52x">Two</div>
    <div class="pos fs11" style="left: 828px; top: 945px; color: #221F1F;" id="w53x">hundred</div>
    <div class="pos fs11" style="left: 932px; top: 945px; color: #221F1F;" id="w54x">one</div>
    <div class="pos fs11" style="left: 667px; top: 1045px; color: #221F1F;" id="w55x">203</div>
    <div class="pos fs11" style="left: 776px; top: 1045px; color: #221F1F;" id="w56x">Two</div>
    <div class="pos fs11" style="left: 828px; top: 1045px; color: #221F1F;" id="w57x">hundred</div>
    <div class="pos fs11" style="left: 932px; top: 1045px; color: #221F1F;" id="w58x">three</div>
    <div class="pos fs11" style="left: 776px; top: 1094px; color: #221F1F;">Two hundred four</div>
    <div class="pos fs11" style="left: 667px; top: 1144px; color: #221F1F;" id="w59x">205</div>
    <div class="pos fs11" style="left: 776px; top: 1144px; color: #221F1F;" id="w60x">Two</div>
    <div class="pos fs11" style="left: 828px; top: 1144px; color: #221F1F;" id="w61x">hundred</div>
    <div class="pos fs11" style="left: 932px; top: 1144px; color: #221F1F;" id="w62x">five</div>
    <div class="pos fs11" style="left: 667px; top: 1194px; color: #221F1F;">206</div>
    <div class="pos fs7" style="left: 630px; top: 1266px; color: #221F1F;">Oh! 206! Guess how many more</div>
    <div class="pos fs7" style="left: 657px; top: 1305px; color: #221F1F;">to make a triple century?</div>
    <div class="pos fs5" style="left: 625px; top: 1412px; color: #221F1F;">16</div>
    <div class="pos fs7" style="left: 237px; top: 261px; -webkit-transform: rotate(-35.0deg); color: #221F1F;">After 99 we go</div>
    <div class="pos fs7" style="left: 276px; top: 276px; -webkit-transform: rotate(-35.0deg); color: #221F1F;">on like this</div>
    <div class="pos fs7" style="left: 895px; top: 229px; -webkit-transform: rotate(-25.0deg); color: #221F1F;">See you</div>
    <div class="pos fs7" style="left: 900px; top: 260px; -webkit-transform: rotate(-25.0deg); color: #221F1F;">tomorrow</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000005.jpg');"> </div>
    <div class="pos fs6" style="left: 418px; top: 153px; color: #221F1F;">Top Ten Scores in the Cricket World Cup</div>
    <div class="pos fs10" style="left: 281px; top: 465px; color: #221F1F;">Player</div>
    <div class="pos fs11" style="left: 279px; top: 506px; color: #221F1F;">A.P.J.</div>
    <div class="pos fs11" style="left: 280px; top: 562px; color: #221F1F;">A.S.</div>
    <div class="pos fs11" style="left: 272px; top: 618px; color: #221F1F;">C.K.</div>
    <div class="pos fs11" style="left: 272px; top: 673px; color: #221F1F;">D.M.</div>
    <div class="pos fs11" style="left: 272px; top: 729px; color: #221F1F;">K.S.P.</div>
    <div class="pos fs10" style="left: 415px; top: 465px; color: #221F1F;">Score</div>
    <div class="pos fs11" style="left: 427px; top: 506px; color: #221F1F;">128</div>
    <div class="pos fs10" style="left: 656px; top: 463px; color: #221F1F;">Player Score</div>
    <div class="pos fs11" style="left: 659px; top: 504px; color: #221F1F;">M.D.</div>
    <div class="pos fs11" style="left: 427px; top: 562px; color: #221F1F;">100</div>
    <div class="pos fs11" style="left: 442px; top: 618px; color: #221F1F;">99</div>
    <div class="pos fs11" style="left: 427px; top: 673px; color: #221F1F;">162</div>
    <div class="pos fs11" style="left: 428px; top: 729px; color: #221F1F;">152</div>
    <div class="pos fs11" style="left: 659px; top: 560px; color: #221F1F;">P.K.</div>
    <div class="pos fs11" style="left: 659px; top: 616px; color: #221F1F;">S.T.</div>
    <div class="pos fs11" style="left: 659px; top: 671px; color: #221F1F;">T.P.K.</div>
    <div class="pos fs11" style="left: 659px; top: 727px; color: #221F1F;">V.V.S.</div>
    <div class="pos fs11" style="left: 797px; top: 504px; color: #221F1F;">178</div>
    <div class="pos fs11" style="left: 797px; top: 560px; color: #221F1F;">105</div>
    <div class="pos fs11" style="left: 797px; top: 616px; color: #221F1F;">141</div>
    <div class="pos fs11" style="left: 797px; top: 671px; color: #221F1F;">112</div>
    <div class="pos fs11" style="left: 797px; top: 727px; color: #221F1F;">127</div>
    <div class="pos fs4" style="left: 158px; top: 989px; color: #EB008B;" id="w1x">✸</div>
    <div class="pos fs2" style="left: 193px; top: 989px; color: #221F1F;" id="w2x">C.K.</div>
    <div class="pos fs2" style="left: 258px; top: 989px; color: #221F1F;" id="w3x">just</div>
    <div class="pos fs2" style="left: 317px; top: 989px; color: #221F1F;" id="w4x">missed</div>
    <div class="pos fs2" style="left: 418px; top: 989px; color: #221F1F;" id="w5x">his</div>
    <div class="pos fs2" style="left: 466px; top: 989px; color: #221F1F;" id="w6x">century.</div>
    <div class="pos fs2" style="left: 584px; top: 989px; color: #221F1F;" id="w7x">How</div>
    <div class="pos fs2" style="left: 650px; top: 989px; color: #221F1F;" id="w8x">many</div>
    <div class="pos fs2" style="left: 732px; top: 989px; color: #221F1F;" id="w9x">runs</div>
    <div class="pos fs2" style="left: 802px; top: 989px; color: #221F1F;" id="w10x">did</div>
    <div class="pos fs2" style="left: 852px; top: 989px; color: #221F1F;" id="w11x">he</div>
    <div class="pos fs2" style="left: 893px; top: 989px; color: #221F1F;" id="w12x">need</div>
    <div class="pos fs2" style="left: 964px; top: 989px; color: #221F1F;" id="w13x">to</div>
    <div class="pos fs2" style="left: 193px; top: 1027px; color: #221F1F;">make a century?</div>
    <div class="pos fs4" style="left: 158px; top: 1080px; color: #EB008B;">✸</div>
    <div class="pos fs4" style="left: 158px; top: 1130px; color: #EB008B;">✸</div>
    <div class="pos fs4" style="left: 158px; top: 1229px; color: #EB008B;">✸</div>
    <div class="pos fs2" style="left: 323px; top: 1077px; color: #221F1F;">and</div>
    <div class="pos fs2" style="left: 513px; top: 1077px; color: #221F1F;">scored almost equal runs.</div>
    <div class="pos fs4" style="left: 158px; top: 1177px; color: #EB008B;" id="w14x">✸</div>
    <div class="pos fs2" style="left: 193px; top: 1177px; color: #221F1F;" id="w15x">Most</div>
    <div class="pos fs2" style="left: 267px; top: 1177px; color: #221F1F;" id="w16x">runs</div>
    <div class="pos fs2" style="left: 338px; top: 1177px; color: #221F1F;" id="w17x">scored</div>
    <div class="pos fs2" style="left: 433px; top: 1177px; color: #221F1F;" id="w18x">by</div>
    <div class="pos fs2" style="left: 475px; top: 1177px; color: #221F1F;" id="w19x">any</div>
    <div class="pos fs2" style="left: 532px; top: 1177px; color: #221F1F;" id="w20x">batsman</div>
    <div class="pos fs2" style="left: 657px; top: 1177px; color: #221F1F;" id="w21x">are</div>
    <div class="pos fs2" style="left: 319px; top: 1226px; color: #221F1F;">and</div>
    <div class="pos fs2" style="left: 193px; top: 1265px; color: #221F1F;">them.</div>
    <div class="pos fs4" style="left: 158px; top: 1318px; color: #EB008B;">✸</div>
    <div class="pos fs2" style="left: 323px; top: 1315px; color: #221F1F;">scored 2 more than one and a half century.</div>
    <div class="pos fs5" style="left: 568px; top: 1411px; color: #221F1F;">17</div>
    <div class="pos fs2" style="left: 323px; top: 1127px; color: #221F1F;">scored a complete century, no less, no more.</div>
    <div class="pos fs2" style="left: 828px; top: 1177px; color: #221F1F;">.</div>
    <div class="pos fs2" style="left: 500px; top: 1226px; color: #221F1F;">have a difference of just 1 run between</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000006.jpg');"> </div>
    <div class="pos fs12" style="left: 351px; top: 161px; color: #221F1F;">20</div>
    <div class="pos fs12" style="left: 185px; top: 374px; color: #221F1F;">10</div>
    <div class="pos fs11" style="left: 347px; top: 276px; color: #221F1F;">10</div>
    <div class="pos fs11" style="left: 347px; top: 326px; color: #221F1F;">20</div>
    <div class="pos fs11" style="left: 347px; top: 376px; color: #221F1F;">30</div>
    <div class="pos fs12" style="left: 465px; top: 154px; color: #221F1F;">30</div>
    <div class="pos fs12" style="left: 564px; top: 156px; color: #221F1F;">40</div>
    <div class="pos fs6" style="left: 379px; top: 219px; color: #221F1F;">Counting in 10's</div>
    <div class="pos fs11" style="left: 437px; top: 276px; color: #221F1F;">110</div>
    <div class="pos fs11" style="left: 541px; top: 276px; color: #221F1F;">310</div>
    <div class="pos fs11" style="left: 642px; top: 326px; color: #221F1F;">720</div>
    <div class="pos fs12" style="left: 1021px; top: 360px; color: #221F1F;">90</div>
    <div class="pos fs12" style="left: 666px; top: 158px; color: #221F1F;">50</div>
    <div class="pos fs12" style="left: 783px; top: 163px; color: #221F1F;">60</div>
    <div class="pos fs12" style="left: 906px; top: 182px; color: #221F1F;">70</div>
    <div class="pos fs12" style="left: 957px; top: 274px; color: #221F1F;">80</div>
    <div class="pos fs11" style="left: 642px; top: 625px; color: #221F1F;">780</div>
    <div class="pos fs12" style="left: 1002px; top: 594px; color: #221F1F;">100</div>
    <div class="pos fs11" style="left: 340px; top: 724px; color: #221F1F;">100</div>
    <div class="pos fs11" style="left: 437px; top: 675px; color: #221F1F;">190</div>
    <div class="pos fs11" style="left: 437px; top: 724px; color: #221F1F;">200</div>
    <div class="pos fs7" style="left: 845px; top: 663px; color: #221F1F;">Finish</div>
    <div class="pos fs11" style="left: 541px; top: 724px; color: #221F1F;">400</div>
    <div class="pos fs6" style="left: 216px; top: 846px; color: #221F1F;">Counting in 50's</div>
    <div class="pos fs11" style="left: 378px; top: 921px; color: #221F1F;">550</div>
    <div class="pos fs15" style="left: 817px; top: 875px; color: #221F1F;">50</div>
    <div class="pos fs11" style="left: 277px; top: 919px; color: #221F1F;">200</div>
    <div class="pos fs11" style="left: 277px; top: 969px; color: #221F1F;">250</div>
    <div class="pos fs11" style="left: 378px; top: 1021px; color: #221F1F;">650</div>
    <div class="pos fs11" style="left: 277px; top: 1069px; color: #221F1F;">350</div>
    <div class="pos fs11" style="left: 378px; top: 1120px; color: #221F1F;">750</div>
    <div class="pos fs15" style="left: 934px; top: 1153px; color: #221F1F;">50</div>
    <div class="pos fs11" style="left: 277px; top: 1218px; color: #221F1F;">500</div>
    <div class="pos fs11" style="left: 378px; top: 1220px; color: #221F1F;">850</div>
    <div class="pos fs2" style="left: 224px; top: 1273px; color: #221F1F;">How far can you go like this?</div>
    <div class="pos fs2" style="left: 224px; top: 1323px; color: #221F1F;">What is the biggest number you can call out?</div>
    <div class="pos fs5" style="left: 625px; top: 1412px; color: #221F1F;">18</div>
    <div class="pos fs15" style="left: 884px; top: 1266px; color: #221F1F;">50</div>
    <div class="pos fs15" style="left: 894px; top: 1077px; color: #221F1F;">50</div>
    <div class="pos fs7" style="left: 776px; top: 466px; -webkit-transform: rotate(-47.0deg); color: #221F1F;">Easy ! Just</div>
    <div class="pos fs7" style="left: 798px; top: 493px; -webkit-transform: rotate(-47.0deg); color: #221F1F;">skip in tens.</div>
    <div class="pos fs7" style="left: 145px; top: 632px; -webkit-transform: rotate(-75.0deg); color: #221F1F;">Wow ! How did</div>
    <div class="pos fs7" style="left: 180px; top: 632px; -webkit-transform: rotate(-75.0deg); color: #221F1F;">you do that ?</div>
    <div class="pos fs7" style="left: 566px; top: 829px; -webkit-transform: rotate(-25.0deg); color: #221F1F;">Count in fifty</div>
    <div class="pos fs7" style="left: 585px; top: 849px; -webkit-transform: rotate(-25.0deg); color: #221F1F;">up in a jiffy</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000007.jpg');"> </div>
    <div class="pos fs6" style="left: 163px; top: 159px; color: #221F1F;">Colour the Numbers</div>
    <div class="pos fit fs11" style="left: 357px; top: 344px; width: 371px; color: #221F1F;">
      <span class="just">744 810 45 401 54</span>
    </div>
    <div class="pos fit fs11" style="left: 357px; top: 395px; width: 371px; color: #221F1F;">
      <span class="just">555 374 171 261 159</span>
    </div>
    <div class="pos fit fs11" style="left: 357px; top: 444px; width: 371px; color: #221F1F;">
      <span class="just">656 140 179 891 16</span>
    </div>
    <div class="pos fit fs11" style="left: 357px; top: 494px; width: 371px; color: #221F1F;">
      <span class="just">195 155 410 159 685</span>
    </div>
    <div class="pos fs11" style="left: 357px; top: 544px; color: #221F1F;" id="w1x">454</div>
    <div class="pos fs11" style="left: 437px; top: 544px; color: #221F1F;" id="w2x">136</div>
    <div class="pos fs11" style="left: 532px; top: 544px; color: #221F1F;" id="w3x">60</div>
    <div class="pos fs11" style="left: 618px; top: 544px; color: #221F1F;" id="w4x">74</div>
    <div class="pos fs11" style="left: 684px; top: 544px; color: #221F1F;" id="w5x">699</div>
    <div class="pos fit fs11" style="left: 357px; top: 594px; width: 371px; color: #221F1F;">
      <span class="just">800 445 642 202 943</span>
    </div>
    <div class="pos fs16" style="left: 162px; top: 763px; color: #221F1F;">Find these numbers in the above chart. Colour them.</div>
    <div class="pos fs10" style="left: 263px; top: 866px; color: #221F1F;">Green</div>
    <div class="pos fs10" style="left: 552px; top: 865px; color: #221F1F;">Red</div>
    <div class="pos fs11" style="left: 171px; top: 920px; color: #221F1F;">One hundred forty</div>
    <div class="pos fs11" style="left: 171px; top: 976px; color: #221F1F;">Two hundred two</div>
    <div class="pos fs11" style="left: 171px; top: 1031px; color: #221F1F;">Two hundred sixty-one</div>
    <div class="pos fs11" style="left: 171px; top: 1087px; color: #221F1F;">Eight hundred</div>
    <div class="pos fit fs11" style="left: 171px; top: 1143px; width: 144px; color: #221F1F;">
      <span class="just">300 + 70 + 4</span>
    </div>
    <div class="pos fit fs11" style="left: 171px; top: 1198px; width: 144px; color: #221F1F;">
      <span class="just">600 + 50 + 6</span>
    </div>
    <div class="pos fit fs11" style="left: 171px; top: 1253px; width: 144px; color: #221F1F;">
      <span class="just">5 + 50 + 100</span>
    </div>
    <div class="pos fs11" style="left: 437px; top: 919px; color: #221F1F;">Fifty-four</div>
    <div class="pos fs11" style="left: 437px; top: 975px; color: #221F1F;">Sixty</div>
    <div class="pos fs11" style="left: 437px; top: 1030px; color: #221F1F;">One hundred ninety-five</div>
    <div class="pos fs11" style="left: 437px; top: 1086px; color: #221F1F;">Five hundred fifty-five</div>
    <div class="pos fit fs11" style="left: 437px; top: 1142px; width: 144px; color: #221F1F;">
      <span class="just">600 + 40 + 2</span>
    </div>
    <div class="pos fit fs11" style="left: 437px; top: 1197px; width: 144px; color: #221F1F;">
      <span class="just">100 + 70 + 9</span>
    </div>
    <div class="pos fs11" style="left: 437px; top: 1253px; color: #221F1F;">800 + 10</div>
    <div class="pos fs10" style="left: 825px; top: 865px; color: #221F1F;">Yellow</div>
    <div class="pos fs11" style="left: 722px; top: 920px; color: #221F1F;">Four hundred forty-five</div>
    <div class="pos fs11" style="left: 722px; top: 976px; color: #221F1F;">Sixteen</div>
    <div class="pos fs11" style="left: 722px; top: 1031px; color: #221F1F;">One hundred fifty-nine</div>
    <div class="pos fs11" style="left: 722px; top: 1086px; color: #221F1F;">Six hundred eighty-five</div>
    <div class="pos fs11" style="left: 722px; top: 1142px; color: #221F1F;">600 + 90 + 9</div>
    <div class="pos fs11" style="left: 722px; top: 1198px; color: #221F1F;">70 + 4</div>
    <div class="pos fs11" style="left: 722px; top: 1253px; color: #221F1F;">1 + 90 + 80</div>
    <div class="pos fs5" style="left: 568px; top: 1411px; color: #221F1F;">19</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000008.jpg');"> </div>
    <div class="pos fs6" style="left: 495px; top: 157px; color: #221F1F;">Jumping Animals</div>
    <div class="pos fs14" style="left: 206px; top: 297px; color: #EB008B;">Gabru</div>
    <div class="pos fs12" style="left: 299px; top: 338px; color: #221F1F;">90</div>
    <div class="pos fs12" style="left: 380px; top: 383px; color: #221F1F;">91</div>
    <div class="pos fs12" style="left: 334px; top: 439px; color: #221F1F;">92</div>
    <div class="pos fs12" style="left: 308px; top: 511px; color: #221F1F;">93</div>
    <div class="pos fs12" style="left: 356px; top: 568px; color: #221F1F;">94</div>
    <div class="pos fs12" style="left: 437px; top: 572px; color: #221F1F;">95</div>
    <div class="pos fs12" style="left: 581px; top: 674px; color: #221F1F;">103</div>
    <div class="pos fs2" style="left: 224px; top: 772px; color: #221F1F;">Gabru, Bunny and</div>
    <div class="pos fs2" style="left: 224px; top: 810px; color: #221F1F;">Tarru are jumping all</div>
    <div class="pos fs12" style="left: 638px; top: 771px; color: #221F1F;">104</div>
    <div class="pos fs12" style="left: 695px; top: 751px; color: #221F1F;">105</div>
    <div class="pos fs2" style="left: 224px; top: 848px; color: #221F1F;">the way. Gabru jumps on every 7th box, Bunny on every 5th</div>
    <div class="pos fs2" style="left: 224px; top: 887px; color: #221F1F;">box, Tarru on every 4th box.</div>
    <div class="pos fs2" style="left: 224px; top: 937px; color: #221F1F;">Gabru starts jumping from number 90.</div>
    <div class="pos fit fs2" style="left: 224px; top: 986px; width: 533px; color: #221F1F;">
      <span class="just">Bunny starts jumping from number 99.</span>
    </div>
    <div class="pos fit fs2" style="left: 224px; top: 1036px; width: 535px; color: #221F1F;">
      <span class="just">Tarru starts jumping from number 106.</span>
    </div>
    <div class="pos fs12" style="left: 507px; top: 508px; color: #221F1F;">96</div>
    <div class="pos fs12" style="left: 627px; top: 589px; color: #221F1F;">102</div>
    <div class="pos fs12" style="left: 776px; top: 655px; color: #221F1F;">106</div>
    <div class="pos fs12" style="left: 547px; top: 422px; color: #221F1F;">97</div>
    <div class="pos fs12" style="left: 620px; top: 374px; color: #221F1F;">98</div>
    <div class="pos fs12" style="left: 683px; top: 361px; color: #221F1F;">99</div>
    <div class="pos fs12" style="left: 728px; top: 436px; color: #221F1F;">100</div>
    <div class="pos fs12" style="left: 689px; top: 505px; color: #221F1F;">101</div>
    <div class="pos fs12" style="left: 820px; top: 558px; color: #221F1F;">107</div>
    <div class="pos fs14" style="left: 933px; top: 665px; color: #00ADEE;">Tarru</div>
    <div class="pos fs12" style="left: 882px; top: 511px; color: #221F1F;">108</div>
    <div class="pos fs12" style="left: 991px; top: 494px; color: #221F1F;">109</div>
    <div class="pos fs12" style="left: 1078px; top: 496px; color: #221F1F;">110</div>
    <div class="pos fs14" style="left: 861px; top: 291px; color: #221F1F;">Bunny</div>
    <div class="pos fs5" style="left: 622px; top: 1412px; color: #221F1F;">20</div>
    <div class="pos fs12" style="left: 212px; top: 380px; -webkit-transform: rotate(-25.0deg); color: #221F1F;">89</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000009.jpg');"> </div>
    <div class="pos fs12" style="left: 633px; top: 193px; color: #221F1F;">131</div>
    <div class="pos fs12" style="left: 600px; top: 265px; color: #221F1F;">130</div>
    <div class="pos fs12" style="left: 636px; top: 316px; color: #221F1F;">129</div>
    <div class="pos fs12" style="left: 254px; top: 363px; color: #221F1F;">114</div>
    <div class="pos fs12" style="left: 176px; top: 437px; color: #221F1F;">113</div>
    <div class="pos fs12" style="left: 157px; top: 494px; color: #221F1F;">112</div>
    <div class="pos fs12" style="left: 94px; top: 539px; color: #221F1F;">111</div>
    <div class="pos fs12" style="left: 231px; top: 617px; color: #221F1F;">118</div>
    <div class="pos fs12" style="left: 197px; top: 671px; color: #221F1F;">119</div>
    <div class="pos fs12" style="left: 203px; top: 742px; color: #221F1F;">120</div>
    <div class="pos fs12" style="left: 256px; top: 802px; color: #221F1F;">121</div>
    <div class="pos fs12" style="left: 327px; top: 781px; color: #221F1F;">122</div>
    <div class="pos fs12" style="left: 327px; top: 402px; color: #221F1F;">115</div>
    <div class="pos fs12" style="left: 319px; top: 478px; color: #221F1F;">116</div>
    <div class="pos fs12" style="left: 307px; top: 547px; color: #221F1F;">117</div>
    <div class="pos fs12" style="left: 403px; top: 615px; color: #221F1F;">124</div>
    <div class="pos fs12" style="left: 382px; top: 668px; color: #221F1F;">123</div>
    <div class="pos fs12" style="left: 545px; top: 714px; color: #221F1F;">141</div>
    <div class="pos fs12" style="left: 561px; top: 789px; color: #221F1F;">142 143</div>
    <div class="pos fs12" style="left: 707px; top: 760px; color: #221F1F;">144</div>
    <div class="pos fs12" style="left: 588px; top: 648px; color: #221F1F;">140</div>
    <div class="pos fs12" style="left: 768px; top: 683px; color: #221F1F;">145</div>
    <div class="pos fs12" style="left: 455px; top: 479px; color: #221F1F;" id="w1x">125</div>
    <div class="pos fs12" style="left: 533px; top: 479px; color: #221F1F;" id="w2x">126</div>
    <div class="pos fs12" style="left: 598px; top: 479px; color: #221F1F;" id="w3x">127</div>
    <div class="pos fs12" style="left: 756px; top: 563px; color: #221F1F;">138</div>
    <div class="pos fs12" style="left: 680px; top: 604px; color: #221F1F;">139</div>
    <div class="pos fs12" style="left: 809px; top: 607px; color: #221F1F;">146</div>
    <div class="pos fs12" style="left: 670px; top: 387px; color: #221F1F;">128</div>
    <div class="pos fs12" style="left: 847px; top: 432px; color: #221F1F;">136</div>
    <div class="pos fs12" style="left: 815px; top: 497px; color: #221F1F;">137</div>
    <div class="pos fs12" style="left: 868px; top: 542px; color: #221F1F;">147 148 149</div>
    <div class="pos fs12" style="left: 851px; top: 300px; color: #221F1F;">134</div>
    <div class="pos fs12" style="left: 861px; top: 353px; color: #221F1F;">135</div>
    <div class="pos fs12" style="left: 725px; top: 184px; color: #221F1F;">132</div>
    <div class="pos fs12" style="left: 809px; top: 219px; color: #221F1F;">133</div>
    <div class="pos fs2" style="left: 162px; top: 950px; color: #221F1F;">Bunny’s tenth jump is on number</div>
    <div class="pos fs2" style="left: 162px; top: 992px; color: #221F1F;">Tarru’s tenth jump is on number</div>
    <div class="pos fs2" style="left: 162px; top: 1035px; color: #221F1F;">Gabru’s tenth jump will be on number</div>
    <div class="pos fs2" style="left: 162px; top: 1076px; color: #221F1F;">Gabru and Bunny both jump on numbers 104,</div>
    <div class="pos fs2" style="left: 718px; top: 950px; color: #221F1F;">.</div>
    <div class="pos fs2" style="left: 702px; top: 992px; color: #221F1F;">.</div>
    <div class="pos fs2" style="left: 777px; top: 1035px; color: #221F1F;">.</div>
    <div class="pos fs2" style="left: 838px; top: 1076px; color: #221F1F;">and</div>
    <div class="pos fs16" style="left: 162px; top: 1121px; color: #221F1F;">Find out:</div>
    <div class="pos fs2 " style="left: 162px; top: 1162px; color: #221F1F;">
      <div class="just"><span style="color: #EB008B; font-family: 'akoelp_zapfdingbats';">❖</span> Tarru and Bunny jump on numbers</div>
    </div>
    <div class="pos fs2" style="left: 197px; top: 1197px; color: #221F1F;">and</div>
    <div class="pos fs2" style="left: 344px; top: 1197px; color: #221F1F;">.</div>
    <div class="pos fs4" style="left: 162px; top: 1239px; color: #EB008B;" id="w4x">❖</div>
    <div class="pos fs2" style="left: 197px; top: 1239px; color: #221F1F;" id="w5x">Is</div>
    <div class="pos fs2" style="left: 230px; top: 1239px; color: #221F1F;" id="w6x">there</div>
    <div class="pos fs2" style="left: 308px; top: 1239px; color: #221F1F;" id="w7x">any</div>
    <div class="pos fs2" style="left: 366px; top: 1239px; color: #221F1F;" id="w8x">number</div>
    <div class="pos fs2" style="left: 479px; top: 1239px; color: #221F1F;" id="w9x">where</div>
    <div class="pos fs2" style="left: 568px; top: 1239px; color: #221F1F;" id="w10x">all</div>
    <div class="pos fs2" style="left: 610px; top: 1239px; color: #221F1F;" id="w11x">three</div>
    <div class="pos fs2" style="left: 687px; top: 1239px; color: #221F1F;" id="w12x">of</div>
    <div class="pos fs2" style="left: 721px; top: 1239px; color: #221F1F;" id="w13x">them</div>
    <div class="pos fs2" style="left: 798px; top: 1239px; color: #221F1F;" id="w14x">jump?</div>
    <div class="pos fs4" style="left: 162px; top: 1281px; color: #EB008B;" id="w15x">❖</div>
    <div class="pos fs2" style="left: 197px; top: 1281px; color: #221F1F;" id="w16x">Guess</div>
    <div class="pos fs2" style="left: 288px; top: 1281px; color: #221F1F;" id="w17x">who</div>
    <div class="pos fs2" style="left: 351px; top: 1281px; color: #221F1F;" id="w18x">will</div>
    <div class="pos fs2" style="left: 406px; top: 1281px; color: #221F1F;" id="w19x">finish</div>
    <div class="pos fs2" style="left: 489px; top: 1281px; color: #221F1F;" id="w20x">in</div>
    <div class="pos fs2" style="left: 524px; top: 1281px; color: #221F1F;" id="w21x">the</div>
    <div class="pos fs2" style="left: 576px; top: 1281px; color: #221F1F;" id="w22x">least</div>
    <div class="pos fs2" style="left: 647px; top: 1281px; color: #221F1F;" id="w23x">jumps?</div>
    <div class="pos fs2" style="left: 197px; top: 1316px; color: #221F1F;">many jumps?</div>
    <div class="pos fs5" style="left: 567px; top: 1411px; color: #221F1F;">21</div>
    <div class="pos fs2" style="left: 897px; top: 1281px; color: #221F1F;">In how</div>
    <div class="pos fs2" style="left: 776px; top: 1162px; color: #221F1F;">,</div>
    <div class="pos fs2" style="left: 881px; top: 1162px; color: #221F1F;">,</div>
    <div class="pos fs2" style="left: 978px; top: 1076px; color: #221F1F;">.</div>
    <div class="pos fs12" style="left: 969px; top: 963px; color: #221F1F;">175</div>
    <div class="pos fs7" style="left: 902px; top: 685px; -webkit-transform: rotate(-30.0deg); color: #221F1F;">Hee! Hee!</div>
    <div class="pos fs7" style="left: 928px; top: 707px; -webkit-transform: rotate(-30.0deg); color: #221F1F;">All this</div>
    <div class="pos fs7" style="left: 933px; top: 742px; -webkit-transform: rotate(-30.0deg); color: #221F1F;">jumping is</div>
    <div class="pos fs7" style="left: 942px; top: 775px; -webkit-transform: rotate(-30.0deg); color: #221F1F;">tickling me.</div>
    <div class="pos fs12" style="left: 1023px; top: 975px; -webkit-transform: rotate(-10.0deg); color: #221F1F;">174</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000010.jpg');"> </div>
    <div class="pos fs6" style="left: 223px; top: 154px; color: #221F1F;">Class, Jump!</div>
    <div class="pos fs2" style="left: 216px; top: 689px; color: #221F1F;">Jump 2 steps forward:</div>
    <div class="pos fs2" style="left: 216px; top: 738px; color: #221F1F;">104, 106, 108,</div>
    <div class="pos fs2" style="left: 498px; top: 738px; color: #221F1F;">,</div>
    <div class="pos fs2" style="left: 216px; top: 788px; color: #221F1F;">Jump 2 steps backward:</div>
    <div class="pos fs2" style="left: 216px; top: 838px; color: #221F1F;">262, 260, 258,</div>
    <div class="pos fs2" style="left: 498px; top: 838px; color: #221F1F;">,</div>
    <div class="pos fs2" style="left: 216px; top: 888px; color: #221F1F;">Jump 10 steps forward:</div>
    <div class="pos fs2" style="left: 216px; top: 937px; color: #221F1F;">110, 120, 130,</div>
    <div class="pos fs2" style="left: 498px; top: 938px; color: #221F1F;">,</div>
    <div class="pos fs2" style="left: 216px; top: 988px; color: #221F1F;">Jump 10 steps backward:</div>
    <div class="pos fs2" style="left: 216px; top: 1037px; color: #221F1F;">200, 190, 180,</div>
    <div class="pos fs2" style="left: 498px; top: 1037px; color: #221F1F;">,</div>
    <div class="pos fs2" style="left: 216px; top: 1087px; color: #221F1F;">Continue the pattern:</div>
    <div class="pos fs2" style="left: 216px; top: 1137px; color: #221F1F;">550, 560, 570,</div>
    <div class="pos fs2" style="left: 498px; top: 1137px; color: #221F1F;">,</div>
    <div class="pos fs2" style="left: 216px; top: 1187px; color: #221F1F;">910, 920, 930, 940,</div>
    <div class="pos fit fs2" style="left: 216px; top: 1237px; width: 195px; color: #221F1F;">
      <span class="just">209, 207, 205,</span>
    </div>
    <div class="pos fit fs2" style="left: 216px; top: 1287px; width: 195px; color: #221F1F;">
      <span class="just">401, 402, 403,</span>
    </div>
    <div class="pos fs2" style="left: 567px; top: 1187px; color: #221F1F;">,</div>
    <div class="pos fs2" style="left: 498px; top: 1237px; color: #221F1F;">,</div>
    <div class="pos fs2" style="left: 498px; top: 1287px; color: #221F1F;">,</div>
    <div class="pos fs2" style="left: 594px; top: 1237px; color: #221F1F;">,</div>
    <div class="pos fs2" style="left: 594px; top: 1287px; color: #221F1F;">,</div>
    <div class="pos fs5" style="left: 622px; top: 1412px; color: #221F1F;">22</div>
    <div class="pos fs2" style="left: 594px; top: 738px; color: #221F1F;">,</div>
    <div class="pos fs2" style="left: 594px; top: 838px; color: #221F1F;">,</div>
    <div class="pos fs2" style="left: 594px; top: 938px; color: #221F1F;">,</div>
    <div class="pos fs2" style="left: 594px; top: 1037px; color: #221F1F;">,</div>
    <div class="pos fs2" style="left: 594px; top: 1137px; color: #221F1F;">,</div>
    <div class="pos fs2" style="left: 662px; top: 1187px; color: #221F1F;">,</div>
    <div class="pos fs2" style="left: 690px; top: 1237px; color: #221F1F;">,</div>
    <div class="pos fs2" style="left: 690px; top: 1287px; color: #221F1F;">,</div>
    <div class="pos fs2" style="left: 690px; top: 738px; color: #221F1F;">,</div>
    <div class="pos fs2" style="left: 690px; top: 838px; color: #221F1F;">,</div>
    <div class="pos fs2" style="left: 690px; top: 938px; color: #221F1F;">,</div>
    <div class="pos fs2" style="left: 690px; top: 1037px; color: #221F1F;">,</div>
    <div class="pos fs2" style="left: 690px; top: 1137px; color: #221F1F;">,</div>
    <div class="pos fs2" style="left: 759px; top: 1187px; color: #221F1F;">,</div>
    <div class="pos fs2" style="left: 786px; top: 1237px; color: #221F1F;">.</div>
    <div class="pos fs2" style="left: 786px; top: 1287px; color: #221F1F;">.</div>
    <div class="pos fs2" style="left: 786px; top: 738px; color: #221F1F;">.</div>
    <div class="pos fs2" style="left: 786px; top: 838px; color: #221F1F;">.</div>
    <div class="pos fs2" style="left: 786px; top: 938px; color: #221F1F;">.</div>
    <div class="pos fs2" style="left: 786px; top: 1037px; color: #221F1F;">.</div>
    <div class="pos fs2" style="left: 786px; top: 1137px; color: #221F1F;">.</div>
    <div class="pos fs2" style="left: 855px; top: 1187px; color: #221F1F;">.</div>
    <div class="pos fs7" style="left: 928px; top: 1106px; color: #221F1F;">Join in!</div>
    <div class="pos fs7" style="left: 869px; top: 547px; -webkit-transform: rotate(-15.0deg); color: #221F1F;">Long way to</div>
    <div class="pos fs7" style="left: 923px; top: 568px; -webkit-transform: rotate(-15.0deg); color: #221F1F;">go!</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000011.jpg');"> </div>
    <div class="pos fs6" style="left: 161px; top: 148px; color: #221F1F;">Lazy Crazy Shop</div>
    <div class="pos fs2" style="left: 161px; top: 201px; color: #221F1F;">This is the jungle shop. Lazy Crazy gives things only in packets</div>
    <div class="pos fs2" style="left: 161px; top: 239px; color: #221F1F;">of tens, hundreds and loose items.</div>
    <div class="pos fs2" style="left: 161px; top: 728px; color: #221F1F;">Find out how many packets of tens, hundreds and loose items</div>
    <div class="pos fs2" style="left: 161px; top: 766px; color: #221F1F;">each animal will take. Fill in the blanks.</div>
    <div class="pos fs10" style="left: 537px; top: 825px; color: #221F1F;">Packets</div>
    <div class="pos fs10" style="left: 546px; top: 860px; color: #221F1F;">of 100</div>
    <div class="pos fs11" style="left: 413px; top: 935px; color: #221F1F;">143</div>
    <div class="pos fs10" style="left: 719px; top: 825px; color: #221F1F;">Packets</div>
    <div class="pos fs10" style="left: 735px; top: 860px; color: #221F1F;">of 10</div>
    <div class="pos fs10" style="left: 875px; top: 825px; color: #221F1F;">Loose</div>
    <div class="pos fs10" style="left: 878px; top: 860px; color: #221F1F;">items</div>
    <div class="pos fs11" style="left: 413px; top: 1070px; color: #221F1F;">210</div>
    <div class="pos fs11" style="left: 413px; top: 1175px; color: #221F1F;">242</div>
    <div class="pos fs11" style="left: 413px; top: 1289px; color: #221F1F;">552</div>
    <div class="pos fs5" style="left: 564px; top: 1411px; color: #221F1F;">23</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000012.jpg');"> </div>
    <div class="pos fs2" style="left: 216px; top: 162px; color: #221F1F;">Lazy Crazy also has a crazy way of taking money. He takes only</div>
    <div class="pos fs2" style="left: 216px; top: 228px; color: #221F1F;">in</div>
    <div class="pos fs2" style="left: 392px; top: 228px; color: #221F1F;">notes,</div>
    <div class="pos fs2" style="left: 609px; top: 228px; color: #221F1F;">notes and</div>
    <div class="pos fs2" style="left: 840px; top: 228px; color: #221F1F;">coins. Now find</div>
    <div class="pos fs2" style="left: 216px; top: 283px; color: #221F1F;">out how they will pay him for what they have taken.</div>
    <div class="pos fit fs2" style="left: 472px; top: 353px; width: 95px; color: #221F1F;">
      <span class="just">Rs 420</span>
    </div>
    <div class="pos fit fs2" style="left: 472px; top: 478px; width: 95px; color: #221F1F;">
      <span class="just">Rs 143</span>
    </div>
    <div class="pos fit fs2" style="left: 472px; top: 603px; width: 95px; color: #221F1F;">
      <span class="just">Rs 242</span>
    </div>
    <div class="pos fit fs2" style="left: 472px; top: 727px; width: 95px; color: #221F1F;">
      <span class="just">Rs 55</span>
    </div>
    <div class="pos fs6" style="left: 220px; top: 796px; color: #221F1F;">Who am I? Match with the number.</div>
    <div class="pos fs2" style="left: 219px; top: 853px; color: #221F1F;">a) I come between 40 and 50 and</div>
    <div class="pos fs2" style="left: 254px; top: 891px; color: #221F1F;">there is a 5 in my name.</div>
    <div class="pos fs2" style="left: 950px; top: 891px; color: #221F1F;">96</div>
    <div class="pos fs2" style="left: 219px; top: 939px; color: #221F1F;">b) I have 9 in my name and am very close to 90. 150</div>
    <div class="pos fs2" style="left: 219px; top: 987px; color: #221F1F;">c) If you hit a 4 after me, you score a century.</div>
    <div class="pos fs2" style="left: 219px; top: 1035px; color: #221F1F;">d) I am equal to ten notes of 10.</div>
    <div class="pos fs2" style="left: 219px; top: 1083px; color: #221F1F;">e) I am century + half century</div>
    <div class="pos fs2" style="left: 955px; top: 987px; color: #221F1F;">45</div>
    <div class="pos fs2" style="left: 955px; top: 1035px; color: #221F1F;">89</div>
    <div class="pos fs2" style="left: 955px; top: 1083px; color: #221F1F;">87</div>
    <div class="pos fs2" style="left: 219px; top: 1131px; color: #221F1F;">f) I am exactly in between 77 and 97.</div>
    <div class="pos fs2" style="left: 938px; top: 1131px; color: #221F1F;">100</div>
    <div class="pos fs8" style="left: 242px; top: 1247px; color: #221F1F;">In this chapter several stories and exercises are used to help children understand the decimal</div>
    <div class="pos fs8" style="left: 242px; top: 1274px; color: #221F1F;">number system. The term 'place value', which often confuses children, has not been used at all.</div>
    <div class="pos fs8" style="left: 242px; top: 1301px; color: #221F1F;">Teachers could also find out about other locally used number systems, if any, especially while</div>
    <div class="pos fs8" style="left: 242px; top: 1328px; color: #221F1F;">working in tribal communities.</div>
    <div class="pos fs5" style="left: 622px; top: 1412px; color: #221F1F;">24</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000013.jpg');"> </div>
    <div class="pos fs6" style="left: 161px; top: 156px; color: #221F1F;">How Many are these?</div>
    <div class="pos fs2" style="left: 900px; top: 286px; color: #221F1F;">rupees</div>
    <div class="pos fs2" style="left: 900px; top: 481px; color: #221F1F;">sticks</div>
    <div class="pos fs2" style="left: 901px; top: 663px; color: #221F1F;">blocks</div>
    <div class="pos fs2" style="left: 183px; top: 719px; color: #221F1F;">100</div>
    <div class="pos fs2" style="left: 353px; top: 713px; color: #221F1F;">100</div>
    <div class="pos fs2" style="left: 504px; top: 742px; color: #221F1F;">10</div>
    <div class="pos fs2" style="left: 900px; top: 845px; color: #221F1F;">beads</div>
    <div class="pos fs2" style="left: 900px; top: 1026px; color: #221F1F;">rupees</div>
    <div class="pos fs12" style="left: 712px; top: 1104px; color: #221F1F;">Who am I?</div>
    <div class="pos fs7" style="left: 633px; top: 1148px; color: #221F1F;">There is no biggest number</div>
    <div class="pos fs7" style="left: 660px; top: 1192px; color: #221F1F;">Take any you can find</div>
    <div class="pos fs7" style="left: 633px; top: 1236px; color: #221F1F;">Add me to get the next one</div>
    <div class="pos fs7" style="left: 638px; top: 1281px; color: #221F1F;">To count, keep me in mind.</div>
    <div class="pos fs5" style="left: 564px; top: 1411px; color: #221F1F;">25</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000014.jpg');"> </div>
    <div class="pos fs7" style="left: 310px; top: 230px; color: #221F1F;">I am Chanda Mama. I have so many friends which twinkle in the</div>
    <div class="pos fs7" style="left: 324px; top: 263px; color: #221F1F;">sky. Yes, you are right! My friends are stars. One day all of</div>
    <div class="pos fs7" style="left: 336px; top: 295px; color: #221F1F;">them came to my home. I started counting to see how many</div>
    <div class="pos fs7" style="left: 354px; top: 328px; color: #221F1F;">friends had come. But my friends were too many. So to</div>
    <div class="pos fs7" style="left: 364px; top: 364px; color: #221F1F;">remember their numbers, I did something like this —</div>
    <div class="pos fs7" style="left: 522px; top: 428px; color: #221F1F;">Hello</div>
    <div class="pos fs7" style="left: 491px; top: 457px; color: #221F1F;">everybody!</div>
    <div class="pos fs17" style="left: 219px; top: 818px; color: #221F1F;">I counted one star and kept one</div>
    <div class="pos fs17" style="left: 300px; top: 871px; color: #221F1F;">for one star.</div>
    <div class="pos fs17" style="left: 219px; top: 977px; color: #221F1F;">When I had 10</div>
    <div class="pos fs6" style="left: 380px; top: 776px; color: #221F1F;">Moon Mama Counts his Starry Friends</div>
    <div class="pos fs17" style="left: 677px; top: 818px; color: #221F1F;">card in my pocket.</div>
    <div class="pos fit fs17" style="left: 604px; top: 871px; width: 147px; color: #221F1F;">
      <span class="just">for 2 stars.</span>
    </div>
    <div class="pos fit fs17" style="left: 492px; top: 924px; width: 258px; color: #221F1F;">
      <span class="just">for how many stars?</span>
    </div>
    <div class="pos fs17" style="left: 454px; top: 977px; color: #221F1F;" id="w1x">cards,</div>
    <div class="pos fs17" style="left: 536px; top: 977px; color: #221F1F;" id="w2x">I</div>
    <div class="pos fs17" style="left: 558px; top: 977px; color: #221F1F;" id="w3x">changed</div>
    <div class="pos fs17" style="left: 667px; top: 977px; color: #221F1F;" id="w4x">it</div>
    <div class="pos fs17" style="left: 695px; top: 977px; color: #221F1F;" id="w5x">with</div>
    <div class="pos fs17" style="left: 756px; top: 977px; color: #221F1F;" id="w6x">this</div>
    <div class="pos fs17" style="left: 812px; top: 977px; color: #221F1F;" id="w7x">card</div>
    <div class="pos fs17" style="left: 908px; top: 977px; color: #221F1F;" id="w8x">.</div>
    <div class="pos fs17" style="left: 217px; top: 1070px; color: #221F1F;">But my friends kept coming. So I had to count more stars. My</div>
    <div class="pos fs17" style="left: 217px; top: 1109px; color: #221F1F;">pockets were getting full. So when I had 10 cards like this</div>
    <div class="pos fs17" style="left: 217px; top: 1149px; color: #221F1F;">changed it with a</div>
    <div class="pos fs17" style="left: 1030px; top: 1109px; color: #221F1F;">I</div>
    <div class="pos fs17" style="left: 496px; top: 1149px; color: #221F1F;">card.</div>
    <div class="pos fs17" style="left: 219px; top: 1271px; color: #221F1F;">But I have so many, many, friends that my pockets kept getting full.</div>
    <div class="pos fs17" style="left: 219px; top: 1310px; color: #221F1F;">Just see how many cards I had.</div>
    <div class="pos fs5" style="left: 622px; top: 1412px; color: #221F1F;">26</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000015.jpg');"> </div>
    <div class="pos fs18" style="left: 478px; top: 265px; color: #221F1F;">line. We'll play a game.</div>
    <div class="pos fs18" style="left: 517px; top: 237px; color: #221F1F;">everybody in a</div>
    <div class="pos fs18" style="left: 545px; top: 209px; color: #221F1F;">All right,</div>
    <div class="pos fs18" style="left: 746px; top: 391px; color: #221F1F;">Yippee!</div>
    <div class="pos fs17" style="left: 161px; top: 678px; color: #221F1F;">Which cards will I have in my pocket if I have counted up to...</div>
    <div class="pos fit fs2" style="left: 161px; top: 744px; width: 81px; color: #221F1F;">
      <span class="just">a. 19</span>
    </div>
    <div class="pos fit fs2" style="left: 161px; top: 806px; width: 81px; color: #221F1F;">
      <span class="just">b. 21</span>
    </div>
    <div class="pos fit fs2" style="left: 161px; top: 867px; width: 81px; color: #221F1F;">
      <span class="just">c. 95</span>
    </div>
    <div class="pos fit fs2" style="left: 161px; top: 928px; width: 98px; color: #221F1F;">
      <span class="just">d. 201</span>
    </div>
    <div class="pos fit fs2" style="left: 161px; top: 990px; width: 98px; color: #221F1F;">
      <span class="just">e. 260</span>
    </div>
    <div class="pos fit fs2" style="left: 161px; top: 1051px; width: 98px; color: #221F1F;">
      <span class="just">f. 300</span>
    </div>
    <div class="pos fit fs2" style="left: 161px; top: 1112px; width: 98px; color: #221F1F;">
      <span class="just">g. 306</span>
    </div>
    <div class="pos fit fs2" style="left: 161px; top: 1174px; width: 98px; color: #221F1F;">
      <span class="just">h. 344</span>
    </div>
    <div class="pos fit fs2" style="left: 161px; top: 1235px; width: 98px; color: #221F1F;">
      <span class="just">i. 350</span>
    </div>
    <div class="pos fit fs2" style="left: 161px; top: 1296px; width: 98px; color: #221F1F;">
      <span class="just">j. 400</span>
    </div>
    <div class="pos fs5" style="left: 565px; top: 1411px; color: #221F1F;">27</div>
  </body>
</html>
<?xml version='1.0' encoding='UTF-8'?>
<html xmlns:epub="http://www.idpf.org/2007/ops" xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
  <head>
    <title> </title>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=1208, height=1573"/>
    <link href="fonts.css" rel="stylesheet" type="text/css"/>
    <link href="styles.css" rel="stylesheet" type="text/css"/>
  </head>
  <body style="width:1208px; height:1573px; margin:0px;">
    <div id="bg" style="top: 0px; left: 0px; width: 1208px; height: 1573px; background-image: url('page_000016.jpg');"> </div>
    <div class="pos fs17" style="left: 218px; top: 158px; color: #221F1F;">When I had</div>
    <div class="pos fit fs17" style="left: 467px; top: 158px; width: 579px; color: #221F1F;">
      <span class="just">cards in my pocket, I knew I had counted 20</span>
    </div>
    <div class="pos fit fs17" style="left: 218px; top: 197px; width: 828px; color: #221F1F;">
      <span class="just">stars. Now you tell me the number of stars counted in each case.</span>
    </div>
    <div class="pos fs17" style="left: 218px; top: 235px; color: #221F1F;">Write the answer in the blank space.</div>
    <div class="pos fs12" style="left: 609px; top: 814px; color: #221F1F;">??</div>
    <div class="pos fs18" style="left: 891px; top: 1010px; color: #221F1F;">Nice new</div>
    <div class="pos fs18" style="left: 892px; top: 1035px; color: #221F1F;">patterns</div>
    <div class="pos fs17" style="left: 220px; top: 1317px; color: #221F1F;">Guess how many starry friends I have in all... !!!</div>
    <div class="pos fs5" style="left: 622px; top: 1412px; color: #221F1F;">28</div>
    <div class="pos fs6" style="left: 317px; top: 350px; -webkit-transform: rotate(-10.0deg); color: #EB008B;">20</div>
  </body>
</html>
