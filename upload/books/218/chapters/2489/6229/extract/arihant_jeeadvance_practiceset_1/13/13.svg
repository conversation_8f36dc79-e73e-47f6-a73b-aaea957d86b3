<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg viewBox="0 0 909 1286" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<defs>
<clipPath id="c0_13"><path d="M738.2,614.2l0,-116.1l115,0l0,116.1Z" /></clipPath>
<style type="text/css"><![CDATA[
.g1_13{
fill: #E7E8E9;
}
.g2_13{
fill: none;
stroke: #FFFFFF;
stroke-width: 0.33;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
}
.g3_13{
fill: #231F20;
}
.g4_13{
fill: none;
stroke: #231F20;
stroke-width: 0.5253105;
stroke-linecap: butt;
stroke-linejoin: round;
stroke-miterlimit: 2;
}
.g5_13{
fill: none;
stroke: #A8AAAD;
stroke-width: 0.9168042;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
}
.g6_13{
fill: none;
stroke: #231F20;
stroke-width: 0.7626355;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
stroke-dasharray: 4,3;
}
.g7_13{
fill: none;
stroke: #231F20;
stroke-width: 0.7626355;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
}
.g8_13{
fill: none;
stroke: #231F20;
stroke-width: 1.0683898;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
}
.g9_13{
fill: none;
stroke: #231F20;
stroke-width: 0.76350635;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
}
.g10_13{
fill: none;
stroke: #818386;
stroke-width: 0.76350635;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
}
.g11_13{
fill: none;
stroke: #231F20;
stroke-width: 0.76393545;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
}
.g12_13{
fill: none;
stroke: #231F20;
stroke-width: 0.76350635;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
stroke-dasharray: 4,3;
}
.g13_13{
fill: none;
stroke: #231F20;
stroke-width: 1.0692459;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
}
.g14_13{
fill: #A8AAAD;
}
.g15_13{
fill: none;
stroke: #939598;
stroke-width: 0.9168042;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
}
.g16_13{
fill: none;
stroke: #818386;
stroke-width: 0.7557138;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
}
.g17_13{
fill: none;
stroke: #231F20;
stroke-width: 0.7557138;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
}
.g18_13{
fill: none;
stroke: #231F20;
stroke-width: 0.7557138;
stroke-linecap: butt;
stroke-linejoin: miter;
stroke-miterlimit: 2;
stroke-dasharray: 4,3;
}
]]></style>
</defs>
<path fill-rule="evenodd" d="M180.3,133.1c0,-8.3,-6.8,-15.1,-15,-15.1c-8.3,0,-15,6.8,-15,15.1c0,8.3,6.7,15.1,15,15.1c8.2,0,15,-6.8,15,-15.1Z" class="g1_13" />
<path d="M180.3,133.1c0,-8.3,-6.8,-15.1,-15,-15.1c-8.3,0,-15,6.8,-15,15.1c0,8.3,6.7,15.1,15,15.1c8.2,0,15,-6.8,15,-15.1Z" class="g2_13" />
<path fill-rule="evenodd" d="M195.1,218l15.3,0l-15.3,0Z" class="g3_13" />
<path d="M195.1,218l15.3,0" class="g4_13" />
<path fill-rule="evenodd" d="M157.5,252.9l15.3,0l-15.3,0Z" class="g3_13" />
<path d="M157.5,252.9l15.3,0" class="g4_13" />
<path fill-rule="evenodd" d="M204.6,387.2l20,0l-20,0Z" class="g3_13" />
<path d="M204.6,387.2l20,0" class="g4_13" />
<path fill-rule="evenodd" d="M258.9,387.2l19.9,0l-19.9,0Z" class="g3_13" />
<path d="M258.9,387.2l19.9,0" class="g4_13" />
<path fill-rule="evenodd" d="M296,387.2l17.7,0l-17.7,0Z" class="g3_13" />
<path d="M296,387.2l17.7,0" class="g4_13" />
<path fill-rule="evenodd" d="M180.4,465.3l20,0l-20,0Z" class="g3_13" />
<path d="M180.4,465.3l20,0" class="g4_13" />
<path fill-rule="evenodd" d="M234.7,465.3l20,0l-20,0Z" class="g3_13" />
<path d="M234.7,465.3l20,0" class="g4_13" />
<path fill-rule="evenodd" d="M271.9,465.3l17.6,0l-17.6,0Z" class="g3_13" />
<path d="M271.9,465.3l17.6,0" class="g4_13" />
<path fill-rule="evenodd" d="M159.2,796.4l9.1,0l-9.1,0Z" class="g3_13" />
<path d="M159.2,796.4l9.1,0" class="g4_13" />
<path fill-rule="evenodd" d="M212,796.4l9.1,0l-9.1,0Z" class="g3_13" />
<path d="M212,796.4l9.1,0" class="g4_13" />
<path fill-rule="evenodd" d="M159.2,976.4l9.1,0l-9.1,0Z" class="g3_13" />
<path d="M159.2,976.4l9.1,0" class="g4_13" />
<path d="M55.7,150.5l797.5,0" class="g5_13" />
<path d="M333.3,880.6l0,73.5l-84.2,0" class="g6_13" />
<path d="M245.8,954.1l-64.1,0" class="g7_13" />
<path d="M334.9,864.9c-3.4,60.6,-36,84.7,-91.3,89.2" class="g8_13" />
<path d="M323.3,861.6c6,0,10.9,4.9,10.9,10.9c0,6,-4.9,10.9,-10.9,10.9c-6,0,-10.9,-4.9,-10.9,-10.9c0,-6,4.9,-10.9,10.9,-10.9Z" class="g7_13" />
<path fill-rule="evenodd" d="M324.4,871.5c0.5,0.6,0.6,1.5,0,2.1c-0.5,0.5,-1.4,0.6,-2,0c-0.6,-0.5,-0.6,-1.4,-0.1,-2c0.6,-0.6,1.5,-0.6,2.1,-0.1Z" class="g3_13" />
<path d="M324.4,871.5c0.5,0.6,0.6,1.5,0,2.1c-0.5,0.5,-1.4,0.6,-2,0c-0.6,-0.5,-0.6,-1.4,-0.1,-2c0.6,-0.6,1.5,-0.6,2.1,-0.1Z" class="g7_13" />
<path fill-rule="evenodd" d="M229,942.3c0.8,0,1.5,0.7,1.5,1.5c0,0.7,-0.7,1.4,-1.5,1.4c-0.7,0,-1.4,-0.7,-1.4,-1.4c0,-0.8,0.7,-1.5,1.4,-1.5Z" class="g3_13" />
<path d="M229,942.3c0.8,0,1.5,0.7,1.5,1.5c0,0.7,-0.7,1.4,-1.5,1.4c-0.7,0,-1.4,-0.7,-1.4,-1.4c0,-0.8,0.7,-1.5,1.4,-1.5Z" class="g7_13" />
<path d="M229,933.4c5.8,0,10.4,4.6,10.4,10.4c0,5.7,-4.6,10.3,-10.4,10.3c-5.7,0,-10.3,-4.6,-10.3,-10.3c0,-5.8,4.6,-10.4,10.3,-10.4Z" class="g7_13" />
<path d="M229.3,943.8l21.4,0" class="g7_13" />
<path fill-rule="evenodd" d="M247,940.3c1.4,2.5,1.4,4.8,0.1,6.9c2,-1.9,4.7,-2.9,8,-3.5c-3.1,-0.3,-5.8,-1.4,-8.1,-3.4Z" class="g3_13" />
<path d="M217.8,932.5c1.5,-1.4,3.2,-2.6,5.1,-3.4c1.9,-0.8,4,-1.2,6.1,-1.2c2.2,0,4.3,0.4,6.2,1.2c1.9,0.8,3.6,2,5.1,3.4" class="g7_13" />
<path fill-rule="evenodd" d="M240.1,927.5c-0.8,2.7,-2.4,4.4,-4.9,4.9c2.8,0.1,5.5,1.3,8.2,3.2c-2,-2.4,-3.1,-5.1,-3.3,-8.1Z" class="g3_13" />
<path d="M305.1,866.8c0.5,-2,1.3,-3.9,2.5,-5.5c1.2,-1.7,2.7,-3.2,4.6,-4.4c1.8,-1.1,3.9,-1.9,5.9,-2.2c2,-0.3,4.1,-0.2,6.1,0.2" class="g7_13" />
<path fill-rule="evenodd" d="M321.4,850.7c0.7,2.8,0.2,5.1,-1.6,6.8c2.5,-1.4,5.4,-1.8,8.7,-1.6c-3,-1,-5.4,-2.7,-7.1,-5.2Z" class="g3_13" />
<path d="M688.1,802.7l-73.1,0" class="g9_13" />
<path d="M685.8,802.7l4.8,-5.8m-74.1,5.8l4.8,-5.8m60.1,5.8l4.8,-5.8m-9.1,5.8l4.8,-5.8m-9.1,5.8l4.8,-5.8m-9.2,5.8l4.8,-5.8m-9.1,5.8l4.8,-5.8m-9.1,5.8l4.8,-5.8m-9.2,5.8l4.9,-5.8m-9.2,5.8l4.8,-5.8m-9.1,5.8l4.8,-5.8m-9.1,5.8l4.8,-5.8m-9.2,5.8l4.8,-5.8m-9.1,5.8l4.8,-5.8m-9.1,5.8l4.8,-5.8m-9.2,5.8l4.8,-5.8m-9.1,5.8l4.8,-5.8" class="g10_13" />
<path d="M687.8,802.6l0,73.1" class="g9_13" />
<path d="M687.8,804.9l5.7,-4.8m-5.7,74.1l5.7,-4.8m-5.7,-60.2l5.7,-4.8m-5.7,9.2l5.7,-4.8m-5.7,9.1l5.7,-4.8m-5.7,9.1l5.7,-4.8m-5.7,9.2l5.7,-4.8m-5.7,9.1l5.7,-4.8m-5.7,9.1l5.7,-4.8m-5.7,9.1l5.7,-4.8m-5.7,9.2l5.7,-4.8m-5.7,9.1l5.7,-4.8m-5.7,9.1l5.7,-4.8m-5.7,9.2l5.7,-4.8m-5.7,9.1l5.7,-4.8m-5.7,9.1l5.7,-4.8m-5.7,9.2l5.7,-4.8" class="g10_13" />
<path fill-rule="evenodd" d="M616.2,844.6c0.9,0,1.6,0.8,1.6,1.7c0,0.9,-0.7,1.6,-1.6,1.6c-0.9,0,-1.7,-0.7,-1.7,-1.6c0,-0.9,0.8,-1.7,1.7,-1.7Z" class="g3_13" />
<path d="M616.2,844.6c0.9,0,1.6,0.8,1.6,1.7c0,0.9,-0.7,1.6,-1.6,1.6c-0.9,0,-1.7,-0.7,-1.7,-1.6c0,-0.9,0.8,-1.7,1.7,-1.7Z" class="g11_13" />
<path d="M616.4,846.3l20.3,0" class="g11_13" />
<path fill-rule="evenodd" d="M633,842.8c1.3,2.5,1.3,4.9,0,7c2.1,-1.9,4.8,-3,8.1,-3.5c-3.2,-0.3,-5.9,-1.4,-8.1,-3.5Z" class="g3_13" />
<path fill-rule="evenodd" d="M642.5,779.5c0.9,0,1.7,0.7,1.7,1.6c0,0.9,-0.8,1.7,-1.7,1.7c-0.9,0,-1.6,-0.8,-1.6,-1.7c0,-0.9,0.7,-1.6,1.6,-1.6Z" class="g3_13" />
<path d="M642.5,779.5c0.9,0,1.7,0.7,1.7,1.6c0,0.9,-0.8,1.7,-1.7,1.7c-0.9,0,-1.6,-0.8,-1.6,-1.7c0,-0.9,0.7,-1.6,1.6,-1.6Z" class="g11_13" />
<path fill-rule="evenodd" d="M703.8,779.5c0.9,0,1.6,0.7,1.6,1.6c0,0.9,-0.7,1.7,-1.6,1.7c-0.9,0,-1.7,-0.8,-1.7,-1.7c0,-0.9,0.8,-1.6,1.7,-1.6Z" class="g3_13" />
<path d="M703.8,779.5c0.9,0,1.6,0.7,1.6,1.6c0,0.9,-0.7,1.7,-1.6,1.7c-0.9,0,-1.7,-0.8,-1.7,-1.7c0,-0.9,0.8,-1.6,1.7,-1.6Z" class="g11_13" />
<path fill-rule="evenodd" d="M703.8,849.2c0.9,0,1.6,0.7,1.6,1.6c0,1,-0.7,1.7,-1.6,1.7c-0.9,0,-1.7,-0.7,-1.7,-1.7c0,-0.9,0.8,-1.6,1.7,-1.6Z" class="g3_13" />
<path d="M703.8,849.2c0.9,0,1.6,0.7,1.6,1.6c0,1,-0.7,1.7,-1.6,1.7c-0.9,0,-1.7,-0.7,-1.7,-1.7c0,-0.9,0.8,-1.6,1.7,-1.6Z" class="g11_13" />
<path d="M731.8,206.6l0,113.9l-114,0l114,-113.9Z" class="g9_13" />
<path d="M640.8,256.9c9.3,0,16.8,7.6,16.8,16.9c0,9.2,-7.5,16.8,-16.8,16.8C631.5,290.6,624,283,624,273.8c0,-9.3,7.5,-16.9,16.8,-16.9Z" class="g9_13" />
<path d="M617,272.6c0.4,-7.4,4.1,-14.2,10.2,-18.4" class="g9_13" />
<path fill-rule="evenodd" d="M629.1,252.9l-7,1.4c1.8,1,2.9,2.5,3.2,4.6l3.8,-6Z" class="g3_13" />
<path d="M619.6,295L662,252.5" class="g9_13" />
<path fill-rule="evenodd" d="M618,296.6l2.6,-6.6c0.7,1.9,2,3.3,4,3.9l-6.6,2.7Z" class="g3_13" />
<path fill-rule="evenodd" d="M663.6,250.9l-6.6,2.7c2,0.6,3.3,2,4,3.9l2.6,-6.6Z" class="g3_13" />
<path d="M670.5,267.8l6.6,-6.5" class="g9_13" />
<path fill-rule="evenodd" d="M678.7,259.7l-6.6,2.6c1.9,0.7,3.3,2,3.9,4l2.7,-6.6Z" class="g3_13" />
<path d="M617.3,1021.4l70.1,0l0,70.1" class="g9_13" />
<path d="M652.4,1056.3l73,-73" class="g12_13" />
<path d="M725.1,1059.2L648.9,983" class="g12_13" />
<path d="M670.4,1004.5l-3.4,-3.4" class="g12_13" />
<path fill-rule="evenodd" d="M672,1006.1l-6.6,-2.6c1.9,-0.7,3.3,-2,3.9,-4l2.7,6.6Z" class="g3_13" />
<path d="M701.3,1035.5l3.4,3.4" class="g12_13" />
<path fill-rule="evenodd" d="M699.7,1033.9l6.6,2.6c-2,0.7,-3.3,2,-3.9,4l-2.7,-6.6Z" class="g3_13" />
<path d="M704,1004.5l3.4,-3.4" class="g12_13" />
<path fill-rule="evenodd" d="M702.4,1006.1l2.7,-6.6c0.6,2,2,3.3,3.9,4l-6.6,2.6Z" class="g3_13" />
<path fill-rule="evenodd" d="M648.9,981.5c0.7,0,1.4,0.7,1.4,1.4c0,0.8,-0.7,1.4,-1.4,1.4c-0.8,0,-1.5,-0.6,-1.5,-1.4c0,-0.7,0.7,-1.4,1.5,-1.4Z" class="g3_13" />
<path d="M648.9,981.5c0.7,0,1.4,0.7,1.4,1.4c0,0.8,-0.7,1.4,-1.4,1.4c-0.8,0,-1.5,-0.6,-1.5,-1.4c0,-0.7,0.7,-1.4,1.5,-1.4Z" class="g9_13" />
<path fill-rule="evenodd" d="M725.5,981.5c0.8,0,1.4,0.7,1.4,1.4c0,0.8,-0.6,1.4,-1.4,1.4c-0.8,0,-1.4,-0.6,-1.4,-1.4c0,-0.7,0.6,-1.4,1.4,-1.4Z" class="g3_13" />
<path d="M725.5,981.5c0.8,0,1.4,0.7,1.4,1.4c0,0.8,-0.6,1.4,-1.4,1.4c-0.8,0,-1.4,-0.6,-1.4,-1.4c0,-0.7,0.6,-1.4,1.4,-1.4Z" class="g9_13" />
<path fill-rule="evenodd" d="M725.5,1058.1c0.8,0,1.4,0.7,1.4,1.5c0,0.7,-0.6,1.4,-1.4,1.4c-0.8,0,-1.4,-0.7,-1.4,-1.4c0,-0.8,0.6,-1.5,1.4,-1.5Z" class="g3_13" />
<path d="M725.5,1058.1c0.8,0,1.4,0.7,1.4,1.5c0,0.7,-0.6,1.4,-1.4,1.4c-0.8,0,-1.4,-0.7,-1.4,-1.4c0,-0.8,0.6,-1.5,1.4,-1.5Z" class="g9_13" />
<path fill-rule="evenodd" d="M652.4,1055c0.8,0,1.4,0.7,1.4,1.4c0,0.8,-0.6,1.5,-1.4,1.5c-0.8,0,-1.4,-0.7,-1.4,-1.5c0,-0.7,0.6,-1.4,1.4,-1.4Z" class="g3_13" />
<path d="M652.4,1055c0.8,0,1.4,0.7,1.4,1.4c0,0.8,-0.6,1.5,-1.4,1.5c-0.8,0,-1.4,-0.7,-1.4,-1.5c0,-0.7,0.6,-1.4,1.4,-1.4Z" class="g9_13" />
<path d="M675.8,1032.5l-3.4,3.4" class="g12_13" />
<path fill-rule="evenodd" d="M677.4,1030.9l-2.6,6.6c-0.7,-2,-2,-3.3,-4,-4l6.6,-2.6Z" class="g3_13" />
<path d="M354.8,636l0,84.6l-97,0" class="g12_13" />
<path d="M254,720.6l-73.8,0" class="g9_13" />
<path d="M356.6,617.9c-3.8,69.9,-41.4,97.6,-105.2,102.7" class="g13_13" />
<path d="M343.3,614.2c6.9,0,12.5,5.6,12.5,12.5c0,6.9,-5.6,12.5,-12.5,12.5c-7,0,-12.6,-5.6,-12.6,-12.5c0,-6.9,5.6,-12.5,12.6,-12.5Z" class="g9_13" />
<path fill-rule="evenodd" d="M343.3,625c0.9,0,1.6,0.8,1.6,1.7c0,0.9,-0.7,1.6,-1.6,1.6c-0.9,0,-1.7,-0.7,-1.7,-1.6c0,-0.9,0.8,-1.7,1.7,-1.7Z" class="g3_13" />
<path d="M343.3,625c0.9,0,1.6,0.8,1.6,1.7c0,0.9,-0.7,1.6,-1.6,1.6c-0.9,0,-1.7,-0.7,-1.7,-1.6c0,-0.9,0.8,-1.7,1.7,-1.7Z" class="g9_13" />
<path fill-rule="evenodd" d="M234.7,707.1c0.9,0,1.7,0.7,1.7,1.6c0,1,-0.8,1.7,-1.7,1.7c-0.9,0,-1.6,-0.7,-1.6,-1.7c0,-0.9,0.7,-1.6,1.6,-1.6Z" class="g3_13" />
<path d="M234.7,707.1c0.9,0,1.7,0.7,1.7,1.6c0,1,-0.8,1.7,-1.7,1.7c-0.9,0,-1.6,-0.7,-1.6,-1.7c0,-0.9,0.7,-1.6,1.6,-1.6Z" class="g9_13" />
<path d="M234.7,696.8c6.6,0,11.9,5.4,11.9,11.9c0,6.6,-5.3,12,-11.9,12c-6.6,0,-11.9,-5.4,-11.9,-12c0,-6.5,5.3,-11.9,11.9,-11.9Z" class="g9_13" />
<path d="M235,708.7l25.3,0" class="g9_13" />
<path fill-rule="evenodd" d="M256.6,705.3c1.4,2.5,1.4,4.8,0.1,6.9c2,-1.9,4.7,-3,8,-3.5c-3.1,-0.3,-5.8,-1.4,-8.1,-3.4Z" class="g3_13" />
<path d="M221.8,695.8c1.6,-1.6,3.6,-3,5.8,-3.9c2.2,-0.9,4.6,-1.4,7.1,-1.4c2.5,0,4.9,0.5,7.1,1.4c2.2,0.9,4.2,2.3,5.8,3.9" class="g9_13" />
<path fill-rule="evenodd" d="M247.5,690.8c-0.9,2.7,-2.5,4.3,-4.9,4.9c2.8,0.1,5.5,1.3,8.2,3.2c-2,-2.4,-3.2,-5.1,-3.3,-8.1Z" class="g3_13" />
<path d="M134.8,507.5c-0.4,0,-0.8,0.2,-1,0.4c-0.3,0.3,-0.4,0.6,-0.4,1.1c0,0.4,0.1,0.7,0.4,1c0.2,0.3,0.6,0.4,1,0.4c0.4,0,0.7,-0.1,1,-0.4c0.3,-0.3,0.4,-0.6,0.4,-1c0,-0.5,-0.1,-0.8,-0.4,-1.1c-0.3,-0.2,-0.6,-0.4,-1,-0.4Z" class="g3_13" />
<path d="M141.4,494.8c0,-1.9,-0.7,-3.4,-2,-4.7c-1.2,-1.3,-3.1,-2,-5.5,-2l0,1.8c1.8,0,3.4,0.5,4.3,1.4c1,1,1.5,2.1,1.5,3.5c0,1.3,-0.5,2.5,-1.5,3.4l-2.3,2.2c-1.3,1.3,-2,2.8,-2,4.7c0,0.2,0.1,0.4,0.3,0.6c0.1,0.1,0.3,0.2,0.6,0.2c0.2,0,0.4,-0.1,0.6,-0.2c0.2,-0.2,0.3,-0.4,0.3,-0.6c0,-1.4,0.4,-2.5,1.4,-3.5l2.3,-2.2c1.3,-1.3,2,-2.8,2,-4.6Z" class="g3_13" />
<path d="M125.5,503.2c-0.1,-0.1,-0.1,-0.2,-0.1,-0.3c0,-0.1,0,-0.2,0.1,-0.3l1.7,-1.7c0,0,0.1,-0.1,0.3,-0.1c0.1,0,0.2,0.1,0.3,0.1c0.1,0.1,0.1,0.2,0.1,0.4c0,0.1,0,0.2,-0.1,0.3l-1.7,1.6L126,503.1l0.1,0.1c0,0.1,-0.2,0.2,-0.3,0.2c-0.1,0,-0.2,-0.1,-0.3,-0.2Zm0.3,-0.3l0,0Zm0.8,-0.8l0,0l-0.8,0.8l0.8,-0.8Z" class="g14_13" />
<path d="M123.1,500.7c0,-0.1,0,-0.2,0,-0.3c0,-0.1,0,-0.3,0.1,-0.3l1.9,-1.4c0.1,-0.1,0.2,-0.1,0.3,-0.1c0.1,0,0.2,0,0.3,0.2c0.1,0,0.1,0.1,0.1,0.2c0,0.1,-0.1,0.3,-0.2,0.4l-1.9,1.4l-0.1,-0.2l0.1,0.2c0,0,-0.1,0,-0.2,0c-0.1,0,-0.3,0,-0.4,-0.1Zm0.4,-0.3l0,0Zm0,0l0,0Zm1.9,-1.4l0,0Z" class="g14_13" />
<path d="M121.3,496.9c0,-0.1,0,-0.1,0,-0.1c0,-0.2,0.2,-0.4,0.4,-0.4l2.3,-0.5l0.1,0c0.2,0,0.3,0.1,0.4,0.3c0,0,0,0.1,0,0.1c0,0.2,-0.1,0.4,-0.4,0.4l-2.2,0.5L121.8,497l0.1,0.2c-0.1,0,-0.1,0,-0.1,0c-0.2,0,-0.4,-0.1,-0.5,-0.3Zm0.5,-0.1l0,0Zm2.3,-0.5l0,0Z" class="g14_13" />
<path d="M124,493.7l-2.3,-0.4l0.1,-0.3l-0.1,0.3c-0.2,-0.1,-0.3,-0.2,-0.3,-0.4c0,-0.1,0,-0.1,0,-0.1c0,-0.2,0.2,-0.4,0.4,-0.4l0.1,0l2.3,0.4c0.2,0,0.3,0.2,0.3,0.4c0,0.1,0,0.1,0,0.1c0,0.2,-0.2,0.4,-0.4,0.4l-0.1,0Zm0.1,-0.5l0,0Zm-2.4,0.1l0,0Z" class="g14_13" />
<path d="M124.8,490.9l-2,-1.2l0.1,-0.2l-0.1,0.2c-0.2,-0.1,-0.3,-0.2,-0.3,-0.4c0,-0.1,0.1,-0.1,0.1,-0.2c0.1,-0.2,0.2,-0.2,0.4,-0.2c0.1,0,0.1,0,0.2,0l2,1.2c0.2,0.1,0.2,0.2,0.2,0.4c0,0,0,0.1,0,0.2c-0.1,0.1,-0.2,0.2,-0.4,0.2c-0.1,0,-0.1,0,-0.2,0Zm0.2,-0.4l0,0Zm-1.4,-0.8l0,0L123,489.3l0.6,0.4Z" class="g14_13" />
<path d="M126.9,488.7c-0.1,0,-0.3,-0.1,-0.3,-0.2l-1.4,-1.9l0.2,-0.1l-0.2,0.1c0,-0.1,0,-0.2,0,-0.3c0,-0.1,0,-0.2,0.2,-0.3c0,-0.1,0.1,-0.1,0.2,-0.1c0.1,0,0.3,0.1,0.4,0.2l1.3,1.9c0,0.1,0.1,0.2,0.1,0.3c0,0.1,-0.1,0.2,-0.2,0.3c-0.1,0.1,-0.2,0.1,-0.3,0.1Zm0,-0.4l0,0l-1.3,-2l1.3,2Zm-1.3,-2l0,0Z" class="g14_13" />
<path d="M129.3,487.2l-0.5,-2.3l0,-0.1c0,-0.2,0.2,-0.3,0.4,-0.4l0.1,0c0.2,0,0.3,0.1,0.4,0.4l0.5,2.2c0,0.1,0,0.1,0,0.1c0,0.2,-0.2,0.4,-0.4,0.5l-0.1,0c-0.2,0,-0.3,-0.2,-0.4,-0.4Zm0.4,-0.1l0,0Zm0,0l0,0ZM129,484.9l0,0l0.3,-0.1l-0.1,0l-0.2,0.1Z" class="g14_13" />
<path d="M125.2,494.8c0,-1.9,0.7,-3.4,2,-4.7c1.3,-1.3,3.2,-2,5.6,-2l0,1.8c-1.8,0,-3.5,0.5,-4.4,1.4c-1,1,-1.4,2.1,-1.4,3.5c0,1.3,0.4,2.5,1.4,3.4l2.4,2.2c1.3,1.3,1.9,2.8,1.9,4.7c0,0.2,-0.1,0.4,-0.2,0.6c-0.2,0.1,-0.4,0.2,-0.7,0.2c-0.2,0,-0.4,-0.1,-0.6,-0.2c-0.1,-0.2,-0.2,-0.4,-0.2,-0.6c0,-1.4,-0.5,-2.5,-1.4,-3.5l-2.4,-2.2c-1.3,-1.3,-2,-2.8,-2,-4.6Z" class="g14_13" />
<path d="M132.5,508.8c0,0.2,-0.1,0.3,-0.3,0.3l-1,0c-0.2,0,-0.4,-0.1,-0.4,-0.3l0,-0.3c0,-0.2,0.2,-0.3,0.4,-0.3l1,0c0.2,0,0.3,0.1,0.3,0.3l0,0.3Z" class="g14_13" />
<path d="M132.5,507.5c0,0.1,-0.1,0.3,-0.3,0.3l-1,0c-0.2,0,-0.4,-0.2,-0.4,-0.3l0,-0.3c0,-0.2,0.2,-0.3,0.4,-0.3l1,0c0.2,0,0.3,0.1,0.3,0.3l0,0.3Z" class="g14_13" />
<path d="M132.5,510.1c0,0.1,-0.1,0.3,-0.3,0.3l-1,0c-0.2,0,-0.4,-0.2,-0.4,-0.3l0,-0.3c0,-0.2,0.2,-0.3,0.4,-0.3l1,0c0.2,0,0.3,0.1,0.3,0.3l0,0.3Z" class="g14_13" />
<path d="M134.9,512.4l0,89.9l19.6,0.3" class="g15_13" />
<path d="M738.5,552.8l2.5,-3.5" class="g16_13" />
<path d="M741.3,552.8l2.4,-3.5" class="g16_13" />
<path d="M744,552.8l2.4,-3.5" class="g16_13" />
<path d="M746.7,552.8l2.5,-3.5" class="g16_13" />
<path d="M749.4,552.8l2.5,-3.5" class="g16_13" />
<path d="M752.1,552.8l2.5,-3.5" class="g16_13" />
<path d="M754.8,552.8l2.5,-3.5" class="g16_13" />
<path d="M757.6,552.8l2.4,-3.5" class="g16_13" />
<path d="M760.3,552.8l2.4,-3.5" class="g16_13" />
<path d="M763,552.8l2.5,-3.5" class="g16_13" />
<path d="M765.7,552.8l2.5,-3.5" class="g16_13" />
<path d="M768.4,552.8l2.5,-3.5" class="g16_13" />
<path d="M771.1,552.8l2.5,-3.5" class="g16_13" />
<path d="M773.9,552.8l2.4,-3.5" class="g16_13" />
<path d="M776.6,552.8l2.4,-3.5" class="g16_13" />
<path d="M779.3,552.8l2.5,-3.5" class="g16_13" />
<path d="M782,552.8l2.5,-3.5" class="g16_13" />
<path d="M784.7,552.8l2.5,-3.5" class="g16_13" />
<path d="M787.4,552.8l2.5,-3.5" class="g16_13" />
<path d="M790.1,552.8l2.5,-3.5" class="g16_13" />
<path d="M792.9,552.8l2.4,-3.5" class="g16_13" />
<path d="M795.6,552.8l2.4,-3.5" class="g16_13" />
<path d="M798.3,552.8l2.5,-3.5" class="g16_13" />
<path d="M799.5,552.8l3.6,-2.3" class="g16_13" />
<path d="M799.5,555.6l3.6,-2.3" class="g16_13" />
<path d="M799.5,558.3l3.6,-2.3" class="g16_13" />
<path d="M799.5,561l3.6,-2.3" class="g16_13" />
<path d="M799.5,563.7l3.6,-2.3" class="g16_13" />
<path d="M799.5,566.4l3.6,-2.3" class="g16_13" />
<path d="M799.5,569.1l3.6,-2.3" class="g16_13" />
<path d="M799.5,571.9l3.6,-2.3" class="g16_13" />
<path d="M799.5,574.6l3.6,-2.3" class="g16_13" />
<path d="M799.5,577.3l3.6,-2.3" class="g16_13" />
<path d="M799.5,580l3.6,-2.3" class="g16_13" />
<path d="M799.5,582.7l3.6,-2.3" class="g16_13" />
<path d="M799.5,585.4l3.6,-2.3" class="g16_13" />
<path d="M799.5,588.2l3.6,-2.4" class="g16_13" />
<path d="M799.5,590.9l3.6,-2.3" class="g16_13" />
<path d="M799.5,593.6l3.6,-2.3" class="g16_13" />
<path d="M799.5,596.3l3.6,-2.3" class="g16_13" />
<path d="M799.5,599l3.6,-2.3" class="g16_13" />
<path d="M799.5,601.7l3.6,-2.3" class="g16_13" />
<path d="M799.5,604.5l3.6,-2.4" class="g16_13" />
<path d="M799.5,607.2l3.6,-2.3" class="g16_13" />
<path d="M799.5,609.9l3.6,-2.3" class="g16_13" />
<path d="M799.5,612.6l3.6,-2.3" class="g16_13" />
<g clip-path="url(#c0_13)">
<path d="M738.2,552.8l61.4,0l0,61.4" class="g17_13" />
</g>
<path fill-rule="evenodd" d="M769.2,594c0.7,0,1.3,0.6,1.3,1.3c0,0.6,-0.6,1.2,-1.3,1.2c-0.7,0,-1.2,-0.6,-1.2,-1.2c0,-0.7,0.5,-1.3,1.2,-1.3Z" class="g3_13" />
<path d="M769.2,594c0.7,0,1.3,0.6,1.3,1.3c0,0.6,-0.6,1.2,-1.3,1.2c-0.7,0,-1.2,-0.6,-1.2,-1.2c0,-0.7,0.5,-1.3,1.2,-1.3Z" class="g17_13" />
<path fill-rule="evenodd" d="M842.6,580.7c0.7,0,1.3,0.6,1.3,1.2c0,0.7,-0.6,1.3,-1.3,1.3c-0.6,0,-1.2,-0.6,-1.2,-1.3c0,-0.6,0.6,-1.2,1.2,-1.2Z" class="g3_13" />
<path d="M842.6,580.7c0.7,0,1.3,0.6,1.3,1.2c0,0.7,-0.6,1.3,-1.3,1.3c-0.6,0,-1.2,-0.6,-1.2,-1.3c0,-0.6,0.6,-1.2,1.2,-1.2Z" class="g17_13" />
<path fill-rule="evenodd" d="M834.6,513.2c0.7,0,1.2,0.6,1.2,1.3c0,0.7,-0.5,1.2,-1.2,1.2c-0.7,0,-1.3,-0.5,-1.3,-1.2c0,-0.7,0.6,-1.3,1.3,-1.3Z" class="g3_13" />
<path d="M834.6,513.2c0.7,0,1.2,0.6,1.2,1.3c0,0.7,-0.5,1.2,-1.2,1.2c-0.7,0,-1.3,-0.5,-1.3,-1.2c0,-0.7,0.6,-1.3,1.3,-1.3Z" class="g17_13" />
<path fill-rule="evenodd" d="M770,509c0.7,0,1.2,0.6,1.2,1.3c0,0.7,-0.5,1.2,-1.2,1.2c-0.7,0,-1.3,-0.5,-1.3,-1.2c0,-0.7,0.6,-1.3,1.3,-1.3Z" class="g3_13" />
<path d="M770,509c0.7,0,1.2,0.6,1.2,1.3c0,0.7,-0.5,1.2,-1.2,1.2c-0.7,0,-1.3,-0.5,-1.3,-1.2c0,-0.7,0.6,-1.3,1.3,-1.3Z" class="g17_13" />
<path d="M799.5,500.9c28.7,0,52,23.3,52,52c0,28.7,-23.3,52,-52,52c-28.7,0,-52,-23.3,-52,-52c0,-28.7,23.3,-52,52,-52Z" class="g18_13" />
<path d="M769.2,595.3l18.5,0" class="g17_13" />
<path fill-rule="evenodd" d="M789.9,595.3l-6.5,-2.8c0.9,1.8,0.9,3.7,0,5.6l6.5,-2.8Z" class="g3_13" />
<path d="M770.5,510.3l18.5,0" class="g17_13" />
<path fill-rule="evenodd" d="M791.2,510.3l-6.5,-2.8c0.9,1.8,0.9,3.7,0,5.6l6.5,-2.8Z" class="g3_13" />
<path d="M842.6,581.9l-18.5,0" class="g17_13" />
<path fill-rule="evenodd" d="M821.9,581.9l6.5,2.8c-0.9,-1.8,-0.9,-3.7,0,-5.6l-6.5,2.8Z" class="g3_13" />
<path d="M834.8,514.4l-18.4,0" class="g17_13" />
<path fill-rule="evenodd" d="M814.1,514.4l6.5,2.8c-0.9,-1.9,-0.9,-3.8,0,-5.6l-6.5,2.8Z" class="g3_13" />
<path d="M547.8,523.2c-0.4,0,-0.7,0.2,-1,0.4c-0.3,0.3,-0.4,0.6,-0.4,1c0,0.4,0.1,0.8,0.4,1.1c0.3,0.2,0.6,0.4,1,0.4c0.4,0,0.8,-0.2,1.1,-0.4c0.2,-0.3,0.4,-0.7,0.4,-1.1c0,-0.4,-0.2,-0.7,-0.4,-1c-0.3,-0.2,-0.7,-0.4,-1.1,-0.4Z" class="g3_13" />
<path d="M554.4,510.4c0,-1.8,-0.6,-3.3,-1.9,-4.6c-1.3,-1.3,-3.2,-2,-5.6,-2l0,1.8c1.8,0,3.4,0.5,4.4,1.4c0.9,0.9,1.4,2.1,1.4,3.4c0,1.4,-0.5,2.6,-1.4,3.5l-2.4,2.2c-1.3,1.3,-1.9,2.8,-1.9,4.7c0,0.2,0.1,0.4,0.2,0.6c0.2,0.1,0.4,0.2,0.6,0.2c0.3,0,0.5,-0.1,0.6,-0.2c0.2,-0.2,0.3,-0.4,0.3,-0.6c0,-1.4,0.5,-2.5,1.4,-3.5l2.4,-2.2c1.3,-1.3,1.9,-2.8,1.9,-4.7Z" class="g3_13" />
<path d="M538.6,518.9c-0.1,-0.1,-0.2,-0.2,-0.2,-0.3c0,-0.1,0.1,-0.2,0.2,-0.3l1.6,-1.7c0.1,0,0.2,-0.1,0.3,-0.1c0.1,0,0.2,0,0.3,0.1c0.1,0.1,0.1,0.2,0.1,0.3c0,0.2,0,0.3,-0.1,0.3l-1.6,1.7L539,518.8l0.2,0.1c-0.1,0.1,-0.2,0.1,-0.3,0.1c-0.1,0,-0.2,0,-0.3,-0.1Zm0.3,-0.3l0,0Zm0.8,-0.8l0,0l-0.8,0.8l0.8,-0.8Z" class="g14_13" />
<path d="M536.2,516.4c-0.1,-0.1,-0.1,-0.2,-0.1,-0.3c0,-0.1,0.1,-0.3,0.2,-0.4l1.9,-1.3c0,-0.1,0.1,-0.1,0.2,-0.1c0.1,0,0.3,0,0.4,0.1c0,0.1,0.1,0.2,0.1,0.3c0,0.1,-0.1,0.3,-0.2,0.4l-1.9,1.3l-0.1,-0.1l0.1,0.1c-0.1,0.1,-0.2,0.1,-0.3,0.1c-0.1,0,-0.2,0,-0.3,-0.1Zm0.3,-0.3l0,0Zm0,0l0,0Zm1.9,-1.4l0,0Z" class="g14_13" />
<path d="M534.4,512.6c0,-0.1,0,-0.1,0,-0.1c0,-0.2,0.1,-0.4,0.3,-0.4l2.3,-0.5c0,0,0.1,0,0.1,0c0.2,0,0.4,0.1,0.4,0.3c0,0,0,0.1,0,0.1c0,0.2,-0.1,0.4,-0.3,0.4l-2.3,0.5l0,-0.2l0,0.2l-0.1,0c-0.2,0,-0.4,-0.1,-0.4,-0.3Zm0.4,-0.1l0,0Zm2.3,-0.5l0,0Z" class="g14_13" />
<path d="M537.1,509.3L534.8,509l0,-0.3l0,0.3c-0.2,-0.1,-0.4,-0.2,-0.4,-0.5c0.1,-0.2,0.2,-0.4,0.4,-0.4c0.1,0,0.1,0,0.1,0l2.3,0.4c0.2,0,0.4,0.2,0.4,0.4c0,0,0,0.1,0,0.1c-0.1,0.2,-0.2,0.4,-0.4,0.4c-0.1,0,-0.1,0,-0.1,-0.1Zm0.1,-0.4l0,0Zm-2.4,0.1l0,0Z" class="g14_13" />
<path d="M537.8,506.5l-2,-1.1l0.1,-0.2l-0.1,0.2c-0.1,-0.1,-0.2,-0.3,-0.2,-0.4c0,-0.1,0,-0.2,0.1,-0.2c0,-0.2,0.2,-0.2,0.3,-0.2c0.1,0,0.2,0,0.3,0l2,1.2c0.1,0.1,0.2,0.2,0.2,0.4c0,0,0,0.1,-0.1,0.2c0,0.1,-0.2,0.2,-0.3,0.2c-0.1,0,-0.2,0,-0.3,-0.1Zm0.3,-0.3l0,0Zm-1.5,-0.9l0,0L536,505l0.6,0.3Z" class="g14_13" />
<path d="M540,504.4c-0.2,0,-0.3,-0.1,-0.4,-0.2l-1.3,-1.9l0.2,-0.1l-0.2,0.1c-0.1,-0.1,-0.1,-0.2,-0.1,-0.3c0,-0.1,0.1,-0.2,0.2,-0.3c0.1,-0.1,0.2,-0.1,0.2,-0.1c0.2,0,0.3,0.1,0.4,0.2l1.3,1.9c0.1,0.1,0.1,0.2,0.1,0.3c0,0.1,-0.1,0.2,-0.2,0.3c-0.1,0.1,-0.1,0.1,-0.2,0.1Zm0,-0.4l0,0l-1.3,-2l1.3,2Zm-1.3,-2l-0.1,0l0.1,0Z" class="g14_13" />
<path d="M542.4,502.9l-0.5,-2.3l0,-0.1c0,-0.2,0.1,-0.3,0.3,-0.4c0,0,0.1,0,0.1,0c0.2,0,0.4,0.1,0.4,0.3l0.5,2.3c0,0.1,0,0.1,0,0.1c0,0.2,-0.1,0.4,-0.3,0.4c0,0.1,-0.1,0.1,-0.1,0.1c-0.2,0,-0.4,-0.2,-0.4,-0.4Zm0.4,-0.1l0,0Zm0,0l0,0Zm-0.7,-2.2l0,0l0.2,-0.1l-0.2,0.1Z" class="g14_13" />
<path d="M538.3,510.4c0,-1.8,0.6,-3.3,1.9,-4.6c1.3,-1.3,3.2,-2,5.6,-2l0,1.8c-1.8,0,-3.4,0.5,-4.3,1.4c-1,0.9,-1.5,2.1,-1.5,3.4c0,1.4,0.5,2.6,1.5,3.5l2.3,2.2c1.3,1.3,2,2.8,2,4.7c0,0.2,-0.1,0.4,-0.3,0.6c-0.2,0.1,-0.4,0.2,-0.6,0.2c-0.2,0,-0.4,-0.1,-0.6,-0.2C544.1,521.2,544,521,544,520.8c0,-1.4,-0.4,-2.5,-1.4,-3.5l-2.4,-2.2c-1.3,-1.3,-1.9,-2.8,-1.9,-4.7Z" class="g14_13" />
<path d="M545.6,524.5c0,0.2,-0.2,0.3,-0.3,0.3l-1.1,0c-0.2,0,-0.3,-0.1,-0.3,-0.3l0,-0.3c0,-0.2,0.1,-0.3,0.3,-0.3l1.1,0c0.1,0,0.3,0.1,0.3,0.3l0,0.3Z" class="g14_13" />
<path d="M545.6,523.2c0,0.1,-0.2,0.3,-0.3,0.3l-1.1,0c-0.2,0,-0.3,-0.2,-0.3,-0.3l0,-0.3c0,-0.2,0.1,-0.3,0.3,-0.3l1.1,0c0.1,0,0.3,0.1,0.3,0.3l0,0.3Z" class="g14_13" />
<path d="M545.6,525.8c0,0.1,-0.2,0.3,-0.3,0.3l-1.1,0c-0.2,0,-0.3,-0.2,-0.3,-0.3l0,-0.3c0,-0.2,0.1,-0.3,0.3,-0.3l1.1,0c0.1,0,0.3,0.1,0.3,0.3l0,0.3Z" class="g14_13" />
<path d="M546.4,528.7l0,85.7L566,614.3" class="g15_13" />
<path d="M716.1,359.3l0,114l-113.9,0l113.9,-114Z" class="g17_13" />
<path d="M670.9,364.1c9.3,0,16.8,7.5,16.8,16.8c0,9.3,-7.5,16.8,-16.8,16.8c-9.3,0,-16.8,-7.5,-16.8,-16.8c0,-9.3,7.5,-16.8,16.8,-16.8Z" class="g17_13" />
<path d="M647.1,379.7c0.3,-7.4,4.1,-14.2,10.2,-18.4" class="g17_13" />
<path fill-rule="evenodd" d="M647,382l-2.5,-6.7c1.8,1,3.7,1.1,5.6,0.3L647,382Z" class="g3_13" />
<path d="M649.7,402.1l42.4,-42.4" class="g17_13" />
<path fill-rule="evenodd" d="M648.1,403.7l2.6,-6.6c0.7,2,2,3.3,4,4l-6.6,2.6Z" class="g3_13" />
<path fill-rule="evenodd" d="M693.7,358.1l-6.6,2.6c2,0.7,3.3,2,4,4l2.6,-6.6Z" class="g3_13" />
<path d="M700.6,375l6.5,-6.6" class="g17_13" />
<path fill-rule="evenodd" d="M708.8,366.8l-6.7,2.6c2,0.7,3.4,2,4,4l2.7,-6.6Z" class="g3_13" />
</svg>