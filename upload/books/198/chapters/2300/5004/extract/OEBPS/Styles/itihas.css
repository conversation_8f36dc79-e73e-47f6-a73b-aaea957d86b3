@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:bold;
	src : url("../Fonts/wcb.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:normal;
	src : url("../Fonts/wcn.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}

html, body {
font-family:"Walkman-Chanakya-905";
}

body {
font-size:120%;
line-height:150%;
padding:2%;
text-align:justify;
}

* {
margin:0;
padding:0;
}

.image {
text-align:center;
}

.chapterHeading {
font-size:160%;
margin-bottom:1%;
}

.chapterNumber {
	font-size: 125%;
	font-family: "Times New Roman", Times, serif;
}

.purple{
	background:#BDAEC6;
	padding: 15px;
font-size:0.9em;
line-height:120%;
}
	
.bold{
		font-size:120%;
		font-weight:bold;
	}


.center {
	text-align: center;
	font-family: "Times New Roman", Times, serif;
}

.excercise {
font-weight:bold;
margin:1% 0%;
}

.box{
background-color:rgba(3, 78, 162, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}
.brownac{
	background:rgb(220,197,157);
	margin-bottom:1%;
	padding:2%;
}

.activityBox{
background-color:#FF9900;
padding: 15px;
font-size:0.9em;
line-height:120%;
}

.newWordsBox{
background-color:rgba(252, 187, 118, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}

.englishMeaning{
	font-family:Arial, Helvetica, sans-serif;
	font-size:70%;
}


.left {
float:left;
}

.right {
float:right;	
}

/*Extra Add CSS*/

/* Chapter Name */
h2
{
color:#000;
font-size:1.5em;
background:#C0A172;
padding:15px;
}
/* Chapter number */
h4
{
color:#444;
font-size:1.3em;
}
h3, .BoxHead
{
	background:#0099CC;
	padding:10px;
	color:#fff;
	margin:0px;
}
.blue_box
{
	background:#0099CC;
	padding:10px;
	color:#fff;
	margin:0px;
	font-size:0.9em;
}
h3.numbers
{
	background:#E47297;
	padding:10px;
	margin:0px;
}
.Activity-Rule
{
	background:#FFC3CE;
	padding:10px;
	border-bottom:1px solid #E47297;
	margin:0px;
}
/* Concept Heading */
.ConceptHeading, .subjectHead
{
color:#e924aa;
font-size:1.3em;
font-weight:bold;
margin-top:20px;
}
/* Sub Heading */
.SubHeading, .sub-head, .chapterSubheading
{
font-size:1.1em;
font-weight:bold;
color:#8C489F;
}


.subHeading {
color:rgb(113,66,177);
font-size:125%;
font-weight:bold;
}
/* Sub Heading 2*/
.SubHeading2
{
color:#d1640f;
font-size:1em;
font-weight:bold;
}
.footer
{
display:none;
}
table
{
	width:100%;
}
table td
{
	padding:10px;
	border:1px solid #000;
	border-collapse:collapse;
	
}
.no_border table , .no_border table td
{
	border:0px solid #000;
}
/* Hightlisght Boxes */
.NewWordBox{
background-color:#F7E7BD;
padding: 15px;
margin: 15px;
font-size:0.9em;
line-height:120%;
}
.words
{
	background-color:#C5D0C1;
padding: 15px;
font-size:0.9em;
line-height:120%;
width:100%;
font-weight:bold;
}

.ActivityBox, .subheading{
background-color:#FFC3CE;
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.abox {
background-color:#FFCC99;
padding: 15px;
font-size:0.9em;
line-height:120%;
}
.box1,  .BoxText{
background-color:#cce0f0;
padding: 15px;
line-height:120%;
font-size:0.9em;
}
.box, .subheadingBlue, .bubble {
background-color:rgba(3, 78, 162, 0.4);
padding: 15px;
font-size:0.9em;
line-height:120%;
}
/* Hightlight Boxes Heading : CSS given directly to <b> tag*/
.NewWordBox b, .activityBox b, .box b 
{
	font-weight:bold;
	font-size:1.2em;
}
/* Hightlight Boxes Sub Heading */
.NewWordBox .Subheading, .activityBox .Subheading, .box .Subheading 
{
	font-weight:bold;
	font-size:1em;
}

#prelims .char-style-override-25, #prelims .para-style-override-24
{
	font-weight:bold;
}
#prelims .heading
{
	font-size: 1.667em; 
	color: rgb(236, 0, 140); 
	font-size: 1.67em; 
	font-weight: bold;
}
#prelims .char-style-override-10
{
	font-style:italic;
}
#prelims .subheading
{
	
	color:rgb(236, 0, 140); 
}
#prelims img
{
	width:100%;
}


.caption, .Figure-No-
{
font-style: italic;
font-size: 0.83em;
color: #4D4D4D;
text-align:center;
}
img {
	width:50%;
	max-width:100%;
	}
.liningbox {
	border:#21B6A8 1px solid;
	border-radius:15px;
padding:15px;
	}
.exerciseb {
	background-color:#177F75;
	padding: 15px;
font-size:0.9em;
line-height:120%;
	}


div.layout{
  text-align: center;
}
div.chapter_pos
{
text-align: center;
width: 96%;
position:absolute;
top:30%;
font-weight:bold;
font-size:28px;
color:#FFFFFF;
}
div.chapter_pos div
{
background:#993300;
padding:10px;
width:40%;
margin:auto;
opacity:0.9;
}
div.chapter_pos div span
{
font-size:0.7em;
color:#FFF;
line-height:200%;
font-weight:normal;
}
.cover_img_small
{
width:50%;
}
@media only screen and (max-width: 767px) {
div.chapter_pos
{
top:15%;
font-size:1em;
}
div.chapter_pos div span
{
font-size:0.5em;
}
div.chapter_pos div
{
width:70%;
}
.cover_img_small
{
width:90%;
}
}