<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN"
  "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" xmlns:epub="http://www.idpf.org/2007/ops">
<head>
  <title>Mathematics</title>
  <link href="../Styles/mathematics.css" rel="stylesheet" type="text/css" />
</head>

<body>
  <div class="layout">
    <div class="chapter_pos">
      <div>
        Chapter 1<br />
        <span style="color: rgb(255, 255, 255); font-size: 28px; font-weight: bold;">SETS</span><br />
      </div>
    </div>
  </div>

  <p style="text-align: center;"><img alt="Cover Image" class="img_wid cover_img_small" src="../Images/Cover.png" title="Cover Image" /></p>
</body>
</html>
<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN"
  "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">

<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="" xmlns:xml="http://www.w3.org/XML/1998/namespace">
<head>
  <title>Chapter 1</title>
  <link href="../Styles/mathematics.css" rel="stylesheet" type="text/css" />
  
<style type="text/css">
.activityBox{
  background-color:rgba(206, 19, 55, 0.4);
  padding: 5px 5px 5px 5px;
  margin: 5px 5px 5px 5px;
  }
  .box{
  background-color:#C5EFFD;
  padding: 15px;
        font-size:0.9em;
        border:1px solid #00aeef;
  }
  .caption

  {

  font-style: italic;

  font-size: 0.83em;

  color: #4D4D4D;

  text-align:center;

  }
  h2
  {
  color:#fff;
  font-size:1.5em;
  background:#00aeef;
  padding:10px;
  }
  /* Chapter number */
  h4
  {
  color:#00aeef;
  font-size:1.3em;
  }
  .blue
  {
        color:#00aeef;
  }
</style>
</head>

<body id="MathSc">
  <div>
    <div>
      <h4 style="text-align: left;">Chapter 1</h4>

      <h2>SETS</h2>

      <div class="blue">
        <p class="calibre1"><i class="calibre5"><b class="calibre4">In these days of conflict between ancient and modern studies; there&nbsp;</b></i><i class="calibre5"><b class="calibre4">must surely be something to be said for a study which did not&nbsp;</b></i><i class="calibre5"><b class="calibre4">begin with Pythagoras and will not end with Einstein; but&nbsp;</b></i><i class="calibre5"><b class="calibre4">is the oldest and the youngest. — G.H. HARDY</b></i></p>
      </div>

      <p class="topicHeading">1.1 Introduction</p>

      <p class="calibre1">The concept of set serves as a fundamental part of the present day mathematics. Today this concept is being used in almost every branch of mathematics. Sets are used to define the concepts of relations and functions. The study of geometry, sequences, probability, etc. requires the knowledge of sets.</p>

      <p class="calibre1">The theory of sets was developed by German mathematician Georg Cantor (1845-1918). He first encountered sets while working on “problems on trigonometric series”. In this Chapter, we discuss some basic definitions&nbsp;and operations involving sets.</p>

      <p class="calibre1"><b class="calibre4">Georg Cantor</b></p>

      <p class="calibre1"><b class="calibre4">(1845-1918)</b></p>

      <p class="topicHeading">1.2 Sets and their Representations</p>

      <p class="calibre1">In everyday life, we often speak of collections of objects of a particular kind, such as, a pack of cards, a crowd of people, a cricket team, etc. In mathematics also, we come across collections, for example, of natural numbers, points, prime numbers, etc. More specially, we examine the following collections:</p>

      <p class="calibre1">(i) Odd natural numbers less than 10, i.e., 1, 3, 5, 7, 9</p>

      <p class="calibre1">(ii) The rivers of India</p>

      <p class="calibre1">(iii) The vowels in the English alphabet, namely, <i class="calibre5">a, e, i, o, u</i></p>

      <p class="calibre1">(iv) Various kinds of triangles</p>

      <p class="calibre1">(v) Prime factors of 210, namely, 2,3,5 and 7</p>

      <p class="calibre1">(vi) The solution of the equation: <i class="calibre5">x</i><sup>2</sup> – 5 <i class="calibre5">x</i> + 6 = 0, viz, 2 and 3.</p>

      <p class="calibre1">We note that each of the above example is a well-defined collection of objects in not to be republished</p>

      <p class="calibre1">the sense that we can definitely decide whether a given particular object belongs to a given collection or not. For example, we can say that the river Nile does not belong to the collection of rivers of India. On the other hand, the river Ganga does belong to this colleciton.</p>

      <p class="calibre1">We give below a few more examples of sets used particularly in mathematics, viz.</p>

      <p class="calibre1"><b class="calibre4">N :</b> the set of all natural numbers</p>

      <p class="calibre1"><b class="calibre4">Z :</b> the set of all integers</p>

      <p class="calibre1"><b class="calibre4">Q :</b> the set of all rational numbers</p>

      <p class="calibre1"><b class="calibre4">R :</b> the set of real numbers</p>

      <p class="calibre1"><b class="calibre4">Z<sup>+</sup> :</b> the set of positive integers</p>

      <p class="calibre1"><b class="calibre4">Q<sup>+</sup> :</b> the set of positive rational numbers, and</p>

      <p class="calibre1"><b class="calibre4">R<sup>+</sup> :</b> the set of positive real numbers.</p>

      <p class="calibre1">The symbols for the special sets given above will be referred to throughout this text.</p>

      <p class="calibre1">Again the collection of five most renowned mathematicians of the world is not well-defined, because the criterion for determining a mathematician as most renowned may vary from person to person. Thus, it is not a well-defined collection.</p>

      <p class="calibre1">We shall say that <i class="calibre5"><b class="calibre4">a set is a well-defined collection of objects.</b></i></p>

      <p class="calibre1">The following points may be noted :</p>

      <p class="calibre1">(i) Objects, elements and members of a set are synonymous terms.</p>

      <p class="calibre1">(ii) Sets are usually denoted by capital letters A, B, C, X, Y, Z, etc.</p>

      <p class="calibre1">(iii) The elements of a set are represented by small letters <i class="calibre5">a, b, c, x, y, z,</i> etc.</p>

      <p class="calibre1">If <i class="calibre5">a</i> is an element of a set A, we say that “ <i class="calibre5">a</i> belongs to A” the Greek symbol ∈</p>

      <p class="calibre1">(epsilon) is used to denote the phrase ‘&nbsp;<i class="calibre5">belongs to</i>’. Thus, we write <i class="calibre5">a</i> ∈ A. If ‘ <i class="calibre5">b</i>’ is not an element of a set A, we write <i class="calibre5">b</i> ∉ A and read “<i class="calibre5">b</i> does not belong to A”.</p>

      <p class="calibre1">Thus, in the set V of vowels in the English alphabet, <i class="calibre5">a</i> ∈ &nbsp;V but <i class="calibre5">b</i> ∉ V. In the set P of prime factors of 30, 3 ∈ P but 15 ∉ &nbsp;P.</p>

      <p class="calibre1">There are two methods of representing a set :</p>

      <p class="calibre1">(i) Roster or tabular form</p>

      <p class="calibre1">(ii) Set-builder form.</p>

      <p class="calibre1">(i) In roster form, all the elements of a set are listed, the elements are being separated by commas and are enclosed within braces { }. For example, the set of all even positive integers less than 7 is described in roster form as {2, 4, 6}. Some more examples of representing a set in roster form are given below :</p>

      <p class="calibre1">(a) The set of all natural numbers which divide 42 is {1, 2, 3, 6, 7, 14, 21, 42}. not to be republished</p>

      <div class="box">
        <p><span class="note_bg">Note</span> In roster form, the order in which the elements are listed is immaterial.</p>
      </div>

      <p class="calibre1">Thus, the above set can also be represented as {1, 3, 7, 21, 2, 6, 14, 42}.</p>

      <p class="calibre1">(b) The set of all vowels in the English alphabet is { <i class="calibre5">a, e, i, o, u</i>}.</p>

      <p class="calibre1">(c) The set of odd natural numbers is represented by {1, 3, 5, . . .}. The dots tell us that the list of odd numbers continue indefinitely.</p>

      <div class="box">
        <p><span class="note_bg">Note</span> It may be noted that while writing the set in roster form an element is not generally repeated, i.e., all the elements are taken as distinct. For example, the set of letters forming the word ‘SCHOOL’ is { S, C, H, O, L} or {H, O, L, C, S}. Here, the order of listing elements has no relevance.</p>
      </div>

      <p class="calibre1">(ii) In set-builder form, all the elements of a set possess a single common property which is not possessed by any element outside the set. For example, in the set { <i class="calibre5">a, e, i, o, u</i>}, all the elements possess a common property, namely, each of them is a vowel in the English alphabet, and no other letter possess this property. Denoting this set by V, we write</p>

      <p class="calibre1">V = { <i class="calibre5">x : x</i> is a vowel in English alphabet}</p>

      <p class="calibre1">It may be observed that we describe the element of the set by using a symbol <i class="calibre5">x</i> (any other symbol like the letters <i class="calibre5">y</i>, <i class="calibre5">z</i>, etc. could be used) which is followed by a colon “ : ”. After the sign of colon, we write the characteristic property possessed by the elements of the set and then enclose the whole description within braces. The above description of the set V is read as “the set of all <i class="calibre5">x</i> such that <i class="calibre5">x</i> is a vowel of the English alphabet”. In this description the braces stand for “the set of all”, the colon stands for “such that”. For example, the set</p>

      <p class="calibre1">A = { <i class="calibre5">x : x</i> is a natural number and 3 &lt; <i class="calibre5">x</i> &lt; 10} is read as “the set of all <i class="calibre5">x</i> such that <i class="calibre5">x</i> is a natural number and <i class="calibre5">x</i> lies between 3 and 10. Hence, the numbers 4, 5, 6, 7, 8 and 9 are the elements of the set A.</p>

      <p class="calibre1">If we denote the sets described in ( <i class="calibre5">a</i>), ( <i class="calibre5">b</i>) and ( <i class="calibre5">c</i>) above in roster form by A, B, C, respectively, then A, B, C can also be represented in set-builder form as follows: A= { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a natural number which divides 42}</p>

      <p class="calibre1">B= { <i class="calibre5">y</i> : <i class="calibre5">y</i> is a vowel in the English alphabet}</p>

      <p class="calibre1">C= { <i class="calibre5">z</i> : <i class="calibre5">z</i> is an odd natural number}</p>

      <p>&lt; span class="highlight"&gt;Example 1Write the solution set of the equation <i class="calibre5">x</i> 2 + <i class="calibre5">x –</i> 2 = 0 in roster form.</p>

      <p class="calibre1"></p>

      <p class="calibre1"><b class="calibre4">Solution</b> The given equation can be written as</p>

      <p class="calibre1">( <i class="calibre5">x –</i> 1) ( <i class="calibre5">x</i> + 2) = 0, i. e., <i class="calibre5">x</i> = 1, – 2</p>

      <p class="calibre1">Therefore, the solution set of the given equation can be written in roster form as {1, – 2}.</p>

      <p class="calibre1">not to be republished</p>

      <p>&lt; span class="highlight"&gt;Example 2Write the set { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a positive integer and <i class="calibre5">x</i> 2 &lt; 40} in the roster form.</p>

      <p class="calibre1">4 MATHEMATICS</p>

      <p class="calibre1"><b class="calibre4">Solution</b> The required numbers are 1, 2, 3, 4, 5, 6. So, the given set in the roster form is {1, 2, 3, 4, 5, 6}.</p>

      <p>&lt; span class="highlight"&gt;Example 3Write the set A = {1, 4, 9, 16, 25, . . . }in set-builder form.</p>

      <p class="calibre1"><b class="calibre4">Solution</b> We may write the set A as</p>

      <p class="calibre1">A = { <i class="calibre5">x</i> : <i class="calibre5">x</i> is the square of a natural number}</p>

      <p class="calibre1">Alternatively, we can write</p>

      <p class="calibre1">A = { <i class="calibre5">x</i> : <i class="calibre5">x</i> = <i class="calibre5">n</i> 2, where <i class="calibre5">n</i> ∈ <b class="calibre4">N</b>}</p>

      <p class="calibre1">1 2 3 4 5 6</p>

      <p>&lt; span class="highlight"&gt;Example 4Write the set { <i class="calibre5">,</i></p>

      <p class="calibre1"><i class="calibre5">,</i></p>

      <p class="calibre1"><i class="calibre5">,</i></p>

      <p class="calibre1"><i class="calibre5">, ,</i> } in the set-builder form.</p>

      <p class="calibre1">2 3 4 5 6 7</p>

      <p class="calibre1"><b class="calibre4">Solution</b> We see that each member in the given set has the numerator one less than the denominator. Also, the numerator begin from 1 and do not exceed 6. Hence, in the set-builder form the given set is</p>

      <p class="calibre1">⎧</p>

      <p class="calibre1"><i class="calibre5">n</i></p>

      <p class="calibre1">⎫</p>

      <p class="calibre1">⎨ <i class="calibre5">x : x</i> =</p>

      <p class="calibre1"><i class="calibre5">,</i> where <i class="calibre5">n</i> is a natural number and 1 ≤ <i class="calibre5">n</i> ≤ 6⎬</p>

      <p class="calibre1">⎩</p>

      <p class="calibre1"><i class="calibre5">n</i> +1</p>

      <p class="calibre1">⎭</p>

      <p>&lt; span class="highlight"&gt;Example 5Match each of the set on the left described in the roster form with the same set on the right described in the set-builder form :</p>

      <p class="calibre1">(i) {P, R, I, N, C, A, L} (a) { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a positive integer and is a divisor of 18}</p>

      <p class="calibre1">(ii) { 0 }</p>

      <p class="calibre1">(b) { <i class="calibre5">x</i> : <i class="calibre5">x</i> is an integer and <i class="calibre5">x</i> 2 – 9 = 0}</p>

      <p class="calibre1">(iii) {1, 2, 3, 6, 9, 18}</p>

      <p class="calibre1">(c) { <i class="calibre5">x</i> : <i class="calibre5">x</i> is an integer and <i class="calibre5">x</i> + 1= 1}</p>

      <p class="calibre1">(iv) {3, –3}</p>

      <p class="calibre1">(d) { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a letter of the word PRINCIPAL}</p>

      <p class="calibre1"><b class="calibre4">Solution</b> Since in (d), there are 9 letters in the word PRINCIPAL and two letters P and I are repeated, so (i) matches (d). Similarly, (ii) matches (c) as <i class="calibre5">x</i> + 1 = 1 implies <i class="calibre5">x</i> = 0. Also, 1, 2 ,3, 6, 9, 18 are all divisors of 18 and so (iii) matches (a). Finally, <i class="calibre5">x</i> 2 – 9 = 0</p>

      <p class="calibre1">implies <i class="calibre5">x</i> = 3, –3 and so (iv) matches (b).</p>

      <p class="calibre1"><b class="calibre4">EXERCISE 1.1</b></p>

      <p class="calibre1"><b class="calibre4">1.</b></p>

      <p class="calibre1">Which of the following are sets ? Justify your answer.</p>

      <p class="calibre1">(i) The collection of all the months of a year beginning with the letter J.</p>

      <p class="calibre1">(ii) The collection of ten most talented writers of India.</p>

      <p class="calibre1"></p>

      <p class="calibre1">(iii) A team of eleven best-cricket batsmen of the world.</p>

      <p class="calibre1">(iv) The collection of all boys in your class.</p>

      <p class="calibre1">(v) The collection of all natural numbers less than 100.</p>

      <p class="calibre1">(vi) A collection of novels written by the writer Munshi Prem Chand.</p>

      <p class="calibre1">not to be republished</p>

      <p class="calibre1">(vii) The collection of all even integers.</p>

      <p class="calibre1">SETS 5</p>

      <p class="calibre1">(viii) The collection of questions in this Chapter.</p>

      <p class="calibre1">(ix) A collection of most dangerous animals of the world.</p>

      <p class="calibre1"><b class="calibre4">2.</b></p>

      <p class="calibre1">Let A = {1, 2, 3, 4, 5, 6}. Insert the appropriate symbol ∈ or ∉ in the blank spaces:</p>

      <p class="calibre1">(i) 5. . .A</p>

      <p class="calibre1">(ii) 8 . . . A</p>

      <p class="calibre1">(iii) 0. . .A</p>

      <p class="calibre1">(iv) 4. . . A</p>

      <p class="calibre1">(v) 2. . .A</p>

      <p class="calibre1">(vi) 10. . .A</p>

      <p class="calibre1"><b class="calibre4">3.</b></p>

      <p class="calibre1">Write the following sets in roster form:</p>

      <p class="calibre1">(i) A = { <i class="calibre5">x</i> : <i class="calibre5">x</i> is an integer and –3 &lt; <i class="calibre5">x</i> &lt; 7}</p>

      <p class="calibre1">(ii) B = { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a natural number less than 6}</p>

      <p class="calibre1">(iii) C = { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a two-digit natural number such that the sum of its digits is 8}</p>

      <p class="calibre1">(iv) D = { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a prime number which is divisor of 60}</p>

      <p class="calibre1">(v) E = The set of all letters in the word TRIGONOMETRY</p>

      <p class="calibre1">(vi) F = The set of all letters in the word BETTER</p>

      <p class="calibre1"><b class="calibre4">4.</b></p>

      <p class="calibre1">Write the following sets in the set-builder form :</p>

      <p class="calibre1">(i) (3, 6, 9, 12}</p>

      <p class="calibre1">(ii) {2,4,8,16,32}</p>

      <p class="calibre1">(iii) {5, 25, 125, 625}</p>

      <p class="calibre1">(iv) {2, 4, 6, . . .}</p>

      <p class="calibre1">(v) {1,4,9, . . .,100}</p>

      <p class="calibre1"><b class="calibre4">5.</b></p>

      <p class="calibre1">List all the elements of the following sets :</p>

      <p class="calibre1">(i) A = { <i class="calibre5">x</i> : <i class="calibre5">x</i> is an odd natural number}</p>

      <p class="calibre1">1</p>

      <p class="calibre1">9</p>

      <p class="calibre1">(ii) B = { <i class="calibre5">x</i> : <i class="calibre5">x</i> is an integer, <i class="calibre5">–</i> &lt;</p>

      <p class="calibre1">}</p>

      <p class="calibre1">2</p>

      <p class="calibre1"><i class="calibre5">x</i> &lt; 2</p>

      <p class="calibre1">(iii) C = { <i class="calibre5">x</i> : <i class="calibre5">x</i> is an integer, <i class="calibre5">x</i> 2 ≤ 4}</p>

      <p class="calibre1">(iv) D = { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a letter in the word “LOYAL”}</p>

      <p class="calibre1">(v) E = { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a month of a year not having 31 days}</p>

      <p class="calibre1">(vi) F = { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a consonant in the English alphabet which precedes <i class="calibre5">k</i> }.</p>

      <p class="calibre1"><b class="calibre4">6.</b></p>

      <p class="calibre1">Match each of the set on the left in the roster form with the same set on the right described in set-builder form:</p>

      <p class="calibre1">(i) {1, 2, 3, 6}</p>

      <p class="calibre1">(a) { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a prime number and a divisor of 6}</p>

      <p class="calibre1">(ii) {2, 3}</p>

      <p class="calibre1">(b) { <i class="calibre5">x</i> : <i class="calibre5">x</i> is an odd natural number less than 10}</p>

      <p class="calibre1">(iii) {M,A,T,H,E,I,C,S} (c) { <i class="calibre5">x</i> : <i class="calibre5">x</i> is natural number and divisor of 6}</p>

      <p class="calibre1">(iv) {1, 3, 5, 7, 9}</p>

      <p class="calibre1">(d) { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a letter of the word MATHEMATICS}.</p>

      <p class="calibre1"><b class="calibre4">1.3 The Empty Set</b></p>

      <p class="calibre1"></p>

      <p class="calibre1">Consider the set</p>

      <p class="calibre1">A = { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a student of Class XI presently studying in a school }</p>

      <p class="calibre1">We can go to the school and count the number of students presently studying in Class XI in the school. Thus, the set A contains a finite number of elements.</p>

      <p class="calibre1">not to be republished</p>

      <p class="calibre1">We now write another set B as follows:</p>

      <p class="calibre1">6 MATHEMATICS</p>

      <p class="calibre1">B = { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a student presently studying in both Classes X and XI }</p>

      <p class="calibre1">We observe that a student cannot study simultaneously in both Classes X and XI.</p>

      <p class="calibre1">Thus, the set B contains no element at all.</p>

      <p class="calibre1"><b class="calibre4">Definition 1</b> A set which does not contain any element is called the <i class="calibre5">empty set</i> or the <i class="calibre5">null set</i> or the <i class="calibre5">void set</i>.</p>

      <p class="calibre1">According to this definition, B is an empty set while A is not an empty set. The empty set is denoted by the symbol φ or { }.</p>

      <p class="calibre1">We give below a few examples of empty sets.</p>

      <p class="calibre1">(i) Let A = { <i class="calibre5">x</i> : 1 &lt; <i class="calibre5">x</i> &lt; 2, <i class="calibre5">x</i> is a natural number}. Then A is the empty set, because there is no natural number between 1 and 2.</p>

      <p class="calibre1">(ii) B = { <i class="calibre5">x</i> : <i class="calibre5">x</i> 2 – 2 = 0 and <i class="calibre5">x</i> is rational number}. Then B is the empty set because the equation <i class="calibre5">x</i> 2 – 2 = 0 is not satisfied by any rational value of <i class="calibre5">x</i>.</p>

      <p class="calibre1">(iii) C = { <i class="calibre5">x</i> : <i class="calibre5">x</i> is an even prime number greater than 2}.Then C is the empty set, because 2 is the only even prime number.</p>

      <p class="calibre1">(iv) D = { <i class="calibre5">x</i> : <i class="calibre5">x</i> 2 = 4, <i class="calibre5">x</i> is odd }. Then D is the empty set, because the equation <i class="calibre5">x</i> 2 = 4 is not satisfied by any odd value of <i class="calibre5">x</i>.</p>

      <p class="calibre1"><b class="calibre4">1.4 Finite and Infinite Sets</b></p>

      <p class="calibre1">Let</p>

      <p class="calibre1">A = {1, 2, 3, 4, 5},</p>

      <p class="calibre1">B = { <i class="calibre5">a, b, c, d, e, g</i>}</p>

      <p class="calibre1">and</p>

      <p class="calibre1">C = { men living presently in different parts of the world}</p>

      <p class="calibre1">We observe that A contains 5 elements and B contains 6 elements. How many elements does C contain? As it is, we do not know the number of elements in C, but it is some natural number which may be quite a big number. By number of elements of a set S, we mean the number of distinct elements of the set and we denote it by <i class="calibre5">n</i> (S). If <i class="calibre5">n</i> (S) is a natural number, then S is <i class="calibre5">non-empty finite</i> set.</p>

      <p class="calibre1">Consider the set of natural numbers. We see that the number of elements of this set is not finite since there are infinite number of natural numbers. We say that the set of natural numbers is an infinite set. The sets A, B and C given above are finite sets and <i class="calibre5">n</i>(A) = 5, <i class="calibre5">n</i>(B) = 6 and <i class="calibre5">n</i>(C) = some finite number.</p>

      <p class="calibre1"><b class="calibre4">Definition 2</b> A set which is empty or consists of a definite number of elements is called <i class="calibre5">finite</i> otherwise, the set is called <i class="calibre5">infinite</i>.</p>

      <p class="calibre1">Consider some examples :</p>

      <p class="calibre1">(i) Let W be the set of the days of the week. Then W is finite.</p>

      <p class="calibre1">(ii) Let S be the set of solutions of the equation</p>

      <p class="calibre1"><i class="calibre5">x</i> 2 –16 = 0. Then S is finite.</p>

      <p class="calibre1">(iii) Let G be the set of points on a line. Then G is infinite.</p>

      <p class="calibre1">When we represent a set in the roster form, we write all the elements of the set within braces { }. It is not possible to write all the elements of an infinite set within not to be republished</p>

      <p class="calibre1">braces { } because the numbers of elements of such a set is not finite. So, we represent SETS 7</p>

      <p class="calibre1">some infinite set in the roster form by writing a few elements which clearly indicate the structure of the set followed ( or preceded ) by three dots.</p>

      <p class="calibre1">For example, {1, 2, 3 . . .} is the set of natural numbers, {1, 3, 5, 7, . . .} is the set of odd natural numbers, {. . .,–3, –2, –1, 0,1, 2 ,3, . . .} is the set of integers. All these sets are infinite.</p>

      <div class="box">
        <p><span class="note_bg">Note</span> All infinite sets cannot be described in the roster form. For example, the set of real numbers cannot be described in this form, because the elements of this set do not follow any particular pattern.</p>

        <p>&lt; span class="highlight"&gt;Example 6State which of the following sets are finite or infinite :</p>

        <p class="calibre1">(i) { <i class="calibre5">x</i> : <i class="calibre5">x</i> ∈ N and ( <i class="calibre5">x</i> – 1) ( <i class="calibre5">x</i> –2) = 0}</p>

        <p class="calibre1">(ii) { <i class="calibre5">x</i> : <i class="calibre5">x</i> ∈ N and <i class="calibre5">x</i> 2 = 4}</p>

        <p class="calibre1">(iii) { <i class="calibre5">x</i> : <i class="calibre5">x</i> ∈ N and 2 <i class="calibre5">x</i> –1 = 0}</p>

        <p class="calibre1">(iv) { <i class="calibre5">x</i> : <i class="calibre5">x</i> ∈ N and <i class="calibre5">x</i> is prime}</p>

        <p class="calibre1">(v) { <i class="calibre5">x</i> : <i class="calibre5">x</i> ∈ N and <i class="calibre5">x</i> is odd}</p>

        <p class="calibre1"><b class="calibre4">Solution</b></p>

        <p class="calibre1">(i) Given set = {1, 2}. Hence, it is finite.</p>

        <p class="calibre1">(ii) Given set = {2}. Hence, it is finite.</p>

        <p class="calibre1">(iii) Given set = φ. Hence, it is finite.</p>

        <p class="calibre1">(iv) The given set is the set of all prime numbers and since set of prime</p>

        <p class="calibre1">numbers is infinite. Hence the given set is infinite</p>

        <p class="calibre1">(v) Since there are infinite number of odd numbers, hence, the given set is</p>

        <p class="calibre1">infinite.</p>

        <p class="calibre1"><b class="calibre4">1.5 Equal Sets</b></p>

        <p class="calibre1">Given two sets A and B, if every element of A is also an element of B and if every element of B is also an element of A, then the sets A and B are said to be equal.</p>

        <p class="calibre1">Clearly, the two sets have exactly the same elements.</p>

        <p class="calibre1"><b class="calibre4">Definition 3</b> Two sets A and B are said to be <i class="calibre5">equal</i> if they have exactly the same elements and we write A = B. Otherwise, the sets are said to be <i class="calibre5">unequal</i> and we write A ≠ B.</p>

        <p class="calibre1">We consider the following examples :</p>

        <p class="calibre1">(i) Let A = {1, 2, 3, 4} and</p>

        <p class="calibre1">B = {3, 1, 4, 2}. Then A = B.</p>

        <p class="calibre1">(ii) Let A be the set of prime numbers less than 6 and P the set of prime factors</p>

        <p class="calibre1"></p>

        <p class="calibre1">of 30. Then A and P are equal, since 2, 3 and 5 are the only prime factors of 30 and also these are less than 6.</p>

        <div class="box">
          <p><span class="note_bg">Note</span> A set does not change if one or more elements of the set are repeated.</p>

          <p class="calibre1">not to be republished</p>

          <p class="calibre1">For example, the sets A = {1, 2, 3} and B = {2, 2, 1, 3, 3} are equal, since each 8 MATHEMATICS</p>

          <p class="calibre1">element of A is in B and vice-versa. That is why we generally do not repeat any element in describing a set.</p>

          <p>&lt; span class="highlight"&gt;Example 7Find the pairs of equal sets, if any, give reasons:</p>

          <p class="calibre1">A = {0},</p>

          <p class="calibre1">B = { <i class="calibre5">x</i> : <i class="calibre5">x</i> &gt; 15 and <i class="calibre5">x</i> &lt; 5},</p>

          <p class="calibre1">C = { <i class="calibre5">x</i> : <i class="calibre5">x</i> – 5 = 0 },</p>

          <p class="calibre1">D = { <i class="calibre5">x</i>: <i class="calibre5">x</i> 2 = 25},</p>

          <p class="calibre1">E = { <i class="calibre5">x</i> : <i class="calibre5">x</i> is an integral positive root of the equation <i class="calibre5">x</i> 2 – 2 <i class="calibre5">x</i> –15 = 0}.</p>

          <p class="calibre1"><b class="calibre4">Solution</b> Since 0 ∈ A and 0 does not belong to any of the sets B, C, D and E, it follows that, A ≠ B, A ≠ C, A ≠ D, A ≠ E.</p>

          <p class="calibre1">Since B = φ but none of the other sets are empty. Therefore B ≠ C, B ≠ D</p>

          <p class="calibre1">and B ≠ E. Also C = {5} but –5 ∈ D, hence C ≠ D.</p>

          <p class="calibre1">Since E = {5}, C = E. Further, D = {–5, 5} and E = {5}, we find that, D ≠ E.</p>

          <p class="calibre1">Thus, the only pair of equal sets is C and E.</p>

          <p>&lt; span class="highlight"&gt;Example 8Which of the following pairs of sets are equal? Justify your answer.</p>

          <p class="calibre1">(i) X, the set of letters in “ALLOY” and B, the set of letters in “LOYAL”.</p>

          <p class="calibre1">(ii) A = { <i class="calibre5">n</i> : <i class="calibre5">n</i> ∈ Z and <i class="calibre5">n</i> 2 ≤ 4} and B = { <i class="calibre5">x</i> : <i class="calibre5">x</i> ∈ R and <i class="calibre5">x</i> 2 – 3 <i class="calibre5">x</i> + 2 = 0}.</p>

          <p class="calibre1"><b class="calibre4">Solution</b> (i) We have, X = {A, L, L, O, Y}, B = {L, O, Y, A, L}. Then X and B are equal sets as repetition of elements in a set do not change a set. Thus,</p>

          <p class="calibre1">X = {A, L, O, Y} = B</p>

          <p class="calibre1">(ii) A = {–2, –1, 0, 1, 2}, B = {1, 2}. Since 0 ∈ A and 0 ∉ B, A and B are not equal sets.</p>

          <p class="calibre1"><b class="calibre4">EXERCISE 1.2</b></p>

          <p class="calibre1"><b class="calibre4">1.</b></p>

          <p class="calibre1">Which of the following are examples of the null set</p>

          <p class="calibre1">(i) Set of odd natural numbers divisible by 2</p>

          <p class="calibre1">(ii) Set of even prime numbers</p>

          <p class="calibre1">(iii) { <i class="calibre5">x : x</i> is a natural numbers, <i class="calibre5">x</i> &lt; 5 and <i class="calibre5">x</i> &gt; 7 }</p>

          <p class="calibre1">(iv) { <i class="calibre5">y : y</i> is a point common to any two parallel lines}</p>

          <p class="calibre1"><b class="calibre4">2.</b></p>

          <p class="calibre1">Which of the following sets are finite or infinite</p>

          <p class="calibre1">(i) The set of months of a year</p>

          <p class="calibre1">(ii) {1, 2, 3, . . .}</p>

          <p class="calibre1">(iii) {1, 2, 3, . . .99, 100}</p>

          <p class="calibre1">(iv) The set of positive integers greater than 100</p>

          <p class="calibre1">(v) The set of prime numbers less than 99</p>

          <p class="calibre1"></p>

          <p class="calibre1"><b class="calibre4">3.</b></p>

          <p class="calibre1">State whether each of the following set is finite or infinite:</p>

          <p class="calibre1">(i) The set of lines which are parallel to the <i class="calibre5">x</i>-axis</p>

          <p class="calibre1">(ii) The set of letters in the English alphabet</p>

          <p class="calibre1">not to be republished</p>

          <p class="calibre1">(iii) The set of numbers which are multiple of 5</p>

          <p class="calibre1">SETS 9</p>

          <p class="calibre1">(iv) The set of animals living on the earth</p>

          <p class="calibre1">(v) The set of circles passing through the origin (0,0)</p>

          <p class="calibre1"><b class="calibre4">4.</b></p>

          <p class="calibre1">In the following, state whether A = B or not:</p>

          <p class="calibre1">(i) A = { <i class="calibre5">a</i>, <i class="calibre5">b</i>, <i class="calibre5">c</i>, <i class="calibre5">d</i> }</p>

          <p class="calibre1">B = { <i class="calibre5">d</i>, <i class="calibre5">c</i>, <i class="calibre5">b</i>, <i class="calibre5">a</i> }</p>

          <p class="calibre1">(ii) A = { 4, 8, 12, 16 }</p>

          <p class="calibre1">B = { 8, 4, 16, 18}</p>

          <p class="calibre1">(iii) A = {2, 4, 6, 8, 10}</p>

          <p class="calibre1">B = { <i class="calibre5">x</i> : <i class="calibre5">x</i> is positive even integer and <i class="calibre5">x</i> ≤ 10}</p>

          <p class="calibre1">(iv) A = { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a multiple of 10},</p>

          <p class="calibre1">B = { 10, 15, 20, 25, 30, . . . }</p>

          <p class="calibre1"><b class="calibre4">5.</b></p>

          <p class="calibre1">Are the following pair of sets equal ? Give reasons.</p>

          <p class="calibre1">(i) A = {2, 3}, B = { <i class="calibre5">x</i> : <i class="calibre5">x</i> is solution of <i class="calibre5">x</i> 2 + 5 <i class="calibre5">x</i> + 6 = 0}</p>

          <p class="calibre1">(ii) A = { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a letter in the word FOLLOW}</p>

          <p class="calibre1">B = { <i class="calibre5">y</i> : <i class="calibre5">y</i> is a letter in the word WOLF}</p>

          <p class="calibre1"><b class="calibre4">6.</b></p>

          <p class="calibre1">From the sets given below, select equal sets :</p>

          <p class="calibre1">A = { 2, 4, 8, 12}, B = { 1, 2, 3, 4}, C = { 4, 8, 12, 14},</p>

          <p class="calibre1">D = { 3, 1, 4, 2}</p>

          <p class="calibre1">E = {–1, 1},</p>

          <p class="calibre1">F = { 0, <i class="calibre5">a</i>},</p>

          <p class="calibre1">G = {1, –1},</p>

          <p class="calibre1">H = { 0, 1}</p>

          <p class="calibre1"><b class="calibre4">1.6 Subsets</b></p>

          <p class="calibre1">Consider the sets : X = set of all students in your school, Y = set of all students in your class.</p>

          <p class="calibre1">We note that every element of Y is also an element of X; we say that Y is a subset of X. The fact that Y is subset of X is expressed in symbols as Y ⊂ X. The symbol ⊂</p>

          <p class="calibre1">stands for ‘is a subset of’ or ‘is contained in’.</p>

          <p class="calibre1"><b class="calibre4">Definition 4</b> A set A is said to be a subset of a set B if every element of A is also an element of B.</p>

          <p class="calibre1">In other words, A ⊂ B if whenever <i class="calibre5">a</i> ∈ A, then <i class="calibre5">a</i> ∈ B. It is often convenient to use the symbol “⇒” which means <i class="calibre5">implies</i>. Using this symbol, we can write the definiton of <i class="calibre5">subset</i> as follows:</p>

          <p class="calibre1">A ⊂ B if <i class="calibre5">a</i> ∈ A ⇒ <i class="calibre5">a</i> ∈ B</p>

          <p class="calibre1">We read the above statement as <b class="calibre4">“</b> <i class="calibre5">A is a subset of B if a is an element of A</i> <i class="calibre5">implies that a is also an element of B</i><b class="calibre4">”.</b> If A is not a subset of B, we write A ⊄ B.</p>

          <p class="calibre1">We may note that for A to be a subset of B, all that is needed is that every</p>

          <p class="calibre1">element of A is in B. It is possible that every element of B may or may not be in A. If it so happens that every element of B is also in A, then we shall also have B ⊂ A. In this case, A and B are the same sets so that we have A ⊂ B and B ⊂ A ⇔ A = B, where</p>

          <p class="calibre1">“⇔” is a symbol for two way implications, and is usually read as <i class="calibre5">if and only if</i> (briefly</p>

          <p class="calibre1"></p>

          <p class="calibre1">written as “iff”).</p>

          <p class="calibre1">It follows from the above definition that every set <i class="calibre5">A is a</i> <i class="calibre5">subset of itself,</i> i.e., A ⊂ A. Since the empty set φ has no elements, we agree to say that φ <i class="calibre5">is a subset of</i> not to be republished</p>

          <p class="calibre1"><i class="calibre5">every set</i><b class="calibre4">.</b> We now consider some examples :</p>

          <p class="calibre1">10 MATHEMATICS</p>

          <p class="calibre1">(i) The set <b class="calibre4">Q</b> of rational numbers is a subset of the set <b class="calibre4">R</b> of real numbes, and we write <b class="calibre4">Q</b> ⊂ R.</p>

          <p class="calibre1">(ii) If A is the set of all divisors of 56 and B the set of all prime divisors of 56, then B is a subset of A and we write B ⊂ A.</p>

          <p class="calibre1">(iii) Let A = {1, 3, 5} and B = { <i class="calibre5">x : x</i> is an odd natural number less than 6}. Then A ⊂ B and B ⊂ A and hence A = B.</p>

          <p class="calibre1">(iv) Let A = { <i class="calibre5">a, e, i, o, u</i>} and B = { <i class="calibre5">a, b, c, d</i>}. Then A is not a subset of B, also B is not a subset of A.</p>

          <p class="calibre1">Let A and B be two sets. If A ⊂ B and A ≠ B , then A is called a <i class="calibre5">proper subset</i> <i class="calibre5">of</i> B and B is called <i class="calibre5">superset</i> of A. For example, A = {1, 2, 3} is a proper subset of B = {1, 2, 3, 4}.</p>

          <p class="calibre1">If a set A has only one element, we call it a <i class="calibre5">singleton set</i>. Thus,{ <i class="calibre5">a</i> } is a singleton set.</p>

          <p>&lt; span class="highlight"&gt;Example 9Consider the sets</p>

          <p class="calibre1">φ, A = { 1, 3 }, B = {1, 5, 9}, C = {1, 3, 5, 7, 9}.</p>

          <p class="calibre1">Insert the symbol ⊂ or ⊄ between each of the following pair of sets:</p>

          <p class="calibre1">(i) φ <b class="calibre4">. . .</b> B</p>

          <p class="calibre1">(ii) A . . . B</p>

          <p class="calibre1">(iii) A . . . C</p>

          <p class="calibre1">(iv) B . . . C</p>

          <p class="calibre1"><b class="calibre4">Solution</b></p>

          <p class="calibre1">(i)</p>

          <p class="calibre1">φ ⊂ B as φ is a subset of every set.</p>

          <p class="calibre1">(ii)</p>

          <p class="calibre1">A ⊄ B as 3 ∈ A and 3 ∉ B</p>

          <p class="calibre1">(iii)</p>

          <p class="calibre1">A ⊂ C as 1, 3 ∈ A also belongs to C</p>

          <p class="calibre1">(iv)</p>

          <p class="calibre1">B ⊂ C as each element of B is also an element of C.</p>

          <p>&lt; span class="highlight"&gt;Example 10Let A = { <i class="calibre5">a, e, i, o, u</i>} and B = { <i class="calibre5">a, b, c, d</i>}. Is A a subset of B ? No.</p>

          <p class="calibre1">(Why?). Is B a subset of A? No. (Why?)</p>

          <p>&lt; span class="highlight"&gt;Example 11Let A, B and C be three sets. If A ∈ B and B ⊂ C, is it true that A ⊂ C?. If not, give an example.</p>

          <p class="calibre1"><b class="calibre4">Solution</b> No. Let A = {1}, B = {{1}, 2} and C = {{1}, 2, 3}. Here A ∈ B as A = {1}</p>

          <p class="calibre1">and B ⊂ C. But A ⊄ C as 1 ∈ A and 1 ∉ C.</p>

          <p class="calibre1">Note that an element of a set can never be a subset of itself.</p>

          <p class="calibre1"><b class="calibre4">1.6.1 <i class="calibre5">Subsets of set of real numbers</i></b></p>

          <p class="calibre1">As noted in Section 1.6, there are many important subsets of <b class="calibre4">R</b>. We give below the names of some of these subsets.</p>

          <p class="calibre1">The set of natural numbers <b class="calibre4">N</b> = {1, 2, 3, 4, 5, . . .}</p>

          <p class="calibre1"></p>

          <p class="calibre1">The set of integers</p>

          <p class="calibre1"><b class="calibre4">Z</b> = {. . ., –3, –2, –1, 0, 1, 2, 3, . . .}</p>

          <p class="calibre1"><i class="calibre5">p</i></p>

          <p class="calibre1">The set of rational numbers <b class="calibre4">Q</b> = { <i class="calibre5">x</i> : <i class="calibre5">x =</i></p>

          <p class="calibre1">, <i class="calibre5">p, q</i> ∈ <b class="calibre4">Z</b> and <i class="calibre5">q</i> ≠ 0}</p>

          <p class="calibre1"><i class="calibre5">q</i></p>

          <p class="calibre1">not to be republished</p>

          <p class="calibre1">SETS 11</p>

          <p class="calibre1"><i class="calibre5">p</i></p>

          <p class="calibre1">which is read “ <b class="calibre4">Q</b> is the set of all numbers <i class="calibre5">x</i> such that <i class="calibre5">x</i> equals the quotient</p>

          <p class="calibre1">, where</p>

          <p class="calibre1"><i class="calibre5">q</i></p>

          <p class="calibre1"><i class="calibre5">p</i> and <i class="calibre5">q</i> are integers and <i class="calibre5">q</i> is not zero”. Members of <b class="calibre4">Q</b> include –5 (which can be 5</p>

          <p class="calibre1">5</p>

          <p class="calibre1">1</p>

          <p class="calibre1">7</p>

          <p class="calibre1">11</p>

          <p class="calibre1">expressed as <i class="calibre5">–</i> ) , , 3 (which can be expressed as ) and <i class="calibre5">–</i></p>

          <p class="calibre1">.</p>

          <p class="calibre1">1</p>

          <p class="calibre1">7</p>

          <p class="calibre1">2</p>

          <p class="calibre1">2</p>

          <p class="calibre1">3</p>

          <p class="calibre1">The set of irrational numbers, denoted by <b class="calibre4">T</b>, is composed of all other real numbers.</p>

          <p class="calibre1">Thus</p>

          <p class="calibre1"><b class="calibre4">T</b> = { <i class="calibre5">x</i> : <i class="calibre5">x</i> ∈ <b class="calibre4">R</b> and <i class="calibre5">x</i> ∉ <b class="calibre4">Q</b>}, i.e., all real numbers that are not rational.</p>

          <p class="calibre1">Members of <b class="calibre4">T</b> include 2 , 5 and π .</p>

          <p class="calibre1">Some of the obvious relations among these subsets are:</p>

          <p class="calibre1"><b class="calibre4">N</b> ⊂ <b class="calibre4">Z</b> ⊂ <b class="calibre4">Q</b>, <b class="calibre4">Q</b> ⊂ <b class="calibre4">R</b>, <b class="calibre4">T</b> ⊂ <b class="calibre4">R</b>, <b class="calibre4">N</b> ⊄ <b class="calibre4">T</b>.</p>

          <p class="calibre1"><b class="calibre4">1.6.2 <i class="calibre5">Intervals as subsets of R</i></b> Let <i class="calibre5">a, b</i> ∈ <b class="calibre4">R</b> and <i class="calibre5">a &lt; b.</i> Then the set of real numbers</p>

          <p class="calibre1">{ <i class="calibre5">y : a &lt; y &lt; b</i>} is called an <i class="calibre5">open interval</i> and is denoted by ( <i class="calibre5">a</i>, <i class="calibre5">b</i>) <i class="calibre5">.</i> All the points between <i class="calibre5">a</i> and <i class="calibre5">b</i> belong to the open interval ( <i class="calibre5">a, b</i>) but <i class="calibre5">a, b</i> themselves do not belong to this interval.</p>

          <p class="calibre1">The interval which contains the end points also is called <i class="calibre5">closed interval</i> and is denoted by [ <i class="calibre5">a, b</i> ]. Thus</p>

          <p class="calibre1">[ <i class="calibre5">a, b</i> ] = { <i class="calibre5">x</i> : <i class="calibre5">a</i> ≤ <i class="calibre5">x</i> ≤ <i class="calibre5">b</i>}</p>

          <p class="calibre1">We can also have intervals closed at one end and open at the other, i.e.,</p>

          <p class="calibre1">[ <i class="calibre5">a, b</i> ) = { <i class="calibre5">x : a</i> ≤ <i class="calibre5">x</i> &lt; <i class="calibre5">b</i>} is an <i class="calibre5">open interval</i> from <i class="calibre5">a</i> to <i class="calibre5">b,</i> including <i class="calibre5">a</i> but excluding <i class="calibre5">b.</i></p>

          <p class="calibre1">( <i class="calibre5">a, b</i> ] = { <i class="calibre5">x</i> : <i class="calibre5">a</i> &lt; <i class="calibre5">x</i> ≤ <i class="calibre5">b</i> } is an <i class="calibre5">open interval</i> from <i class="calibre5">a</i> to <i class="calibre5">b</i> including <i class="calibre5">b</i> but excluding <i class="calibre5">a.</i></p>

          <p class="calibre1">These notations provide an alternative way of designating the subsets of set of real numbers. For example , if A = (–3, 5) and B = [–7, 9], then A ⊂ B. The set [ 0, ∞) defines the set of non-negative real numbers, while set ( – ∞, 0 ) defines the set of negative real numbers. The set ( – ∞, ∞ ) describes the set of real numbers in relation to a line extending from – ∞ to ∞.</p>

          <p class="calibre1">On real number line, various types of intervals described above as subsets of <b class="calibre4">R</b>, are shown in the Fig 1.1.</p>

          <p class="calibre1"><b class="calibre4">Fig 1.1</b></p>

          <p class="calibre1"></p>

          <p class="calibre1">Here, we note that an interval contains infinitely many points.</p>

          <p class="calibre1">For example, the set { <i class="calibre5">x</i> : <i class="calibre5">x</i> ∈ <b class="calibre4">R</b>, –5 &lt; <i class="calibre5">x</i> ≤ 7}, written in set-builder form, can be written in the form of interval as (–5, 7] and the interval [–3, 5) can be written in set-not to be republished</p>

          <p class="calibre1">builder form as { <i class="calibre5">x</i> : –3 ≤ <i class="calibre5">x</i> &lt; 5}.</p>

          <p class="calibre1">12 MATHEMATICS</p>

          <p class="calibre1">The number ( <i class="calibre5">b – a</i>) is called the <i class="calibre5">length of any of the intervals</i> ( <i class="calibre5">a, b</i>), [ <i class="calibre5">a, b</i>],</p>

          <p class="calibre1">[ <i class="calibre5">a, b</i>) or ( <i class="calibre5">a, b</i>] <i class="calibre5">.</i></p>

          <p class="calibre1"><b class="calibre4">1.7 Power Set</b></p>

          <p class="calibre1">Consider the set {1, 2}. Let us write down all the subsets of the set {1, 2}. We know that φ is a subset of every set . So, φ is a subset of {1, 2}. We see that {1}</p>

          <p class="calibre1">and { 2 }are also subsets of {1, 2}. Also, we know that every set is a subset of itself. So, { 1, 2 } is a subset of {1, 2}. Thus, the set { 1, 2 } has, in all, four subsets, viz. φ, { 1 }, { 2 } and { 1, 2 }. The set of all these subsets is called the <i class="calibre5">power set</i> of { 1, 2 }.</p>

          <p class="calibre1"><b class="calibre4">Definition 5</b> The collection of all subsets of a set A is called the <i class="calibre5">power set</i> of A. It is denoted by P(A). In P(A), every element is a set.</p>

          <p class="calibre1">Thus, as in above, if A = { 1, 2 }, then</p>

          <p class="calibre1">P( A ) = { φ,{ 1 }, { 2 }, { 1,2 }}</p>

          <p class="calibre1">Also, note that <i class="calibre5">n</i> [ P (A) ] = 4 = 22</p>

          <p class="calibre1">In general, if A is a set with <i class="calibre5">n</i>(A) = <i class="calibre5">m</i>, then it can be shown that <i class="calibre5">n</i> [ P(A)] = 2 <i class="calibre5">m</i>.</p>

          <p class="calibre1"><b class="calibre4">1.8 Universal Set</b></p>

          <p class="calibre1">Usually, in a particular context, we have to deal with the elements and subsets of a basic set which is relevant to that particular context. For example, while studying the system of numbers, we are interested in the set of natural numbers and its subsets such as the set of all prime numbers, the set of all even numbers, and so forth. This basic set is called the <b class="calibre4">“</b> <i class="calibre5">Universal Set</i><b class="calibre4">”.</b> The universal set is usually denoted by U, and all its subsets by the letters A, B, C, etc.</p>

          <p class="calibre1">For example, for the set of all integers, the universal set can be the set of rational numbers or, for that matter, the set <b class="calibre4">R</b> of real numbers. For another example, in human population studies, the universal set consists of all the people in the world.</p>

          <p class="calibre1"><b class="calibre4">EXERCISE 1.3</b></p>

          <p class="calibre1"><b class="calibre4">1.</b></p>

          <p class="calibre1">Make correct statements by filling in the symbols ⊂ or ⊄ in the blank spaces : (i) { 2, 3, 4 } . . . { 1, 2, 3, 4,5 } (ii) { <i class="calibre5">a</i>, <i class="calibre5">b</i>, <i class="calibre5">c</i> } . . . { <i class="calibre5">b</i>, <i class="calibre5">c</i>, <i class="calibre5">d</i> }</p>

          <p class="calibre1">(iii) { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a student of Class XI of your school}. . .{ <i class="calibre5">x</i> : <i class="calibre5">x</i> student of your school}</p>

          <p class="calibre1">(iv) {</p>

          <p class="calibre1"></p>

          <p class="calibre1"><i class="calibre5">x</i> : <i class="calibre5">x</i> is a circle in the plane} . . .{ <i class="calibre5">x</i> : <i class="calibre5">x</i> is a circle in the same plane with radius 1 unit}</p>

          <p class="calibre1">(v) { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a triangle in a plane} . . . { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a rectangle in the plane}</p>

          <p class="calibre1">(vi) { <i class="calibre5">x</i> : <i class="calibre5">x</i> is an equilateral triangle in a plane} . . . { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a triangle in the same plane}</p>

          <p class="calibre1">not to be republished</p>

          <p class="calibre1">(vii) { <i class="calibre5">x</i> : <i class="calibre5">x</i> is an even natural number} . . . { <i class="calibre5">x</i> : <i class="calibre5">x</i> is an integer}</p>

          <p class="calibre1">SETS 13</p>

          <p class="calibre1"><b class="calibre4">2.</b></p>

          <p class="calibre1">Examine whether the following statements are true or false:</p>

          <p class="calibre1">(i) { <i class="calibre5">a</i>, <i class="calibre5">b</i> } ⊄ { <i class="calibre5">b</i>, <i class="calibre5">c, a</i> }</p>

          <p class="calibre1">(ii) { <i class="calibre5">a</i>, <i class="calibre5">e</i> } ⊂ { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a vowel in the English alphabet}</p>

          <p class="calibre1">(iii) { 1, 2, 3 } ⊂ { 1, 3 <i class="calibre5">,</i> 5 }</p>

          <p class="calibre1">(iv) { <i class="calibre5">a</i> } ⊂ { <i class="calibre5">a</i>, <i class="calibre5">b, c</i> }</p>

          <p class="calibre1">(v) { <i class="calibre5">a</i> } ∈ { <i class="calibre5">a</i>, <i class="calibre5">b, c</i> }</p>

          <p class="calibre1">(vi) { <i class="calibre5">x</i> : <i class="calibre5">x</i> is an even natural number less than 6} ⊂ { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a natural number which divides 36}</p>

          <p class="calibre1"><b class="calibre4">3.</b></p>

          <p class="calibre1">Let A = { 1, 2, { 3, 4 }, 5 }. Which of the following statements are incorrect and why?</p>

          <p class="calibre1">(i) {3, 4} ⊂ A</p>

          <p class="calibre1">(ii) {3, 4} ∈ A</p>

          <p class="calibre1">(iii) {{3, 4}} ⊂ A</p>

          <p class="calibre1">(iv) 1 ∈ A</p>

          <p class="calibre1">(v) 1 ⊂ A</p>

          <p class="calibre1">(vi) {1, 2, 5} ⊂ A</p>

          <p class="calibre1">(vii) {1, 2, 5} ∈ A</p>

          <p class="calibre1">(viii) {1, 2, 3} ⊂ A</p>

          <p class="calibre1">(ix) φ ∈ A</p>

          <p class="calibre1">(x) φ ⊂ A</p>

          <p class="calibre1">(xi) {φ} ⊂ A</p>

          <p class="calibre1"><b class="calibre4">4.</b></p>

          <p class="calibre1">Write down all the subsets of the following sets</p>

          <p class="calibre1">(i) { <i class="calibre5">a</i>}</p>

          <p class="calibre1">(ii) { <i class="calibre5">a</i>, <i class="calibre5">b</i>}</p>

          <p class="calibre1">(iii) {1, 2, 3}</p>

          <p class="calibre1">(iv) φ</p>

          <p class="calibre1"><b class="calibre4">5.</b></p>

          <p class="calibre1">How many elements has P(A), if A = φ?</p>

          <p class="calibre1"><b class="calibre4">6.</b></p>

          <p class="calibre1">Write the following as intervals :</p>

          <p class="calibre1">(i) { <i class="calibre5">x</i> : <i class="calibre5">x</i> ∈ R, – 4 <i class="calibre5">&lt; x</i> ≤ 6}</p>

          <p class="calibre1">(ii) { <i class="calibre5">x</i> : <i class="calibre5">x</i> ∈ R, – 12 <i class="calibre5">&lt; x</i> &lt; –10}</p>

          <p class="calibre1">(iii) { <i class="calibre5">x</i> : <i class="calibre5">x</i> ∈ R, 0 ≤ <i class="calibre5">x &lt; 7</i>}</p>

          <p class="calibre1">(iv) { <i class="calibre5">x</i> : <i class="calibre5">x</i> ∈ R, 3 ≤ <i class="calibre5">x</i> ≤ 4}</p>

          <p class="calibre1"><b class="calibre4">7.</b></p>

          <p class="calibre1">Write the following intervals in set-builder form :</p>

          <p class="calibre1">(i) (– 3, 0)</p>

          <p class="calibre1">(ii) [6 , 12]</p>

          <p class="calibre1">(iii) (6, 12]</p>

          <p class="calibre1">(iv) [–23, 5)</p>

          <p class="calibre1"><b class="calibre4">8.</b></p>

          <p class="calibre1">What universal set(s) would you propose for each of the following :</p>

          <p class="calibre1">(i) The set of right triangles.</p>

          <p class="calibre1">(ii) The set of isosceles triangles.</p>

          <p class="calibre1"><b class="calibre4">9.</b></p>

          <p class="calibre1">Given the sets A = {1, 3, 5}, B = {2, 4, 6} and C = {0, 2, 4, 6, 8}, which of the following may be considered as universal set (s) for all the three sets A, B and C</p>

          <p class="calibre1">(i) {0, 1, 2, 3, 4, 5, 6}</p>

          <p class="calibre1">(ii) φ</p>

          <p class="calibre1">(iii) {0,1,2,3,4,5,6,7,8,9,10}</p>

          <p class="calibre1">(iv) {1,2,3,4,5,6,7,8}</p>

          <p class="calibre1"><b class="calibre4">1.9 Venn Diagrams</b></p>

          <p class="calibre1">Most of the relationships between sets can be</p>

          <p class="calibre1">represented by means of diagrams which are known</p>

          <p class="calibre1">as <i class="calibre5">Venn diagrams</i>. Venn diagrams are named after</p>

          <p class="calibre1">the English logician, John Venn (1834-1883). These</p>

          <p class="calibre1">diagrams consist of rectangles and closed curves</p>

          <p class="calibre1"></p>

          <p class="calibre1">usually circles. The universal set is represented</p>

          <p class="calibre1">usually by a rectangle and its subsets by circles.</p>

          <p class="calibre1">In Venn diagrams, the elements of the sets</p>

          <p class="calibre1">not to be republished</p>

          <p class="calibre1">are written in their respective circles (Figs 1.2 and 1.3)</p>

          <p class="calibre1"><b class="calibre4">Fig 1.2</b></p>

          <p class="calibre1">14 MATHEMATICS</p>

          <p class="calibre1"><b class="calibre4">Illustration 1</b> In Fig 1.2, U = {1,2,3, ..., 10} is the</p>

          <p class="calibre1">universal set of which</p>

          <p class="calibre1">A = {2,4,6,8,10} is a subset.</p>

          <p class="calibre1"><b class="calibre4">Illustration 2</b> In Fig 1.3, U = {1,2,3, ..., 10} is the</p>

          <p class="calibre1">universal set of which</p>

          <p class="calibre1">A = {2,4,6,8,10} and B = {4, 6} are subsets,</p>

          <p class="calibre1">and also B ⊂ A.</p>

          <p class="calibre1"><b class="calibre4">Fig 1.3</b></p>

          <p class="calibre1">The reader will see an extensive use of the</p>

          <p class="calibre1">Venn diagrams when we discuss the union, intersection and difference of sets.</p>

          <p class="calibre1"><b class="calibre4">1.10 Operations on Sets</b></p>

          <p class="calibre1">In earlier classes, we have learnt how to perform the operations of addition, subtraction, multiplication and division on numbers. Each one of these operations was performed on a pair of numbers to get another number. For example, when we perform the</p>

          <p class="calibre1">operation of addition on the pair of numbers 5 and 13, we get the number 18. Again, performing the operation of multiplication on the pair of numbers 5 and 13, we get 65.</p>

          <p class="calibre1">Similarly, there are some operations which when performed on two sets give rise to another set. We will now define certain operations on sets and examine their properties.</p>

          <p class="calibre1">Henceforth, we will refer all our sets as subsets of some universal set.</p>

          <p class="calibre1"><b class="calibre4">1.10.1 <i class="calibre5">Union of sets</i></b> Let A and B be any two sets. The union of A and B is the set which consists of all the elements of A and all the elements of B, the common elements being taken only once. The symbol ‘∪’ is used to denote the <i class="calibre5">union</i>. <i class="calibre5">Symbolically, we</i> <i class="calibre5">write</i> A ∪ B <i class="calibre5">and usually read as <b class="calibre4">‘</b>A union B<b class="calibre4">’</b></i>.</p>

          <p>&lt; span class="highlight"&gt;Example 12Let A = { 2, 4, 6, 8} and B = { 6, 8, 10, 12}. Find A ∪ B.</p>

          <p class="calibre1"><b class="calibre4">Solution</b> We have A ∪ B = { 2, 4, 6, 8, 10, 12}</p>

          <p class="calibre1">Note that the common elements 6 and 8 have been taken only once while writing A ∪ B.</p>

          <p>&lt; span class="highlight"&gt;Example 13Let A = { <i class="calibre5">a, e, i, o, u</i> } and B = { <i class="calibre5">a, i, u</i> }. Show that A ∪ B = A <b class="calibre4">Solution</b> We have, A ∪ B = { <i class="calibre5">a, e, i, o, u</i> } = A.</p>

          <p class="calibre1">This example illustrates that union of sets A and its subset B is the set A</p>

          <p class="calibre1">itself, i.e., if B ⊂ A, then A ∪ B = A.</p>

          <p>&lt; span class="highlight"&gt;Example 14Let X = {Ram, Geeta, Akbar} be the set of students of Class XI, who are</p>

          <p class="calibre1"></p>

          <p class="calibre1">in school hockey team. Let Y = {Geeta, David, Ashok} be the set of students from Class XI who are in the school football team. Find X ∪ Y and interpret the set.</p>

          <p class="calibre1"><b class="calibre4">Solution</b> We have, X ∪ Y = {Ram, Geeta, Akbar, David, Ashok}. This is the set of not to be republished</p>

          <p class="calibre1">students from Class XI who are in the hockey team or the football team or both.</p>

          <p class="calibre1">SETS 15</p>

          <p class="calibre1">Thus, we can define the union of two sets as follows:</p>

          <p class="calibre1"><b class="calibre4">Definition 6</b> The union of two sets A and B is the set C which consists of all those elements which are either in A or in B (including</p>

          <p class="calibre1">those which are in both). In symbols, we write.</p>

          <p class="calibre1">A ∪ B = { <i class="calibre5">x</i> : <i class="calibre5">x</i> ∈A or <i class="calibre5">x</i> ∈B }</p>

          <p class="calibre1">The union of two sets can be represented by a</p>

          <p class="calibre1">Venn diagram as shown in Fig 1.4.</p>

          <p class="calibre1">The shaded portion in Fig 1.4 represents A ∪ B.</p>

          <p class="calibre1"><b class="calibre4">Some Properties of the Operation of Union</b></p>

          <p class="calibre1">(i) A ∪ B = B ∪ A (Commutative law)</p>

          <p class="calibre1"><b class="calibre4">Fig 1.4</b></p>

          <p class="calibre1">(ii) ( A ∪ B ) ∪ C = A ∪ ( B ∪ C)</p>

          <p class="calibre1">(Associative law )</p>

          <p class="calibre1">(iii) A ∪ φ = A (Law of identity element, φ is the identity of ∪)</p>

          <p class="calibre1">(iv) A ∪ A = A (Idempotent law)</p>

          <p class="calibre1">(v) U ∪ A = U (Law of U)</p>

          <p class="calibre1"><b class="calibre4">1.10.2 <i class="calibre5">Intersection of sets</i></b> The intersection of sets A and B is the set of all elements which are common to both A and B. The symbol ‘∩’ is used to denote the <i class="calibre5">intersection</i>.</p>

          <p class="calibre1">The intersection of two sets A and B is the set of all those elements which belong to both A and B. Symbolically, we write A ∩ B = { <i class="calibre5">x</i> : <i class="calibre5">x</i> ∈ A and <i class="calibre5">x</i> ∈ B}.</p>

          <p>&lt; span class="highlight"&gt;Example 15Consider the sets A and B of Example 12. Find A ∩ B.</p>

          <p class="calibre1"><b class="calibre4">Solution</b> We see that 6, 8 are the only elements which are common to both A and B.</p>

          <p class="calibre1">Hence A ∩ B = { 6, 8 }.</p>

          <p>&lt; span class="highlight"&gt;Example 16Consider the sets X and Y of Example 14. Find X ∩ Y.</p>

          <p class="calibre1"><b class="calibre4">Solution</b> We see that element ‘Geeta’ is the only element common to both. Hence, X ∩ Y = {Geeta}.</p>

          <p class="highlight">Example 17</p>Let A = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10} and B = { 2, 3, 5, 7 }. Find A ∩ B and hence show that A ∩ B = B.

          <p class="calibre1"><b class="calibre4">Solution</b> We have A ∩ B = { 2, 3, 5, 7 } = B. We</p>

          <p class="calibre1">note that B ⊂ A and that A ∩ B = B.</p>

          <p class="calibre1"><b class="calibre4">Definition 7</b> The intersection of two sets A and B</p>

          <p class="calibre1">is the set of all those elements which belong to both</p>

          <p class="calibre1"></p>

          <p class="calibre1">A and B. Symbolically, we write</p>

          <p class="calibre1">A ∩ B = { <i class="calibre5">x</i> : <i class="calibre5">x</i> ∈ A and <i class="calibre5">x</i> ∈ B}</p>

          <p class="calibre1">The shaded portion in Fig 1.5 indicates the</p>

          <p class="calibre1">not to be republished</p>

          <p class="calibre1">intersection of A and B.</p>

          <p class="calibre1"><b class="calibre4">Fig 1.5</b></p>

          <p class="calibre1">16 MATHEMATICS</p>

          <p class="calibre1">If A and B are two sets such that A ∩ B = φ, then <b class="calibre4">U</b></p>

          <p class="calibre1">A and B are called <i class="calibre5">disjoint sets.</i></p>

          <p class="calibre1">For example, let A = { 2, 4, 6, 8 } and</p>

          <p class="calibre1">B = { 1, 3, 5, 7 }. Then A and B are disjoint sets,</p>

          <p class="calibre1"><b class="calibre4">A</b></p>

          <p class="calibre1"><b class="calibre4">B</b></p>

          <p class="calibre1">because there are no elements which are common to</p>

          <p class="calibre1">A and B. The disjoint sets can be represented by</p>

          <p class="calibre1">means of Venn diagram as shown in the Fig 1.6</p>

          <p class="calibre1">In the above diagram, A and B are disjoint sets.</p>

          <p class="calibre1"><b class="calibre4">Fig 1.6</b></p>

          <p class="calibre1"><b class="calibre4">Some Properties of Operation of Intersection</b></p>

          <p class="calibre1">(i) A ∩ B = B ∩ A</p>

          <p class="calibre1">(Commutative law).</p>

          <p class="calibre1">(ii) ( A ∩ B ) ∩ C = A ∩ ( B ∩ C )</p>

          <p class="calibre1">(Associative law).</p>

          <p class="calibre1">(iii) φ ∩ A = φ, U ∩ A = A</p>

          <p class="calibre1">(Law of φ and U).</p>

          <p class="calibre1">(iv) A ∩ A = A</p>

          <p class="calibre1">(Idempotent law)</p>

          <p class="calibre1">(v) A ∩ ( B ∪ C ) = ( A ∩ B ) ∪ ( A ∩ C ) (Distributive law ) i. e.,</p>

          <p class="calibre1">∩ distributes over ∪</p>

          <p class="calibre1">This can be seen easily from the following Venn diagrams [Figs 1.7 (i) to (v)].</p>

          <p class="calibre1">(i)</p>

          <p class="calibre1">(iii)</p>

          <p class="calibre1">(ii)</p>

          <p class="calibre1">(iv)</p>

          <p class="calibre1">(v)</p>

          <p class="calibre1">not to be republished</p>

          <p class="calibre1"><b class="calibre4">Figs 1.7 (i) to (v)</b></p>

          <p class="calibre1">SETS 17</p>

          <p class="calibre1"><b class="calibre4">1.10.3 <i class="calibre5">Difference of sets</i></b> The difference of the sets A and B in this order is the set of elements which belong to A but not to B. Symbolically, we write A – B and read as</p>

          <p class="calibre1">“ A minus B”.</p>

          <p class="highlight">Example 18</p>Let A = { 1, 2, 3, 4, 5, 6}, B = { 2, 4, 6, 8 }. Find A – B and B – A.

          <p class="calibre1"><b class="calibre4">Solution</b> We have, A – B = { 1, 3, 5 }, since the elements 1, 3, 5 belong to A but not to B and B – A = { 8 }, since the element 8 belongs to B and not to A.</p>

          <p class="calibre1">We note that A – B ≠ B – A.</p>

          <p class="highlight">Example 19</p>Let V = { <i class="calibre5">a, e, i, o, u</i> } and

          <p class="calibre1">B = { <i class="calibre5">a, i, k, u</i>}. Find V – B and B – V</p>

          <p class="calibre1"><b class="calibre4">Solution</b> We have, V – B = { <i class="calibre5">e, o</i> }, since the elements</p>

          <p class="calibre1"><i class="calibre5">e, o</i> belong to V but not to B and B – V = { <i class="calibre5">k</i> }, since</p>

          <p class="calibre1">the element <i class="calibre5">k</i> belongs to B but not to V.</p>

          <p class="calibre1">We note that V – B ≠ B – V. Using the set-</p>

          <p class="calibre1">builder notation, we can rewrite the definition of</p>

          <p class="calibre1"><b class="calibre4">Fig 1.8</b></p>

          <p class="calibre1">difference as</p>

          <p class="calibre1">A – B = { <i class="calibre5">x</i> : <i class="calibre5">x</i> ∈ A and <i class="calibre5">x</i> ∉ B }</p>

          <p class="calibre1">The difference of two sets A and B can be</p>

          <p class="calibre1">represented by Venn diagram as shown in Fig 1.8.</p>

          <p class="calibre1">The shaded portion represents the difference of</p>

          <p class="calibre1">the two sets A and B.</p>

          <p class="calibre1"><i class="calibre5"><b class="calibre4">Remark</b></i> The sets A – B, A ∩ B and B – A are</p>

          <p class="calibre1">mutually disjoint sets, i.e., the intersection of any of</p>

          <p class="calibre1"><b class="calibre4">Fig 1.9</b></p>

          <p class="calibre1">these two sets is the null set as shown in Fig 1.9.</p>

          <p class="calibre1"><b class="calibre4">EXERCISE 1.4</b></p>

          <p class="calibre1"><b class="calibre4">1.</b> Find the union of each of the following pairs of sets :</p>

          <p class="calibre1">(i) X = {1, 3, 5}</p>

          <p class="calibre1">Y = {1, 2, 3}</p>

          <p class="calibre1">(ii) A = [ <i class="calibre5">a, e, i, o, u</i>}</p>

          <p class="calibre1">B = { <i class="calibre5">a, b, c</i>}</p>

          <p class="calibre1">(iii) A = { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a natural number and multiple of 3}</p>

          <p class="calibre1">B = { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a natural number less than 6}</p>

          <p class="calibre1">(iv) A = { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a natural number and 1 &lt; <i class="calibre5">x</i> ≤ 6 }</p>

          <p class="calibre1">B = {</p>

          <p class="calibre1"></p>

          <p class="calibre1"><i class="calibre5">x</i> : <i class="calibre5">x</i> is a natural number and 6 &lt; <i class="calibre5">x &lt;</i> 10 }</p>

          <p class="calibre1">(v) A = {1, 2, 3}, B = φ</p>

          <p class="calibre1"><b class="calibre4">2.</b> Let A = { <i class="calibre5">a, b</i> }, B = { <i class="calibre5">a, b, c</i>}. Is A ⊂ B ? What is A ∪ B ?</p>

          <p class="calibre1"><b class="calibre4">3.</b> If A and B are two sets such that A ⊂ B, then what is A ∪ B ?</p>

          <p class="calibre1">not to be republished</p>

          <p class="calibre1"><b class="calibre4">4.</b> If A = {1, 2, 3, 4}, B = {3, 4, 5, 6}, C = {5, 6, 7, 8 }and D = { 7, 8, 9, 10 }; find 18 MATHEMATICS</p>

          <p class="calibre1">(i) A ∪ B</p>

          <p class="calibre1">(ii) A ∪ C</p>

          <p class="calibre1">(iii) B ∪ C</p>

          <p class="calibre1">(iv) B ∪ D</p>

          <p class="calibre1">(v) A ∪ B ∪ C</p>

          <p class="calibre1">(vi) A ∪ B ∪ D</p>

          <p class="calibre1">(vii) B ∪ C ∪ D</p>

          <p class="calibre1"><b class="calibre4">5.</b> Find the intersection of each pair of sets of question 1 above.</p>

          <p class="calibre1"><b class="calibre4">6.</b> If A = { 3, 5, 7, 9, 11 }, B = {7, 9, 11, 13}, C = {11, 13, 15}and D = {15, 17}; find (i) A ∩ B</p>

          <p class="calibre1">(ii) B ∩ C</p>

          <p class="calibre1">(iii) A ∩ C ∩ D</p>

          <p class="calibre1">(iv) A ∩ C</p>

          <p class="calibre1">(v) B ∩ D</p>

          <p class="calibre1">(vi) A ∩ (B ∪ C)</p>

          <p class="calibre1">(vii) A ∩ D</p>

          <p class="calibre1">(viii) A ∩ (B ∪ D)</p>

          <p class="calibre1">(ix) ( A ∩ B ) ∩ ( B ∪ C )</p>

          <p class="calibre1">(x) ( A ∪ D) ∩ ( B ∪ C)</p>

          <p class="calibre1"><b class="calibre4">7.</b> If A = { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a natural number }, B = { <i class="calibre5">x</i> : <i class="calibre5">x</i> is an even natural number}</p>

          <p class="calibre1">C = { <i class="calibre5">x</i> : <i class="calibre5">x</i> is an odd natural number}andD = { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a prime number }, find (i) A ∩ B</p>

          <p class="calibre1">(ii) A ∩ C</p>

          <p class="calibre1">(iii) A ∩ D</p>

          <p class="calibre1">(iv) B ∩ C</p>

          <p class="calibre1">(v) B ∩ D</p>

          <p class="calibre1">(vi) C ∩ D</p>

          <p class="calibre1"><b class="calibre4">8.</b> Which of the following pairs of sets are disjoint</p>

          <p class="calibre1">(i) {1, 2, 3, 4} and { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a natural number and 4 ≤ <i class="calibre5">x</i> ≤ 6 }</p>

          <p class="calibre1">(ii) { <i class="calibre5">a, e, i, o, u</i> } and { <i class="calibre5">c, d, e, f</i> }</p>

          <p class="calibre1">(iii) { <i class="calibre5">x</i> : <i class="calibre5">x</i> is an even integer } and { <i class="calibre5">x</i> : <i class="calibre5">x</i> is an odd integer}</p>

          <p class="calibre1"><b class="calibre4">9.</b> If A = {3, 6, 9, 12, 15, 18, 21}, B = { 4, 8, 12, 16, 20 },</p>

          <p class="calibre1">C = { 2, 4, 6, 8, 10, 12, 14, 16 }, D = {5, 10, 15, 20 }; find (i) A – B</p>

          <p class="calibre1">(ii) A – C</p>

          <p class="calibre1">(iii) A – D</p>

          <p class="calibre1">(iv) B – A</p>

          <p class="calibre1">(v) C – A</p>

          <p class="calibre1">(vi) D – A</p>

          <p class="calibre1">(vii) B – C</p>

          <p class="calibre1">(viii) B – D</p>

          <p class="calibre1">(ix) C – B</p>

          <p class="calibre1">(x) D – B</p>

          <p class="calibre1">(xi) C – D</p>

          <p class="calibre1">(xii) D – C</p>

          <p class="calibre1"><b class="calibre4">10.</b> If X= { <i class="calibre5">a, b, c, d</i> } and Y = { <i class="calibre5">f, b, d, g</i>}, find</p>

          <p class="calibre1">(i) X – Y</p>

          <p class="calibre1">(ii) Y – X</p>

          <p class="calibre1">(iii) X ∩ Y</p>

          <p class="calibre1"><b class="calibre4">11.</b></p>

          <p class="calibre1">If <b class="calibre4">R</b> is the set of real numbers and <b class="calibre4">Q</b> is the set of rational numbers, then what is <b class="calibre4">R</b> – <b class="calibre4">Q</b>?</p>

          <p class="calibre1"><b class="calibre4">12.</b> State whether each of the following statement is true or false. Justify your answer.</p>

          <p class="calibre1">(i) { 2, 3, 4, 5 } and { 3, 6} are disjoint sets.</p>

          <p class="calibre1">(ii) { <i class="calibre5">a, e, i, o, u</i> } and { <i class="calibre5">a, b, c, d</i> }are disjoint sets.</p>

          <p class="calibre1">(iii) { 2, 6, 10, 14 } and { 3, 7, 11, 15} are disjoint sets.</p>

          <p class="calibre1">(iv) { 2, 6, 10 } and { 3, 7, 11} are disjoint sets.</p>

          <p class="calibre1"><b class="calibre4">1.11 Complement of a Set</b></p>

          <p class="calibre1">Let U be the universal set which consists of all prime numbers and A be the subset of U which consists of all those prime numbers that are not divisors of 42. Thus,</p>

          <p class="calibre1"></p>

          <p class="calibre1">A = { <i class="calibre5">x</i> : <i class="calibre5">x</i> ∈ U and <i class="calibre5">x</i> is not a divisor of 42 }. We see that 2 ∈ U but 2 ∉ A, because 2 is divisor of 42. Similarly, 3 ∈ U but 3 ∉ A, and 7 ∈ U but 7 ∉ A. Now 2, 3 and 7 are the only elements of U which do not belong to A. The set of these three prime numbers, i.e., the set {2, 3, 7} is called the</p>

          <p class="calibre1">not to be republished</p>

          <p class="calibre1"><i class="calibre5">Complement</i> of A with respect to U, and is denoted by</p>

          <p class="calibre1">SETS 19</p>

          <p class="calibre1">A′. So we have A′ = {2, 3, 7}. Thus, we see that</p>

          <p class="calibre1">A′ = { <i class="calibre5">x</i> : <i class="calibre5">x</i> ∈ U and <i class="calibre5">x</i> ∉ A }. This leads to the following definition.</p>

          <p class="calibre1"><b class="calibre4">Definition 8</b> Let U be the universal set and A a subset of U. Then the complement of A is the set of all elements of U which are not the elements of A. Symbolically, we write A′ to denote the complement of A with respect to U. Thus,</p>

          <p class="calibre1">A′ = { <i class="calibre5">x</i> : <i class="calibre5">x</i> ∈ U and <i class="calibre5">x</i> ∉ A }. Obviously A′ = U – A</p>

          <p class="calibre1">We note that the complement of a set A can be looked upon, alternatively, as the difference between a universal set U and the set A.</p>

          <p class="highlight">Example 20</p>Let U = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10} and A = {1, 3, 5, 7, 9}. Find A′.

          <p class="calibre1"><b class="calibre4">Solution</b> We note that 2, 4, 6, 8, 10 are the only elements of U which do not belong to A. Hence</p>

          <p class="calibre1">A′ = { 2, 4, 6, 8,10 }.</p>

          <p class="highlight">Example 21</p>Let U be universal set of all the students of Class XI of a coeducational school and A be the set of all girls in Class XI. Find A′.

          <p class="calibre1"><b class="calibre4">Solution</b> Since A is the set of all girls, A′ is clearly the set of all boys in the class.</p>

          <div class="box">
            <p><span class="note_bg">Note</span> If A is a subset of the universal set U, then its complement A′ is also a subset of U.</p>

            <p class="calibre1">Again in Example 20 above, we have A′ = { 2, 4, 6, 8, 10 }</p>

            <p class="calibre1">Hence (A′ )′ = { <i class="calibre5">x</i> : <i class="calibre5">x</i> ∈ U and <i class="calibre5">x</i> ∉ A′}</p>

            <p class="calibre1">= {1, 3, 5, 7, 9} = A</p>

            <p class="calibre1">It is clear from the definition of the complement that for any subset of the universal set U, we have ( A′ )′ = A</p>

            <p class="calibre1">Now, we want to find the results for ( A ∪ B )′ and A′ ∩ B′ in the followng example.</p>

            <p class="highlight">Example 22</p>Let U = {1, 2, 3, 4, 5, 6}, A = {2, 3} and B = {3, 4, 5}.

            <p class="calibre1">Find A′, B′ , A′ ∩ B′, A ∪ B and hence show that ( A ∪ B )′ = A′ ∩ B′.</p>

            <p class="calibre1"><b class="calibre4">Solution</b> Clearly A′ = {1, 4, 5, 6}, B′ = { 1, 2, 6 }. Hence A′ ∩ B′ = { 1, 6 }</p>

            <p class="calibre1">Also A ∪ B = { 2, 3, 4, 5 }, so that (A ∪ B )′ = { 1, 6 }</p>

            <p class="calibre1">( A ∪ B )′ = { 1, 6 } = A′ ∩ B′</p>

            <p class="calibre1"></p>

            <p class="calibre1">It can be shown that the above result is true in general. If A and B are any two subsets of the universal set U, then</p>

            <p class="calibre1">( A ∪ B )′ = A′ ∩ B′. Similarly, ( A ∩ B )′ = A′ ∪ B′ . These two results are stated not to be republished</p>

            <p class="calibre1">in words as follows :</p>

            <p class="calibre1">20 MATHEMATICS</p>

            <p class="calibre1"><i class="calibre5">The complement of the union of two sets is</i></p>

            <p class="calibre1"><i class="calibre5">the intersection of their complements and the</i></p>

            <p class="calibre1"><i class="calibre5">complement of the intersection of two sets is the</i></p>

            <p class="calibre1"><i class="calibre5">union of their complements.</i> These are called <i class="calibre5">De</i></p>

            <p class="calibre1"><i class="calibre5">Morgan’s laws</i>. These are named after the</p>

            <p class="calibre1">mathematician De Morgan.</p>

            <p class="calibre1">The complement A′ of a set A can be represented</p>

            <p class="calibre1">by a Venn diagram as shown in Fig 1.10.</p>

            <p class="calibre1"><b class="calibre4">Fig 1.10</b></p>

            <p class="calibre1">The shaded portion represents the complement of the set A.</p>

            <p class="calibre1"><b class="calibre4">Some Properties of Complement Sets</b></p>

            <p class="calibre1"><b class="calibre4">1.</b> Complement laws:</p>

            <p class="calibre1">(i) A ∪ A′ = U</p>

            <p class="calibre1">(ii) A ∩ A′ = φ</p>

            <p class="calibre1"><b class="calibre4">2.</b> De Morgan’s law:</p>

            <p class="calibre1">(i) (A ∪ B)´ = A′ ∩ B′ (ii) (A ∩ B )′ = A′ ∪ B′</p>

            <p class="calibre1"><b class="calibre4">3.</b> Law of double complementation : (A′ )′ = A</p>

            <p class="calibre1"><b class="calibre4">4.</b> Laws of empty set and universal set φ′ = U and U′ = φ.</p>

            <p class="calibre1">These laws can be verified by using Venn diagrams.</p>

            <p class="calibre1"><b class="calibre4">EXERCISE 1.5</b></p>

            <p class="calibre1"><b class="calibre4">1.</b></p>

            <p class="calibre1">Let U = { 1, 2, 3, 4, 5, 6, 7, 8, 9 }, A = { 1, 2, 3, 4}, B = { 2, 4, 6, 8 } and C = { 3, 4, 5, 6 }. Find (i) A′ (ii) B′ (iii) (A ∪ C)′ (iv) (A ∪ B)′ (v) (A′)′</p>

            <p class="calibre1">(vi) (B – C)′</p>

            <p class="calibre1"><b class="calibre4">2.</b></p>

            <p class="calibre1">If U = { <i class="calibre5">a, b, c, d, e, f, g, h</i>}, find the complements of the following sets : (i) A = { <i class="calibre5">a, b, c</i>}</p>

            <p class="calibre1">(ii) B = { <i class="calibre5">d, e, f, g</i>}</p>

            <p class="calibre1">(iii) C = { <i class="calibre5">a, c, e, g</i>}</p>

            <p class="calibre1">(iv) D = { <i class="calibre5">f, g, h, a</i>}</p>

            <p class="calibre1"><b class="calibre4">3.</b></p>

            <p class="calibre1">Taking the set of natural numbers as the universal set, write down the complements of the following sets:</p>

            <p class="calibre1">(i)</p>

            <p class="calibre1">{ <i class="calibre5">x</i> : <i class="calibre5">x</i> is an even natural number}</p>

            <p class="calibre1">(ii) { <i class="calibre5">x</i> : <i class="calibre5">x</i> is an odd natural number }</p>

            <p class="calibre1">(iii) { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a positive multiple of 3}</p>

            <p class="calibre1">(iv) { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a prime number }</p>

            <p class="calibre1">(v) { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a natural number divisible by 3 and 5}</p>

            <p class="calibre1">(vi) { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a perfect square }</p>

            <p class="calibre1">(vii) { <i class="calibre5">x</i> : <i class="calibre5">x</i> is a perfect cube}</p>

            <p class="calibre1">(viii) { <i class="calibre5">x</i> : <i class="calibre5">x</i> + 5 = 8 }</p>

            <p class="calibre1">(ix) { <i class="calibre5">x</i> : 2 <i class="calibre5">x</i> + 5 = 9}</p>

            <p class="calibre1">(x) { <i class="calibre5">x</i> : <i class="calibre5">x</i> ≥ 7 }</p>

            <p class="calibre1">(xi) { <i class="calibre5">x</i> : <i class="calibre5">x</i> ∈ N and 2 <i class="calibre5">x</i> + 1 &gt; 10 }</p>

            <p class="calibre1"><b class="calibre4">4.</b></p>

            <p class="calibre1">If U = {1, 2, 3, 4, 5, 6, 7, 8, 9 }, A = {2, 4, 6, 8} and B = { 2, 3, 5, 7}. Verify that (i) (A ∪ B)′ = A′ ∩ B′</p>

            <p class="calibre1">(ii) (A ∩ B)′ = A′ ∪ B′</p>

            <p class="calibre1"></p>

            <p class="calibre1"><b class="calibre4">5.</b></p>

            <p class="calibre1">Draw appropriate Venn diagram for each of the following :</p>

            <p class="calibre1">(i) (A ∪ B)′,</p>

            <p class="calibre1">(ii) A′ ∩ B′,</p>

            <p class="calibre1">(iii) (A ∩ B)′, (iv) A′ ∪ B′</p>

            <p class="calibre1"><b class="calibre4">6.</b></p>

            <p class="calibre1">Let U be the set of all triangles in a plane. If A is the set of all triangles with at least one angle different from 60°, what is A′?</p>

            <p class="calibre1">not to be republished</p>

            <p class="calibre1">SETS 21</p>

            <p class="calibre1"><b class="calibre4">7.</b></p>

            <p class="calibre1">Fill in the blanks to make each of the following a true statement :</p>

            <p class="calibre1">(i)</p>

            <p class="calibre1">A ∪ A′ = . . .</p>

            <p class="calibre1">(ii)</p>

            <p class="calibre1">φ′ ∩ A = . . .</p>

            <p class="calibre1">(iii) A ∩ A′ = . . .</p>

            <p class="calibre1">(iv)</p>

            <p class="calibre1">U′ ∩ A = . . .</p>

            <p class="calibre1"><b class="calibre4">1.12 Practical Problems on Union and</b></p>

            <p class="calibre1"><b class="calibre4">Intersection of Two Sets</b></p>

            <p class="calibre1">In earlier Section, we have learnt union, intersection</p>

            <p class="calibre1">and difference of two sets. In this Section, we will</p>

            <p class="calibre1">go through some practical problems related to our</p>

            <p class="calibre1">daily life.The formulae derived in this Section will</p>

            <p class="calibre1">also be used in subsequent Chapter on Probability</p>

            <p class="calibre1">(Chapter 16).</p>

            <p class="calibre1"><b class="calibre4">Fig 1.11</b></p>

            <p class="calibre1">Let A and B be finite sets. If A ∩ B = φ, then</p>

            <p class="calibre1">(i) <i class="calibre5">n</i> ( A ∪ B ) = <i class="calibre5">n</i> ( A ) + <i class="calibre5">n</i> ( B ) ... (1) The elements in A ∪ B are either in A or in B but not in both as A ∩ B = φ. So, (1) follows immediately.</p>

            <p class="calibre1">In general, if A and B are finite sets, then</p>

            <p class="calibre1">(ii) <i class="calibre5">n</i> ( A ∪ B ) = <i class="calibre5">n</i> ( A ) + <i class="calibre5">n</i> ( B ) – <i class="calibre5">n</i> ( A ∩ B )</p>

            <p class="calibre1">... (2)</p>

            <p class="calibre1">Note that the sets A – B, A ∩ B and B – A are disjoint and their union is A ∪ B</p>

            <p class="calibre1">(Fig 1.11). Therefore</p>

            <p class="calibre1"><i class="calibre5">n</i> ( A ∪ B) = <i class="calibre5">n</i> ( A – B) + <i class="calibre5">n</i> ( A ∩ B ) + <i class="calibre5">n</i> ( B – A )</p>

            <p class="calibre1">= <i class="calibre5">n</i> ( A – B) + <i class="calibre5">n</i> ( A ∩ B ) + <i class="calibre5">n</i> ( B – A ) + <i class="calibre5">n</i> ( A ∩ B ) – <i class="calibre5">n</i> ( A ∩ B)</p>

            <p class="calibre1">= <i class="calibre5">n</i> ( A ) + <i class="calibre5">n</i> ( B ) – <i class="calibre5">n</i> ( A ∩ B), which verifies (2) (iii) If A, B and C are finite sets, then</p>

            <p class="calibre1"><i class="calibre5">n</i> ( A ∪ B ∪ C ) = <i class="calibre5">n</i> ( A ) + <i class="calibre5">n</i> ( B ) + <i class="calibre5">n</i> ( C ) – <i class="calibre5">n</i> ( A ∩ B ) – <i class="calibre5">n</i> ( B ∩ C)</p>

            <p class="calibre1">– <i class="calibre5">n</i> ( A ∩ C ) + <i class="calibre5">n</i> ( A ∩ B ∩ C )</p>

            <p class="calibre1">... (3)</p>

            <p class="calibre1">In fact, we have</p>

            <p class="calibre1"><i class="calibre5">n</i> ( A ∪ B ∪ C ) = <i class="calibre5">n</i> (A) + <i class="calibre5">n</i> ( B ∪ C ) – <i class="calibre5">n</i> [ A ∩ ( B ∪ C ) ]</p>

            <p class="calibre1">[ by (2) ]</p>

            <p class="calibre1">= <i class="calibre5">n</i> (A) + <i class="calibre5">n</i> ( B ) + <i class="calibre5">n</i> ( C ) – <i class="calibre5">n</i> ( B ∩ C ) – <i class="calibre5">n</i> [ A ∩ ( B ∪ C ) ]</p>

            <p class="calibre1">[ by (2) ]</p>

            <p class="calibre1">Since A ∩ ( B ∪ C ) = ( A ∩ B ) ∪ ( A ∩ C ), we get</p>

            <p class="calibre1"><i class="calibre5">n</i> [ A ∩ ( B ∪ C ) ] = <i class="calibre5">n</i> ( A ∩ B ) + <i class="calibre5">n</i> ( A ∩ C ) – <i class="calibre5">n</i> [ ( A ∩ B ) ∩ (A ∩ C)]</p>

            <p class="calibre1">= <i class="calibre5">n</i> ( A ∩ B ) + <i class="calibre5">n</i> ( A ∩ C ) – <i class="calibre5">n</i> (A ∩ B ∩ C)</p>

            <p class="calibre1">Therefore</p>

            <p class="calibre1"><i class="calibre5">n</i> ( A ∪ B ∪ C ) = <i class="calibre5">n</i> (A) + <i class="calibre5">n</i> ( B ) + <i class="calibre5">n</i> ( C ) – <i class="calibre5">n</i> ( A ∩ B ) – <i class="calibre5">n</i> ( B ∩ C)</p>

            <p class="calibre1"></p>

            <p class="calibre1">– <i class="calibre5">n</i> ( A ∩ C ) + <i class="calibre5">n</i> ( A ∩ B ∩ C )</p>

            <p class="calibre1">This proves (3).</p>

            <p class="highlight">Example 23</p>If X and Y are two sets such that X ∪ Y has 50 elements, X has not to be republished

            <p class="calibre1">28 elements and Y has 32 elements, how many elements does X ∩ Y have ?</p>

            <p class="calibre1">22 MATHEMATICS</p>

            <p class="calibre1"><b class="calibre4">Solution</b> Given that</p>

            <p class="calibre1"><i class="calibre5">n</i> ( X ∪ Y ) = 50, <i class="calibre5">n</i> ( X ) = 28, <i class="calibre5">n</i> ( Y ) = 32,</p>

            <p class="calibre1"><i class="calibre5">n</i> (X ∩ Y) = ?</p>

            <p class="calibre1">By using the formula</p>

            <p class="calibre1"><i class="calibre5">n</i> ( X ∪ Y ) = <i class="calibre5">n</i> ( X ) + <i class="calibre5">n</i> ( Y ) – <i class="calibre5">n</i> ( X ∩ Y ), we find that</p>

            <p class="calibre1"><i class="calibre5">n</i> ( X ∩ Y ) = <i class="calibre5">n</i> ( X ) + <i class="calibre5">n</i> ( Y ) – <i class="calibre5">n</i> ( X ∪ Y )</p>

            <p class="calibre1">= 28 + 32 – 50 = 10</p>

            <p class="calibre1"><b class="calibre4">Fig 1.12</b></p>

            <p class="calibre1"><b class="calibre4">Alternatively</b>, suppose <i class="calibre5">n</i> ( X ∩ Y ) = <i class="calibre5">k</i>, then</p>

            <p class="calibre1"><i class="calibre5">n</i> ( X – Y ) = 28 – <i class="calibre5">k</i> , <i class="calibre5">n</i> ( Y – X ) = 32 – <i class="calibre5">k</i> (by Venn diagram in Fig 1.12 ) This gives 50 = <i class="calibre5">n</i> ( X ∪ Y ) = <i class="calibre5">n</i> (X – Y) + <i class="calibre5">n</i> (X ∩ Y) + <i class="calibre5">n</i> ( Y – X)</p>

            <p class="calibre1">= ( 28 – <i class="calibre5">k</i> ) + <i class="calibre5">k</i> + (32 – <i class="calibre5">k</i> )</p>

            <p class="calibre1">Hence</p>

            <p class="calibre1"><i class="calibre5">k</i> = 10.</p>

            <p class="highlight">Example 24</p>In a school there are 20 teachers who teach mathematics or physics. Of these, 12 teach mathematics and 4 teach both physics and mathematics. How many teach physics ?

            <p class="calibre1"><b class="calibre4">Solution</b> Let M denote the set of teachers who teach mathematics and P denote the set of teachers who teach physics. In the statement of the problem, the word ‘or’ gives us a clue of union and the word ‘and’ gives us a clue of intersection. We, therefore, have</p>

            <p class="calibre1"><i class="calibre5">n</i> ( M ∪ P ) = 20 , <i class="calibre5">n</i> ( M ) = 12 and <i class="calibre5">n</i> ( M ∩ P ) = 4</p>

            <p class="calibre1">We wish to determine <i class="calibre5">n</i> ( P ).</p>

            <p class="calibre1">Using the result</p>

            <p class="calibre1"><i class="calibre5">n</i> ( M ∪ P ) = <i class="calibre5">n</i> ( M ) + <i class="calibre5">n</i> ( P ) – <i class="calibre5">n</i> ( M ∩ P ), we obtain</p>

            <p class="calibre1">20 = 12 + <i class="calibre5">n</i> ( P ) – 4</p>

            <p class="calibre1">Thus</p>

            <p class="calibre1"><i class="calibre5">n</i> ( P ) = 12</p>

            <p class="calibre1">Hence 12 teachers teach physics.</p>

            <p class="highlight">Example 25</p>In a class of 35 students, 24 like to play cricket and 16 like to play football. Also, each student likes to play at least one of the two games. How many students like to play both cricket and football ?

            <p class="calibre1"><b class="calibre4">Solution</b> Let X be the set of students who like to play cricket and Y be the set of</p>

            <p class="calibre1"></p>

            <p class="calibre1">students who like to play football. Then X ∪ Y is the set of students who like to play at least one game, and X ∩ Y is the set of students who like to play both games.</p>

            <p class="calibre1">Given</p>

            <p class="calibre1"><i class="calibre5">n</i> ( X) = 24, <i class="calibre5">n</i> ( Y ) = 16, <i class="calibre5">n</i> ( X ∪ Y ) = 35, <i class="calibre5">n</i> (X ∩ Y) = ?</p>

            <p class="calibre1">Using the formula <i class="calibre5">n</i> ( X ∪ Y ) = <i class="calibre5">n</i> ( X ) + <i class="calibre5">n</i> ( Y ) – <i class="calibre5">n</i> ( X ∩ Y ), we get not to be republished</p>

            <p class="calibre1">35 = 24 + 16 – <i class="calibre5">n</i> (X ∩ Y)</p>

            <p class="calibre1">SETS 23</p>

            <p class="calibre1">Thus,</p>

            <p class="calibre1"><i class="calibre5">n</i> (X ∩ Y) = 5</p>

            <p class="calibre1">i.e.,</p>

            <p class="calibre1">5 students like to play both games.</p>

            <p class="highlight">Example 26</p>In a survey of 400 students in a school, 100 were listed as taking apple juice, 150 as taking orange juice and 75 were listed as taking both apple as well as orange juice. Find how many students were taking neither apple juice nor orange juice.

            <p class="calibre1"><b class="calibre4">Solution</b> Let U denote the set of surveyed students and A denote the set of students taking apple juice and B denote the set of students taking orange juice. Then <i class="calibre5">n</i> (U) = 400, <i class="calibre5">n</i> (A) = 100, <i class="calibre5">n</i> (B) = 150 and <i class="calibre5">n</i> (A ∩ B) = 75.</p>

            <p class="calibre1">Now</p>

            <p class="calibre1"><i class="calibre5">n</i> (A′ ∩ B′) = <i class="calibre5">n</i> (A ∪ B)′</p>

            <p class="calibre1">= <i class="calibre5">n</i> (U) – <i class="calibre5">n</i> (A ∪ B)</p>

            <p class="calibre1">= <i class="calibre5">n</i> (U) – <i class="calibre5">n</i> (A) – <i class="calibre5">n</i> (B) + <i class="calibre5">n</i> (A ∩ B)</p>

            <p class="calibre1">= 400 – 100 – 150 + 75 = 225</p>

            <p class="calibre1">Hence 225 students were taking neither apple juice nor orange juice.</p>

            <p class="highlight">Example 27</p>There are 200 individuals with a skin disorder, 120 had been exposed to the chemical C , 50 to chemical C , and 30 to both the chemicals C and C . Find the 1

            <p class="calibre1">2</p>

            <p class="calibre1">1</p>

            <p class="calibre1">2</p>

            <p class="calibre1">number of individuals exposed to</p>

            <p class="calibre1">(i)</p>

            <p class="calibre1">Chemical C but not chemical C</p>

            <p class="calibre1">(ii) Chemical C but not chemical C</p>

            <p class="calibre1">1</p>

            <p class="calibre1">2</p>

            <p class="calibre1">2</p>

            <p class="calibre1">1</p>

            <p class="calibre1">(iii) Chemical C or chemical C</p>

            <p class="calibre1">1</p>

            <p class="calibre1">2</p>

            <p class="calibre1"><b class="calibre4">Solution</b> Let U denote the universal set consisting of individuals suffering from the skin disorder, A denote the set of individuals exposed to the chemical C and B denote 1</p>

            <p class="calibre1">the set of individuals exposed to the chemical C .2</p>

            <p class="calibre1">Here</p>

            <p class="calibre1"><i class="calibre5">n</i> ( U) = 200, <i class="calibre5">n</i> ( A ) = 120, <i class="calibre5">n</i> ( B ) = 50 and <i class="calibre5">n</i> ( A ∩ B ) = 30</p>

            <p class="calibre1">(i) From the Venn diagram given in Fig 1.13, we have</p>

            <p class="calibre1">A = ( A – B ) ∪ ( A ∩ B ).</p>

            <p class="calibre1"><i class="calibre5">n</i> (A) = <i class="calibre5">n</i>( A – B ) + <i class="calibre5">n</i>( A ∩ B ) (Since A – B) and A ∩ B are disjoint.) or <i class="calibre5">n</i> ( A – B ) = <i class="calibre5">n</i> ( A ) – <i class="calibre5">n</i> ( A ∩ B ) = 120 –30 = 90</p>

            <p class="calibre1">Hence, the number of individuals exposed to</p>

            <p class="calibre1">chemical C but not to chemical C is 90.</p>

            <p class="calibre1">1</p>

            <p class="calibre1">2</p>

            <p class="calibre1">(ii) From the Fig 1.13, we have</p>

            <p class="calibre1">B = ( B – A) ∪ ( A ∩ B).</p>

            <p class="calibre1"></p>

            <p class="calibre1">and so,</p>

            <p class="calibre1"><i class="calibre5">n</i> (B) = <i class="calibre5">n</i> (B – A) + <i class="calibre5">n</i> ( A ∩ B)</p>

            <p class="calibre1">(Since B – A and A ∩B are disjoint.)</p>

            <p class="calibre1">or</p>

            <p class="calibre1"><i class="calibre5">n</i> ( B – A ) = <i class="calibre5">n</i> ( B ) – <i class="calibre5">n</i> ( A ∩ B )</p>

            <p class="calibre1">= 50 – 30 = 20</p>

            <p class="calibre1">not to be republished</p>

            <p class="calibre1"><b class="calibre4">Fig 1.13</b></p>

            <p class="calibre1">24 MATHEMATICS</p>

            <p class="calibre1">Thus, the number of individuals exposed to chemical C and not to chemical C is 20.</p>

            <p class="calibre1">2</p>

            <p class="calibre1">1</p>

            <p class="calibre1">(iii) The number of individuals exposed either to chemical C or to chemical C , i.e., 1</p>

            <p class="calibre1">2</p>

            <p class="calibre1"><i class="calibre5">n</i> ( A ∪ B ) = <i class="calibre5">n</i> ( A ) + <i class="calibre5">n</i> ( B ) – <i class="calibre5">n</i> ( A ∩ B )</p>

            <p class="calibre1">= 120 + 50 – 30 = 140.</p>

            <p class="calibre1"><b class="calibre4">EXERCISE 1.6</b></p>

            <p class="calibre1"><b class="calibre4">1.</b></p>

            <p class="calibre1">If X and Y are two sets such that <i class="calibre5">n</i> ( X ) = 17, <i class="calibre5">n</i> ( Y ) = 23 and <i class="calibre5">n</i> ( X ∪ Y ) = 38, find <i class="calibre5">n</i> ( X ∩ Y ).</p>

            <p class="calibre1"><b class="calibre4">2.</b></p>

            <p class="calibre1">If X and Y are two sets such that X ∪ Y has 18 elements, X has 8 elements and Y has 15 elements ; how many elements does X ∩ Y have?</p>

            <p class="calibre1"><b class="calibre4">3.</b></p>

            <p class="calibre1">In a group of 400 people, 250 can speak Hindi and 200 can speak English. How</p>

            <p class="calibre1">many people can speak both Hindi and English?</p>

            <p class="calibre1"><b class="calibre4">4.</b></p>

            <p class="calibre1">If S and T are two sets such that S has 21 elements, T has 32 elements, and S ∩ T</p>

            <p class="calibre1">has 11 elements, how many elements does S ∪ T have?</p>

            <p class="calibre1"><b class="calibre4">5.</b></p>

            <p class="calibre1">If X and Y are two sets such that X has 40 elements, X ∪ Y has 60 elements and X ∩ Y has 10 elements, how many elements does Y have?</p>

            <p class="calibre1"><b class="calibre4">6.</b></p>

            <p class="calibre1">In a group of 70 people, 37 like coffee, 52 like tea and each person likes at least one of the two drinks. How many people like both coffee and tea?</p>

            <p class="calibre1"><b class="calibre4">7.</b></p>

            <p class="calibre1">In a group of 65 people, 40 like cricket, 10 like both cricket and tennis. How many like tennis only and not cricket? How many like tennis?</p>

            <p class="calibre1"><b class="calibre4">8.</b></p>

            <p class="calibre1">In a committee, 50 people speak French, 20 speak Spanish and 10 speak both</p>

            <p class="calibre1">Spanish and French. How many speak at least one of these two languages?</p>

            <p class="calibre1"><i class="calibre5"><b class="calibre4">Miscellaneous Examples</b></i></p>

            <p class="highlight">Example 28</p>Show that the set of letters needed to spell “ CATARACT ” and the set of letters needed to spell “ TRACT” are equal.

            <p class="calibre1"><b class="calibre4">Solution</b> Let X be the set of letters in “CATARACT”. Then</p>

            <p class="calibre1">X = { C, A, T, R }</p>

            <p class="calibre1">Let Y be the set of letters in “ TRACT”. Then</p>

            <p class="calibre1">Y = { T, R, A, C, T } = { T, R, A, C }</p>

            <p class="calibre1">Since every element in X is in Y and every element in Y is in X. It follows that X = Y.</p>

            <p class="highlight">Example 29</p>List all the subsets of the set { –1, 0, 1 }.

            <p class="calibre1"></p>

            <p class="calibre1"><b class="calibre4">Solution</b> Let A = { –1, 0, 1 }. The subset of A having no element is the empty set φ. The subsets of A having one element are { –1 }, { 0 }, { 1 }. The subsets of A having two elements are {–1, 0}, {–1, 1} ,{0, 1}. The subset of A having three elements of A is A itself. So, all the subsets of A are φ, {–1}, {0}, {1}, {–1, 0}, {–1, 1}, not to be republished</p>

            <p class="calibre1">{0, 1} and {–1, 0, 1}.</p>

            <p class="calibre1">SETS 25</p>

            <p class="highlight">Example 30</p>Show that A ∪ B = A ∩ B implies A = B

            <p class="calibre1"><b class="calibre4">Solution</b> Let <i class="calibre5">a</i> ∈ A. Then <i class="calibre5">a</i> ∈ A ∪ B. Since A ∪ B = A ∩ B , <i class="calibre5">a</i> ∈ A ∩ B. So <i class="calibre5">a</i> ∈ B.</p>

            <p class="calibre1">Therefore, A ⊂ B. Similarly, if <i class="calibre5">b</i> ∈ B, then <i class="calibre5">b</i> ∈ A ∪ B. Since A ∪ B = A ∩ B, <i class="calibre5">b</i> ∈ A ∩ B. So, <i class="calibre5">b</i> ∈ A. Therefore, B ⊂ A. Thus, A = B</p>

            <p class="highlight">Example 31</p>For any sets A and B, show that

            <p class="calibre1">P ( A ∩ B ) = P ( A ) ∩ P ( B ).</p>

            <p class="calibre1"><b class="calibre4">Solution</b> Let X ∈ P ( A ∩ B ). Then X ⊂ A ∩ B. So, X ⊂ A and X ⊂ B. Therefore, X ∈ P ( A ) and X ∈ P ( B ) which implies X ∈ P ( A ) ∩ P ( B). This gives P ( A ∩ B )</p>

            <p class="calibre1">⊂ P ( A ) ∩ P ( B ). Let Y ∈ P ( A ) ∩ P ( B ). Then Y ∈ P ( A) and Y ∈ P ( B ). So, Y ⊂ A and Y ⊂ B. Therefore, Y ⊂ A ∩ B, which implies Y ∈ P ( A ∩ B ). This gives P ( A ) ∩ P ( B ) ⊂ P ( A ∩ B)</p>

            <p class="calibre1">Hence P ( A ∩ B ) = P ( A ) ∩ P ( B ).</p>

            <p class="highlight">Example 32</p>A market research group conducted a survey of 1000 consumers and reported that 720 consumers like product A and 450 consumers like product B, what is the least number that must have liked both products?

            <p class="calibre1"><b class="calibre4">Solution</b> Let U be the set of consumers questioned, S be the set of consumers who liked the product A and T be the set of consumers who like the product B. Given that <i class="calibre5">n</i> ( U ) = 1000, <i class="calibre5">n</i> ( S ) = 720, <i class="calibre5">n</i> ( T ) = 450</p>

            <p class="calibre1">So</p>

            <p class="calibre1"><i class="calibre5">n</i> ( S ∪ T ) = <i class="calibre5">n</i> ( S ) + <i class="calibre5">n</i> ( T ) – <i class="calibre5">n</i> ( S ∩ T )</p>

            <p class="calibre1">= 720 + 450 – <i class="calibre5">n</i> (S ∩ T) = 1170 – <i class="calibre5">n</i> ( S ∩ T )</p>

            <p class="calibre1">Therefore, <i class="calibre5">n</i> ( S ∪ T ) is maximum when <i class="calibre5">n</i> ( S ∩ T ) is least. But S ∪ T ⊂ U implies <i class="calibre5">n</i> ( S ∪ T ) ≤ <i class="calibre5">n</i> ( U ) = 1000. So, maximum values of <i class="calibre5">n</i> ( S ∪ T ) is 1000. Thus, the least value of <i class="calibre5">n</i> ( S ∩ T ) is 170. Hence, the least number of consumers who liked both products is 170.</p>

            <p>&lt; span class="highlight"&gt;Example 33 Out of 500 car owners investigated, 400 owned car A and 200 owned car B, 50 owned both A and B cars. Is this data correct?</p>

            <p class="calibre1"><b class="calibre4">Solution</b> Let U be the set of car owners investigated, M be the set of persons who owned car A and S be the set of persons who owned car B.</p>

            <p class="calibre1">Given that</p>

            <p class="calibre1"><i class="calibre5">n</i> ( U ) = 500, <i class="calibre5">n</i> (M ) = 400, <i class="calibre5">n</i> ( S ) = 200 and <i class="calibre5">n</i> ( S ∩ M ) = 50.</p>

            <p class="calibre1">Then</p>

            <p class="calibre1"><i class="calibre5">n</i> ( S ∪ M ) = <i class="calibre5">n</i> ( S ) + <i class="calibre5">n</i> ( M ) – <i class="calibre5">n</i> ( S ∩ M ) = 200 + 400 – 50 = 550</p>

            <p class="calibre1">But S ∪ M ⊂ U implies <i class="calibre5">n</i> ( S ∪ M ) ≤ <i class="calibre5">n</i> ( U ).</p>

            <p class="calibre1"></p>

            <p class="calibre1">This is a contradiction. So, the given data is incorrect.</p>

            <p>&lt; span class="highlight"&gt;Example 34 A college awarded 38 medals in football, 15 in basketball and 20 in cricket. If these medals went to a total of 58 men and only three men got medals in all not to be republished</p>

            <p class="calibre1">the three sports, how many received medals in exactly two of the three sports ?</p>

            <p class="calibre1">26 MATHEMATICS</p>

            <p class="calibre1"><b class="calibre4">Solution</b> Let F, B and C denote the set of men who</p>

            <p class="calibre1">received medals in football, basketball and cricket,</p>

            <p class="calibre1">respectively.</p>

            <p class="calibre1">Then <i class="calibre5">n</i> ( F ) = 38, <i class="calibre5">n</i> ( B ) = 15, <i class="calibre5">n</i> ( C ) = 20</p>

            <p class="calibre1"><i class="calibre5">n</i> (F ∪ B ∪ C ) = 58 and <i class="calibre5">n</i> (F ∩ B ∩ C ) = 3</p>

            <p class="calibre1">Therefore,</p>

            <p class="calibre1"><i class="calibre5">n</i> (F ∪ B ∪ C ) = <i class="calibre5">n</i> ( F ) + <i class="calibre5">n</i> ( B )</p>

            <p class="calibre1"><i class="calibre5">+ n</i> ( C ) <i class="calibre5">– n</i> (F ∩ B ) <i class="calibre5">– n</i> (F ∩ C ) – <i class="calibre5">n</i> (B ∩ C ) +</p>

            <p class="calibre1"><i class="calibre5">n</i> ( F ∩ B ∩ C ),</p>

            <p class="calibre1"><b class="calibre4">Fig 1.14</b></p>

            <p class="calibre1">gives <i class="calibre5">n</i> ( F ∩ B ) + <i class="calibre5">n</i> ( F ∩ C ) + <i class="calibre5">n</i> ( B ∩ C ) = 18</p>

            <p class="calibre1">Consider the Venn diagram as given in Fig 1.14</p>

            <p class="calibre1">Here, <i class="calibre5">a</i> denotes the number of men who got medals in football and basketball only, <i class="calibre5">b</i> denotes the number of men who got medals in football and cricket only, <i class="calibre5">c</i> denotes the number of men who got medals in basket ball and cricket only and <i class="calibre5">d</i> denotes the number of men who got medal in all the three. Thus, <i class="calibre5">d</i> = <i class="calibre5">n</i> ( F ∩ B ∩ C ) = 3 and <i class="calibre5">a</i> +</p>

            <p class="calibre1"><i class="calibre5">d</i> + <i class="calibre5">b</i> + <i class="calibre5">d</i> + <i class="calibre5">c</i> + <i class="calibre5">d</i> = 18</p>

            <p class="calibre1">Therefore</p>

            <p class="calibre1"><i class="calibre5">a</i> + <i class="calibre5">b</i> + <i class="calibre5">c</i> = 9,</p>

            <p class="calibre1">which is the number of people who got medals in exactly two of the three sports.</p>

            <p class="calibre1"><i class="calibre5"><b class="calibre4">Miscellaneous Exercise on Chapter 1</b></i></p>

            <p class="calibre1"><b class="calibre4">1.</b></p>

            <p class="calibre1">Decide, among the following sets, which sets are subsets of one and another: A = { <i class="calibre5">x</i> : <i class="calibre5">x</i> ∈ <b class="calibre4">R</b> and <i class="calibre5">x</i> satisfy <i class="calibre5">x</i> 2 – 8 <i class="calibre5">x</i> + 12 = 0 }, B = { 2, 4, 6 },</p>

            <p class="calibre1">C = { 2, 4, 6, 8, . . . }, D = { 6 }.</p>

            <p class="calibre1"><b class="calibre4">2.</b></p>

            <p class="calibre1">In each of the following, determine whether the statement is true or false. If it is true, prove it. If it is false, give an example.</p>

            <p class="calibre1">(i) If <i class="calibre5">x</i> ∈ A and A ∈ B , then <i class="calibre5">x</i> ∈ B</p>

            <p class="calibre1">(ii) If A ⊂ B and B ∈ C , then A ∈ C</p>

            <p class="calibre1">(iii) If A ⊂ B and B ⊂ C , then A ⊂ C</p>

            <p class="calibre1">(iv) If A ⊄ B and B ⊄ C , then A ⊄ C</p>

            <p class="calibre1">(v) If <i class="calibre5">x</i> ∈ A and A ⊄ B , then <i class="calibre5">x</i> ∈ B</p>

            <p class="calibre1">(vi) If A ⊂ B and <i class="calibre5">x</i> ∉ B , then <i class="calibre5">x</i> ∉ A</p>

            <p class="calibre1"><b class="calibre4">3.</b></p>

            <p class="calibre1">Let A, B, and C be the sets such that A ∪ B = A ∪ C and A ∩ B = A ∩ C. Show</p>

            <p class="calibre1">that B = C.</p>

            <p class="calibre1"><b class="calibre4">4.</b></p>

            <p class="calibre1">Show that the following four conditions are equivalent :</p>

            <p class="calibre1">(i) A ⊂ B(ii) A – B = φ (iii) A ∪ B = B (iv) A ∩ B = A</p>

            <p class="calibre1"></p>

            <p class="calibre1"><b class="calibre4">5.</b></p>

            <p class="calibre1">Show that if A ⊂ B, then C – B ⊂ C – A.</p>

            <p class="calibre1"><b class="calibre4">6.</b></p>

            <p class="calibre1">Assume that P ( A ) = P ( B ). Show that A = B</p>

            <p class="calibre1"><b class="calibre4">7.</b></p>

            <p class="calibre1">Is it true that for any sets A and B, P ( A ) ∪ P ( B ) = P ( A ∪ B )? Justify your not to be republished</p>

            <p class="calibre1">answer.</p>

            <p class="calibre1">SETS 27</p>

            <p class="calibre1"><b class="calibre4">8.</b></p>

            <p class="calibre1">Show that for any sets A and B,</p>

            <p class="calibre1">A = ( A ∩ B ) ∪ ( A – B ) and A ∪ ( B – A ) = ( A ∪ B )</p>

            <p class="calibre1"><b class="calibre4">9.</b></p>

            <p class="calibre1">Using properties of sets, show that</p>

            <p class="calibre1">(i) A ∪ ( A ∩ B ) = A (ii) A ∩ ( A ∪ B ) = A.</p>

            <p class="calibre1"><b class="calibre4">10.</b></p>

            <p class="calibre1">Show that A ∩ B = A ∩ C need not imply B = C.</p>

            <p class="calibre1"><b class="calibre4">11.</b></p>

            <p class="calibre1">Let A and B be sets. If A ∩ X = B ∩ X = φ and A ∪ X = B ∪ X for some set</p>

            <p class="calibre1">X, show that A = B.</p>

            <p class="calibre1">(<b class="calibre4">Hints</b> A = A ∩ ( A ∪ X ) , B = B ∩ ( B ∪ X ) and use Distributive law ) <b class="calibre4">12.</b></p>

            <p class="calibre1">Find sets A, B and C such that A ∩ B, B ∩ C and A ∩ C are non-empty</p>

            <p class="calibre1">sets and A ∩ B ∩ C = φ.</p>

            <p class="calibre1"><b class="calibre4">13.</b></p>

            <p class="calibre1">In a survey of 600 students in a school, 150 students were found to be taking tea and 225 taking coffee, 100 were taking both tea and coffee. Find how many</p>

            <p class="calibre1">students were taking neither tea nor coffee?</p>

            <p class="calibre1"><b class="calibre4">14.</b></p>

            <p class="calibre1">In a group of students, 100 students know Hindi, 50 know English and 25 know</p>

            <p class="calibre1">both. Each of the students knows either Hindi or English. How many students</p>

            <p class="calibre1">are there in the group?</p>

            <p class="calibre1"><b class="calibre4">15.</b></p>

            <p class="calibre1">In a survey of 60 people, it was found that 25 people read newspaper H, 26 read newspaper T, 26 read newspaper I, 9 read both H and I, 11 read both H and T,</p>

            <p class="calibre1">8 read both T and I, 3 read all three newspapers. Find:</p>

            <p class="calibre1">(i) the number of people who read at least one of the newspapers.</p>

            <p class="calibre1">(ii) the number of people who read exactly one newspaper.</p>

            <p class="calibre1"><b class="calibre4">16.</b></p>

            <p class="calibre1">In a survey it was found that 21 people liked product A, 26 liked product B and 29 liked product C. If 14 people liked products A and B, 12 people liked products C and A, 14 people liked products B and C and 8 liked all the three products.</p>

            <p class="calibre1">Find how many liked product C only.</p>

            <p class="calibre1"><i class="calibre5"><b class="calibre4">Summary</b></i></p>

            <p class="calibre1">This chapter deals with some basic definitions and operations involving sets. These are summarised below:</p>

            <p class="calibre1">A set is a well-defined collection of objects.</p>

            <p class="calibre1">A set which does not contain any element is called <i class="calibre5">empty set</i>.</p>

            <p class="calibre1">A set which consists of a definite number of elements is called <i class="calibre5">finite set</i>, otherwise, the set is called <i class="calibre5">infinite set</i>.</p>

            <p class="calibre1"></p>

            <p class="calibre1"></p>

            <p class="calibre1">Two sets A and B are said to be equal if they have exactly the same elements.</p>

            <p class="calibre1">A set A is said to be subset of a set B, if every element of A is also an element of B. Intervals are subsets of <b class="calibre4">R</b>.</p>

            <p class="calibre1">A power set of a set A is collection of all subsets of A. It is denoted by P(A).</p>

            <p class="calibre1">not to be republished</p>

            <p class="calibre1">28 MATHEMATICS</p>

            <p class="calibre1">The union of two sets A and B is the set of all those elements which are either in A or in B.</p>

            <p class="calibre1">The intersection of two sets A and B is the set of all elements which are</p>

            <p class="calibre1">common. The difference of two sets A and B in this order is the set of elements which belong to A but not to B.</p>

            <p class="calibre1">The complement of a subset A of universal set U is the set of all elements of U</p>

            <p class="calibre1">which are not the elements of A.</p>

            <p class="calibre1">For any two sets A and B, (A ∪ B)′ = A′ ∩ B′ and ( A ∩ B )′ = A′ ∪ B′</p>

            <p class="calibre1">If A and B are finite sets such that A ∩ B = φ, then</p>

            <p class="calibre1"><i class="calibre5">n</i> (A ∪ B) = <i class="calibre5">n</i> (A) + <i class="calibre5">n</i> (B).</p>

            <p class="calibre1">If A ∩ B ≠ φ, then</p>

            <p class="calibre1"><i class="calibre5">n</i> (A ∪ B) = <i class="calibre5">n</i> (A) + <i class="calibre5">n</i> (B) – <i class="calibre5">n</i> (A ∩ B)</p>

            <p class="calibre1"><i class="calibre5"><b class="calibre4">Historical Note</b></i></p>

            <p class="calibre1">The modern theory of sets is considered to have been originated largely by the German mathematician Georg Cantor (1845-1918). His papers on set theory</p>

            <p class="calibre1">appeared sometimes during 1874 to 1897. His study of set theory came when he was studying trigonometric series of the form <i class="calibre5">a</i> sin <i class="calibre5">x</i> + <i class="calibre5">a</i> sin 2 <i class="calibre5">x</i> + <i class="calibre5">a</i> sin 3 <i class="calibre5">x</i> + ...</p>

            <p class="calibre1">1</p>

            <p class="calibre1">2</p>

            <p class="calibre1">3</p>

            <p class="calibre1">He published in a paper in 1874 that the set of real numbers could not be put into one-to-one correspondence wih the integers. From 1879 onwards, he publishd</p>

            <p class="calibre1">several papers showing various properties of abstract sets.</p>

            <p class="calibre1">Cantor’s work was well received by another famous mathematician Richard</p>

            <p class="calibre1">Dedekind (1831-1916). But Kronecker (1810-1893) castigated him for regarding</p>

            <p class="calibre1">infinite set the same way as finite sets. Another German mathematician Gottlob Frege, at the turn of the century, presented the set theory as principles of logic.</p>

            <p class="calibre1">Till then the entire set theory was based on the assumption of the existence of the set of all sets. It was the famous Englih Philosopher Bertand Russell (1872-1970 ) who showed in 1902 that the assumption of existence of a set of all sets leads to a contradiction. This led to the famous Russell’s Paradox. Paul R.Halmos writes about it in his book ‘Naïve Set Theory’ that “nothing contains everything”.</p>

            <p class="calibre1"></p>

            <p class="calibre1">The Russell’s Paradox was not the only one which arose in set theory.</p>

            <p class="calibre1">Many paradoxes were produced later by several mathematicians and logicians.</p>

            <p class="calibre1">not to be republished</p>

            <p class="calibre1">SETS 29</p>

            <p class="calibre1">As a consequence of all these paradoxes, the first axiomatisation of set theory was published in 1908 by Ernst Zermelo. Another one was proposed by Abraham</p>

            <p class="calibre1">Fraenkel in 1922. John Von Neumann in 1925 introduced explicitly the axiom of regularity. Later in 1937 Paul Bernays gave a set of more satisfactory</p>

            <p class="calibre1">axiomatisation. A modification of these axioms was done by Kurt Gödel in his</p>

            <p class="calibre1">monograph in 1940. This was known as Von Neumann-Bernays (VNB) or Gödel-</p>

            <p class="calibre1">Bernays (GB) set theory.</p>

            <p class="calibre1">Despite all these difficulties, Cantor’s set theory is used in present day</p>

            <p class="calibre1">mathematics. In fact, these days most of the concepts and results in mathematics are expressed in the set theoretic language.</p>

            <p class="calibre1"></p>

            <p class="calibre1"><b class="calibre4">—</b> <b class="calibre4">—</b></p>

            <p class="calibre1"></p>

            <p class="calibre1">not to be republished</p>

            <p class="calibre1"></p>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
</html>
