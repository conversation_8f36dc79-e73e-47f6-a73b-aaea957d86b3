body {
font-family:"Arial";
font-size:100%;
line-height:110%;
padding:15px;
text-align:justify;
}

* {
margin:0;
padding:0;
}

.image {
text-align:center;
}

.author {
text-align:right;
}
.chapterHeading {
font-size:160%;
margin-bottom:20px;
text-align:center;
text-transform:uppercase;

}
h3
{
color:#AA0078;
font-size:20px;
padding:15px;
}
.Heading {
font-size:160%;
margin-bottom:20px;
text-align:center;
}

.chapterNumber {
	font-size: 125%;
	
	text-align:center;
}

.subHeading {
font-size:125%;
margin-bottom:20px;
text-align:center;
text-transform:uppercase;
}

.topicHeading {
font-size:115%;
margin-bottom:20px;
text-transform:uppercase;
}

.center {
	text-align: center;
	
}

.excercise {
text-transform:uppercase;
font-weight:bold;
margin:1% 0%;
}

.box{
background-color:rgba(3, 78, 162, 0.4);
padding: 15px;
font-size: 0.9em;
}

.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 15px;
font-size: 0.9em;
}

.newWordsBox{
background-color:rgba(252, 187, 118, 0.4);
padding: 15px;
font-size: 0.9em;
}
div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;
margin-left:6.6%;
width: 85%;

position:absolute;

top:50%;
line-height:110%;
font-weight:bold;

font-size:180%;

color:#fff;

}

div.chapter_pos div

{

background:#9D538E;

padding:15px;

width:34%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#eaeaea;

font-weight:normal;

}
.activitybox2{
background-color:#F4A460;
padding: 15px;
font-size: 0.9em;
}
.lining_box
{
border:2px solid #AA0078;
padding:15px;
border-radius:15px;
}
img
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:40%;
}
.img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:100%;
}
ul

{

margin-left:45px;

}

.caption

{

font-style: italic;

font-size: 0.83em;

color: #4D4D4D;

text-align:center;

}

p

{

margin-top:10px;

}

h2
{
color:#fff;
font-size:1.5em;
background:#AA0078;
padding:15px;
}


h4
{
	color:#d1640f;
	font-size: 1.1em;
}

.footer

{

display:none;

}

table td

{

padding:15px;

}
.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 15px;
font-size: 0.9em;
}
.box{
background-color:rgba(3, 78, 162, 0.4);
padding: 15px;
font-size: 0.9em;
}

.cover_img_small

{

width:50%;

}

@media only screen and (max-width: 767px) {


div.chapter_pos


{

top:30%;

font-size:1em;

}

div.chapter_pos div


{

width:70%;

}

.cover_img_small

{

width:90%;

}

}


 .resize3 img

{

height:75px;

position:relative;

top:25px;



div.layout

{

text-align: center;

}

div.chapter_pos


{


text-align: center;


width: 96%;


position:absolute;


top:70%;


font-weight:bold;


font-size:28px;


color:#fff;


}


div.chapter_pos div


{


background:#993300;


padding:10px;


width:40%;


margin:auto;

opacity:0.9;


}


div.chapter_pos div span


{


font-size:0.7em;


color:#eaeaea;


font-weight:normal;


}

@media only screen and (max-width: 767px) {


div.chapter_pos


{


font-size:0.8em;

line-height:120%;

top:50%;

}


div.chapter_pos div span


{


font-size:0.7em;


}

}
