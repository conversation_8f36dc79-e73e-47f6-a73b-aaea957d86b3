@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:bold;
	src : url("../Fonts/wcb.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:bold;
	src : url("../Fonts/wcbi.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:normal;
	font-weight:normal;
	src : url("../Fonts/wcn.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:italic;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}
@font-face {
	font-family:"Walkman-Chanakya-905";
	font-style:oblique;
	font-weight:normal;
	src : url("../Fonts/wcni.ttf");
}

html, body {
	font-family:"Walkman-Chanakya-905";
}

body {
font-size:120%;
line-height:150%;
padding:2%;
text-align:justify;
}

* {
margin:0;
padding:0;
}

.image {
text-align:center;
}
.author {
text-align:right;
}

.chapterHeading {
font-size:160%;
color: gray;
margin-bottom:20px;
}

.chapterNumber {
	font-size: 125%;
}

.subHeading {
color:#ce1337;
font-size:125%;
}

.center {
	text-align: center;
}

.excercise {
text-transform:uppercase;
font-weight:bold;
margin:1% 0%;
}

.box{
background-color:rgba(3, 78, 162, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}

.activityBox{
background-color:rgba(206, 19, 55, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}

.newWordsBox{
background-color:rgba(252, 187, 118, 0.4);
padding: 5px 5px 5px 5px;
margin: 5px 5px 5px 5px;
}
.img
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:50%;
}
.img_wid
{
	margin-left: auto;
    margin-right: auto;
	display: block;
	width:auto;
}
ul
{
	margin-left:45px;
}
.caption
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
	text-align:center;
}
p
{
	margin-top:10px;
}
h2
{
	color:#006699;
}
h4
{
	color:#d1640f;
}
.footer
{
	display:none;
}
table td
{
	padding:10px;
}
p.para-style-override-1 {
	color:#00aeef;
	font-size:2.5em;
	font-weight:bold;
	text-align:right;
}
span.char-style-override-1 {
	color:#000000;
	font-family:arial;
	font-weight:bold;
}
span.char-style-override-3 {
	font-size:1.25em;
	font-weight:bold;
}
span.char-style-override-6 {
	font-family:"Walkman-Chanakya-905", serif;
	font-size:1.333em;
	font-weight:bold;
}
span.char-style-override-12 {
	color:#000000;
	font-family:"Walkman-Chanakya-905", serif;
	font-size:1.273em;
	font-style:normal;
	font-weight:bold;
}
span.char-style-override-13 {
	color:#01a0c6;
	font-size:1.273em;
	font-weight:bold;
}
span.char-style-override-13 {
	color:#01a0c6;
	font-family:"Walkman-Chanakya-905", serif;
	font-size:1.273em;
	font-style:normal;
	font-weight:bold;
}
span.char-style-override-22 {
	font-family:"Walkman-Chanakya-905", serif;
	font-size:1.25em;
	font-weight:bold;
}
span.char-style-override-12 {
	color:#000000;
	font-family:"Walkman-Chanakya-905", serif;
	font-size:1.273em;
	font-weight:bold;
}
.conc
{
	color:#006699;
}
.englishMeaning, .char-style-override-11, .char-style-override-28, .char-style-override-2, .char-style-override-14, .Body-text para-style-override-25, .char-style-override-37, .char-style-override-15, .char-style-override-40, .char-style-override-54, .char-style-override-19, .char-style-override-19, .char-style-override-62, .char-style-override-38
{
	font-family: arial;
}
.right
{
	display:inline;
	float:right;
	clear:both;
}
.bold
{
	font-size:115%;
	font-family: Walkman-Chanakya-905;
		font-weight:bold;
}
.italic
{
	font-weight:bold;
	font-size:100%;
	color:#03C;
}
.center
{
	text-align:center;
}
.right
{
	text-align:right;
}
.background
{
	background:#999;
	font-weight:bold;
	
}
.superscript{
position:relative;
top:-15%;
font-size: 85%;
font-family:Arial, Helvetica, sans-serif;
}

.subscript{
position:relative;
bottom:-25%;
font-size: 85%;
font-family:Arial, Helvetica, sans-serif;
}
.work
{
	font-size:105% ;
}
div.layout
{
  text-align: center;
}
div.chapter_pos

{

text-align: center;

width: 96%;

position:absolute;

top:40%;

font-weight:bold;

font-size:28px;

color:#fff;

}

div.chapter_pos div

{

background:#CF5300;

padding:10px;

width:40%;

margin:auto;
opacity:0.9;

}

div.chapter_pos div span

{

font-size:0.7em;

color:#FFCC00;

font-weight:normal;

}

.note
{
	font-style: italic; 
	font-size: 0.83em; 
	color: #4D4D4D;
}
@media only screen and (max-width: 767px) {

div.chapter_pos

{

font-size:0.8em;
line-height:120%;
}

div.chapter_pos div span

{

font-size:0.5em;

}