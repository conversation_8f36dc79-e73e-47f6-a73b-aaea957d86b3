package com.wonderslate.usermanagement
import com.wonderslate.CreationService
import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.ChaptersMst
import com.wonderslate.data.MetainfoService
import com.wonderslate.data.ObjectiveMst
import com.wonderslate.data.ResourceDtl
import com.wonderslate.data.SiteMst
import com.wonderslate.data.UserGradeDtl
import com.wonderslate.data.UtilService

import com.wonderslate.groups.GroupsService
import com.wonderslate.log.TeacherNomineeDtl
import com.wonderslate.logs.LogsService
import com.wonderslate.publish.BooksTagDtl
import com.wonderslate.publish.Publishers
import com.wonderslate.publish.SyllabusGradeDtl
import grails.converters.JSON
import grails.plugin.springsecurity.annotation.Secured
import grails.transaction.Transactional
import groovy.sql.Sql
import org.hibernate.Transaction
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.multipart.MultipartHttpServletRequest

import javax.imageio.ImageIO
import javax.sql.DataSource
import java.awt.image.BufferedImage
import java.text.SimpleDateFormat

class UsermanagementController {
    def springSecurityService
    def redisService
    DataProviderService dataProviderService
    MetainfoService metainfoService
    def rememberMeServices
    def UserPointsCacheService userPointsCacheService
    UserPointsService userPointsService
    UserManagementService userManagementService
    UtilService utilService

    CreationService creationService
    GroupsService groupsService
    LogsService logsService

    @Secured(['ROLE_USER']) @Transactional
    def index() {
        String uerName = params.userName, currentUserName = springSecurityService.currentUser.username
        User user = User.findByUsername(uerName)
        HashMap userPointsModuleWise = userPointsService.getUserPointsModuleWise(user.username)
        List<HashMap> userPointsModuleWiseList = userPointsModuleWise.get("moduleWiseScore")
        String rankTypeStr = params.rankType,scoreTypeStr = params.scoreType
        int scoreType = Integer.parseInt(scoreTypeStr), user7daysPoints = 0, lifeTimePoints = 0
//        Map topersRankMap = new HashMap()
//        topersRankMap = userPointsService.getTopRankers(scoreType,rankTypeStr)
//        List<HashMap> topersRankList = new ArrayList<>()
//        for(Map.Entry item:topersRankMap){
//            topersRankList.add(item.value)
//        }
        HashMap<String,HashMap<String, Integer>> pointsHashMap = userPointsCacheService.get7DaysUserPoints(scoreType)
        HashMap<String,Integer> lifeTimeUserPoints = userPointsCacheService.getLifeTimeUserPoints(scoreType)
        HashMap<String,Integer> userPointsMap = pointsHashMap.get(user.username)
        if(userPointsMap == null) userPointsMap = new HashMap<>()
        for(Map.Entry<String,Integer> map:userPointsMap){
            user7daysPoints = user7daysPoints + map.value
        }
        if(lifeTimeUserPoints.get(user.username) != null) lifeTimePoints = lifeTimeUserPoints.get(user.username)
        String appType = session['appType'];
        if(appType.equals("android") || appType.equals("ios")){
            def json = ['user':user,'userPointsModuleWiseList':userPointsModuleWiseList,'scoreType':scoreType]
            render json as JSON
        }else ['user':user,'userPointsModuleWiseList':userPointsModuleWiseList,'scoreType':scoreType,'lifeTimePoints':lifeTimePoints,'user7daysPoints':user7daysPoints,
        'showEdit':currentUserName.equals(uerName) ? true:null]

    }

    @Secured(['ROLE_USER']) @Transactional
    def updateUserPreference(){
        String mode = params.mode
        UserGradeDtl userGradeDtl
        def result = "Nothing happened"
        if("add".equals(mode)){
            def gradeId = null, syllabusId=null,grade=null,syllabus=null
            if(params.gradeId!=null) gradeId = new Long(params.gradeId)
            if(params.syllabusId!=null) syllabusId = new Long(params.syllabusId)
            if(params.grade!=null) grade = params.grade
            if(params.syllabus!=null) syllabus = params.syllabus
            //first remove the currentlySelected
            userGradeDtl = UserGradeDtl.findByUsernameAndCurrentlySelected(springSecurityService.currentUser.username,"true")
            if(userGradeDtl!=null){
                userGradeDtl.currentlySelected=null
                userGradeDtl.save(failOnError: true, flush: true)
            }

            //first check to see if this preference is present
            userGradeDtl = UserGradeDtl.findByGradeIdAndGradeAndSyllabusIdAndUsernameAndSyllabus(gradeId,grade,syllabusId,springSecurityService.currentUser.username,syllabus)
            if(userGradeDtl==null)
            //add a new preference
                userGradeDtl = new UserGradeDtl(username: springSecurityService.currentUser.username,grade: params.grade,gradeId:params.gradeId,
                        syllabusId:params.syllabusId,syllabus: params.syllabus,
                        currentlySelected: "true")
            else userGradeDtl.currentlySelected="true"

            userGradeDtl.save(failOnError: true, flush: true)
            result="Preference added"

        }else if("delete".equals(mode)){
            userGradeDtl = UserGradeDtl.findById(new Long(params.id))
            if(userGradeDtl!=null){
                userGradeDtl.delete(flush: true)
                result = "Preference removed"
            }
        }
        def json = ["results":result]
        render json as JSON
    }


    @Secured(['ROLE_USER']) @Transactional
    def getUserPreferences(){
        List userPreferences = UserGradeDtl.findAllByUsername(springSecurityService.currentUser.username)
        def json = ["status":(userPreferences.size()==0?"Nothing Present":"Present"), userPreferences: userPreferences]
        render json as JSON

    }

    @Secured(['ROLE_USER']) @Transactional
    def getUserResources()
    {
        if(redisService.("seenResources_"+springSecurityService.currentUser.username+"_"+params.chapterId)==null) dataProviderService.updateUsageList(new Long(params.chapterId))
        def addedResources = metainfoService.getResourceDetails(new Long(params.chapterId),springSecurityService.currentUser.username)
        def seenResources = redisService.("seenResources_"+springSecurityService.currentUser.username+"_"+params.chapterId)

        def json =
                [
                        'addedResources': addedResources,
                        seenResouces: seenResources,
                ]

        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def addUserResourceFavourite()
    {

        FavouriteResources favouriteResources = new FavouriteResources(resId: new Integer(params.resId),username: springSecurityService.currentUser.username)
        favouriteResources.save(failOnError: true, flush: true)
        dataProviderService.getUserFavResIds()
        def json = ['status':'added']
        render json as JSON
    }
    @Secured(['ROLE_USER']) @Transactional
    def removeUserResourceFavourite()
    {
        FavouriteResources favouriteResources = FavouriteResources.findByResIdAndUsername(new Integer(params.resId),springSecurityService.currentUser.username)
        if(favouriteResources!=null) favouriteResources.delete(flush: true)
        dataProviderService.getUserFavResIds()
        def json = ['status':'removed']
        render json as JSON

    }

    @Secured(['ROLE_USER']) @Transactional
    def getUserResourceFavourites()
    {
        if(redisService.(springSecurityService.currentUser.username+"_favResIds")==null){
            dataProviderService.getUserFavResIds()
        }
        String resIds = redisService.(springSecurityService.currentUser.username+"_favResIds")
        def json
        if(resIds!=null&&!"".equals(resIds)){
            String optionalCondition = ""
            if(params.resType!=null&&!"all".equals(params.resType)) optionalCondition=" and res_type='"+params.resType+"'"
            String sql = "select id resId,res_type resType,resource_name title,res_sub_type,res_link resLink from resource_dtl where id in ("+resIds+")"+optionalCondition
            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)
            json = ['status':'present','flashCardSets':results]

        }else{
            json = ['status':'none']
        }

        render json as JSON

    }

    @Secured(['ROLE_USER']) @Transactional
    def addUserMCQFavourite() {
        Integer objId = new Integer(params.questionId)
        FavouriteMCQs favouriteMCQs = FavouriteMCQs.findByObjIdAndUsername(objId, springSecurityService.currentUser.username)
        if (favouriteMCQs == null){
            String subject = null
        Integer chapterId = null
        ObjectiveMst objectiveMst = logsService.getObjectiveMst(objId)
        if (objectiveMst != null) {
            ResourceDtl resourceDtl = logsService.getResourceDtl(objectiveMst.quizId)
            if (resourceDtl != null) {
                ChaptersMst chaptersMst = dataProviderService.getChaptersMst(resourceDtl.chapterId)
                if (chaptersMst != null) {
                    chapterId = chaptersMst.id
                    BooksTagDtl booksTagDtl = dataProviderService.getBooksTagDtl(chaptersMst.bookId)
                    if (booksTagDtl != null) {
                        subject = booksTagDtl.subject

                    }
                }
            }
        }
         favouriteMCQs = new FavouriteMCQs(objId: objId, username: springSecurityService.currentUser.username, chapterId: chapterId, subject: subject)
        favouriteMCQs.save(failOnError: true, flush: true)
    }

        def json = ['status':'added']
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def removeUserMCQFavourite()
    {
        Integer objId = new Integer(params.questionId)
        FavouriteMCQs favouriteMCQs = FavouriteMCQs.findByObjIdAndUsername(objId,springSecurityService.currentUser.username)
        if(favouriteMCQs!=null) favouriteMCQs.delete(flush: true)
        def json = ['status':'removed']
        render json as JSON

    }

    @Secured(['ROLE_USER']) @Transactional
    def getUserMCQFavourites()
    {
        if(redisService.(springSecurityService.currentUser.username+"_favResIds")==null){
            dataProviderService.getUserFavResIds()
        }
        String resIds = redisService.(springSecurityService.currentUser.username+"_favResIds")
        def json
        if(resIds!=null&&!"".equals(resIds)){
            String optionalCondition = ""
            if(params.resType!=null&&!"all".equals(params.resType)) optionalCondition=" and res_type='"+params.resType+"'"
            String sql = "select id resId,res_type resType,resource_name title,res_sub_type,res_link resLink from resource_dtl where id in ("+resIds+")"+optionalCondition
            def dataSource = grailsApplication.mainContext.getBean('dataSource')
            def sql1 = new Sql(dataSource)
            def results = sql1.rows(sql)
            json = ['status':'present','flashCardSets':results]

        }else{
            json = ['status':'none']
        }

        render json as JSON

    }
    @Secured(['ROLE_USER']) @Transactional
    def updateUserDurationLog()
    {
        Date logDate = new SimpleDateFormat("dd/MM/yyyy").parse(params.logDate);
        int logDuration = Integer.parseInt(params.logDuration)
        UserTimeLog userTimeLog = UserTimeLog.findByUsernameAndLoggedDateAnd(springSecurityService.currentUser.username,logDate)
       if(userTimeLog!=null){
           userTimeLog.duration = new Integer(userTimeLog.duration.intValue()+logDuration)
        }else{
          userTimeLog = new UserTimeLog(username:springSecurityService.currentUser.username,duration:new Integer(logDuration),loggedDate: logDate)

        }
       userTimeLog.save(failOnError: true, flush: true)
        userManagementService.getUserDefaultTimeLog(springSecurityService.currentUser.username)

        def json = ['status':'success']
        render json as JSON

    }

    @Secured(['ROLE_USER']) @Transactional
    def updateUserBookDurationLog()
    {
        Integer siteId=utilService.getSiteId(request,session)
        Date logDate = new SimpleDateFormat("dd/MM/yyyy").parse(params.logDate);
        int logDuration = Integer.parseInt(params.logDuration)
       UserBookTimeLog userBookTimeLog = UserBookTimeLog.findByUsernameAndLoggedDateAndBookIdAndSiteId(springSecurityService.currentUser.username,logDate,new Integer(params.bookId),siteId)
        if(userBookTimeLog!=null){
            userBookTimeLog.duration = new Integer(userBookTimeLog.duration.intValue()+logDuration)
        }else{
            userBookTimeLog = new UserBookTimeLog(username:springSecurityService.currentUser.username,duration:new Integer(logDuration),loggedDate: logDate,siteId:siteId,bookId:new Integer(params.bookId) )

        }
        userBookTimeLog.save(failOnError: true, flush: true)

        def json = ['status':'success']
        render json as JSON

    }

    @Transactional
    def createGuestUser(){

        String guestUsername =  ""
        boolean userExists = true
        User user
        while(userExists){
            guestUsername =  "1_cookie_"+(new Random()).nextInt(9999999)
            user = User.findByUsername(guestUsername)
            if(user==null) userExists = false
        }

        WinGenerator winGenerator = new WinGenerator()
        winGenerator.save(failOnError: true)
        user = new User(username: guestUsername, password: "password", name: params.name,  registeredFrom: "web", siteId: session["siteId"],sessionCount: new Integer(1))
        user.win = winGenerator.id
        user.save(failOnError: true, flush: true)
        Role role = Role.findByAuthority("ROLE_USER")
        UserRole.create(user, role, true)
        role = Role.findByAuthority("ROLE_CAN_ADD")
        UserRole.create(user, role, true)
        role = Role.findByAuthority("ROLE_CAN_UPLOAD")
        UserRole.create(user, role, true)
        springSecurityService.reauthenticate(user.username, user.password)
        def authentication = SecurityContextHolder.context.authentication
        rememberMeServices.loginSuccess(request, response, authentication)
        session['userdetails'] = user
        session.setAttribute("visitedDashboard","true")
        session.setAttribute("sessionCountUpdated","true")

        def json1 =['status': "OK"]
        render json1 as JSON
    }

    @Transactional @Secured(['ROLE_USER'])
    def updateSessionCount(){
        User user = User.findByUsername(springSecurityService.currentUser.username)
        if(user.sessionCount==null) user.sessionCount = new Integer(1)
        else user.sessionCount = new Integer(user.sessionCount.intValue()+1)
        user.save(failOnError: true, flush: true)
        session.setAttribute("sessionCountUpdated","true")
        session.setAttribute("sessionCount",user.sessionCount)
        def json = [sessionCount:""+user.sessionCount]
        render json as JSON
    }

    @Transactional @Secured(['ROLE_USER'])
    def editprofile(){
        User user = null
        String email = "",schoolOrCollege = "", mobile = "", country ="",district = "",state="",who=""
        Integer pincode
        if(springSecurityService.currentUser != null) user = User.findByUsername(springSecurityService.currentUser.username)
        email = user.email != null ? user.email : email
        schoolOrCollege = user.school != null ? user.school : schoolOrCollege
        mobile = user.mobile != null ? user.mobile : mobile
        country = user.country != null ? user.country : country
        district = user.district != null ? user.district : district
        state = user.state != null ? user.state : state
        pincode = user.pincode > 0 ? user.pincode : pincode
        if(user.student == "on") who = "student"
        else if(user.teacher == "on") who = "teacher"
        else if(user.parent == "on") who = "parent"
        ['email':email,'schoolOrCollege':schoolOrCollege,'mobile':mobile,'country':country,'district':district,'state':state,'pincode':pincode,'who':who,'name':user.name,'password':'******','userNameType':user.username.contains('@')?'email':'mobile', commonTemplate:"true"]
    }

    @Transactional
    def getTopRankers(){
        String rankTypeStr = params.rankType,scoreTypeStr = params.scoreType
        int scoreType = Integer.parseInt(scoreTypeStr)
        Map topersRankMap = new HashMap()
        topersRankMap = userPointsService.getTopRankers(scoreType,rankTypeStr)
        List<HashMap> topersRankList = new ArrayList<>()
        def topOne,topTwo,topThree
        User user = null
        for(Map.Entry item:topersRankMap){
            int rank = item.value.rank
            switch (rank){
                case 1: user = User.findByUsername(item.key)
                        topOne = ['rank':item.value.rank,'name':item.value.name,'score':item.value.score,'username':item.key,'userId':user.id,'userImg':user.profilepic]
                        break
                case 2: user = User.findByUsername(item.key)
                        topTwo = ['rank':item.value.rank,'name':item.value.name,'score':item.value.score,'username':item.key,'userId':user.id,'userImg':user.profilepic]
                        break
                case 3: user = User.findByUsername(item.key)
                        topThree = ['rank':item.value.rank,'name':item.value.name,'score':item.value.score,'username':item.key,'userId':user.id,'userImg':user.profilepic]
                        break
            }
            topersRankList.add(['rank':item.value.rank,'name':item.value.name,'score':item.value.score,'username':item.key])
        }
        if(topersRankList != null){
            topersRankList.sort { a, b ->
                b.rank <=> a.rank
            }
        }
        def json = ['topers':topersRankList,'topThree':topThree,'topTwo':topTwo,'topOne':topOne]
        render json as JSON;
    }

    @Transactional
    @Secured(['ROLE_USER'])
    def addPoints(){
        def json =  ['userPoints':userPointsService.addPoints(params.reason,Integer.parseInt(params.points),Integer.parseInt(params.scoreTypeId),params.userName)]
        render json as JSON
    }

    @Transactional
    @Secured(['ROLE_USER'])
    def updateUserProfile(){
        User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
        String ouname=user.name
        Long siteId = null
        siteId = session['siteId']!=null?(Integer)session["siteId"]:new Integer(1)
        String who = params.who, password = params.pswd, pincode = params.pincode
        if(password == null || password.isEmpty()) password = '******'
        if(who.equals("student")) user.student = "on"
        else if(who.equals("parent")) user.parent = "on"
        else if(who.equals("teacher")) user.teacher = "on"
        user.password =  password == '******'? user.password: springSecurityService.encodePassword(password)
        user.name = params.name
        user.email = params.email == null || ("").equals(params.email)? user.email: params.email
        user.school = params.schoolname
        user.mobile = params.mobile == null || ("").equals(params.mobile)? user.mobile: params.mobile
        user.country = params.selectCountry
        user.district = params.districtSelect
        user.state = params.stateSelect
        user.pincode = pincode.isEmpty()?0:Integer.parseInt(pincode)
        user.save(failOnError: true, flush: true)
        discussionService.updateUserDetails(user.username,siteId)
        if(ouname!=params.name) groupsService.updateGroupUser(user.id)
        String appType = session['appType'];
        if(appType.equals("android") || appType.equals("ios")){
            def json = ['user':user]
            render json AS JSON
        }else redirect(controller: "usermanagement", action: "editprofile")
    }
    @Transactional
    @Secured(['ROLE_USER'])
    def updateNoOfTrees() {
        User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
        int noOfTree = 0;
        if(user.noOfTrees!=null) noOfTree = user.noOfTrees.intValue()
        noOfTree++;
        user.noOfTrees = new Integer(noOfTree)
        user.save(failOnError: true, flush: true)
        def json = ['status':'success']
        render json as JSON

    }

    @Transactional
    @Secured(['ROLE_USER'])
    def myTracker(){
     int noOfTrees = 0;
        User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
        if(user.noOfTrees!=null) noOfTrees = user.noOfTrees.intValue()
        ["title":"Trophy Room - Wonderslate", noOfTrees:noOfTrees, commonTemplate:"true"]
    }


    @Transactional @Secured(['ROLE_USER'])

    def getUserTimeLog(){
        String optionalQuery=""
        String sql = ""

        User user = dataProviderService.getUserMst(springSecurityService.currentUser.username)
        def json = [results: results,noOfTrees: user.noOfTrees!=null?user.noOfTrees.intValue():0]
        render json as JSON

    }

    @Transactional @Secured(['ROLE_USER'])

    def getUserBookTimeLog(){
        String optionalQuery=""
        String sql = ""
        int days = 7
        if(params.noOfDays!=null) days = Integer.parseInt(params.noOfDays)


        sql = "SELECT book_id,duration, DATE_FORMAT(DATE(convert_tz(logged_date,'+00:00','+05:30')),'%d/%m/%Y') loggedDate FROM user_book_time_log where DATE(logged_date) > date_add(CURDATE() , INTERVAL -"+days+" day)" +
                " and username='"+springSecurityService.currentUser.username+"' order by loggedDate"

        def dataSource = grailsApplication.mainContext.getBean('dataSource_wsuser')
        Sql sql1 = new Sql(dataSource)
        def results = sql1.rows(sql)
        def json = [results: results]
        render json as JSON

    }

    @Transactional @Secured(['ROLE_USER'])
    def generateOtp(){
        Long siteId = null
        boolean checkPassed = true
        siteId = session['siteId']!=null?(Integer)session["siteId"]:new Integer(1)
        SiteMst sm = dataProviderService.getSiteMst(siteId)
        def clientName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.siteName:sm.clientName
        def siteName=grailsApplication.config.grails.appServer.default=="eutkarsh"?grailsApplication.config.grails.appServer.default:sm.siteName
        def otp
        Random generator = new Random()
        AuthenticationOtp authOtp
        String email = "", mobile = "", currentUserName = springSecurityService.currentUser.username
        User user = User.findByUsername(currentUserName)
        if(user.username.contains('@')) mobile = params.contact
        else email = params.contact
        if(email!=null && !email.equals("")) {
            if(userManagementService.validateEmail(email,(Integer)session["siteId"])) {
                otp = creationService.generateOTP(""+email, sm.id)
                try {
                    userManagementService.sendOtpEmail(user.name, email, (Integer)session["siteId"], otp+"",siteName,clientName)
                } catch (Exception e){
                    checkPassed = false
                    println "Exception in sending OTP mail "+e.toString()
                }
            }
        }

        if(mobile!=null && !mobile.equals("")) {
            otp = creationService.generateOTP(""+mobile, sm.id)
            def message = "Use ${otp} as one time password (OTP) to change your Mobile Number for your Wonderslate account."
            utilService.sendSMS(siteId,message,mobile)

        }

        def json = ['email':email,'mobile':mobile,'status':checkPassed?"OK":"Failed"]
        render json as JSON
    }

    @Transactional
    @Secured(['ROLE_USER'])
    def orders(){
        [commonTemplate:"true"]
    }

    @Transactional
    def addRestoreId(){
        def restoreId=params?.restoreId
        def json
        if(UserChat.findByUsername(springSecurityService.currentUser.username)==null){
        UserChat chatuser=new UserChat(username:springSecurityService.currentUser.username,restoreId: restoreId)
            chatuser.save(failOnError: true, flush: true)
            json=["status":"ok"]
            redisService.("userchat_"+springSecurityService.currentUser.username) = restoreId
        }
        else json=["status":"failed"]
        render json as JSON
    }


    @Transactional
    def checkChatUser(){
        def json
        def present=-1
        UserChat userchat
        if(redisService.("userchat_"+springSecurityService.currentUser.username)) present=1
        else{
            userchat=UserChat.findByUsername(springSecurityService.currentUser.username)
            if(userchat!=null){
                present=1
                redisService.("userchat_"+springSecurityService.currentUser.username) = userchat.restoreId
            }
            else{
                present=-1
            }
        }
        if(present!=1) json=["status":"failed"]
        else json=["status":"ok","restoreId":redisService.("userchat_"+springSecurityService.currentUser.username)]
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def addUserGrades(){
        def json = userManagementService.addUserGrades(request)
        render json as JSON
    }

    @Secured(['ROLE_USER']) @Transactional
    def getUserGrades(){
        def json = userManagementService.getUserGrades(springSecurityService.currentUser.username)
        render json as JSON
    }


    @Secured(['ROLE_WS_CONTENT_ADMIN']) @Transactional
    def checkAccessCodeStatus(){
        Integer siteId=utilService.getSiteId(request,session)
        def json = userManagementService.checkAccessCodeStatus(params.accessCode,siteId)
        render json as JSON
    }

    @Transactional
    def  addTeacherNomineeDetails(request){
        if (request.getHeader('Content-Type') != null && request.getHeader('Content-Type').startsWith("application/json")) {
            Integer siteId=utilService.getSiteId(request,session)
            def json = userManagementService.addTeacherNomineeDetails(request,siteId)
            render json as JSON
        }
    }

    @Transactional
    def addTeacherImage(){
        Integer siteId=1
        def json = userManagementService.addTeacherImage(request,params,siteId)
        render json as JSON
    }

    @Transactional
    def getTeachersNomineeDetails(){
        Integer siteId=1
        def json = userManagementService.getTeachersNomineeDetails(siteId)
        render json as JSON
    }

    @Transactional
    def showTeachersImage(String fileName, String id){
        if(fileName!=null&&!"null".equals(fileName)&&fileName.length()>0) {
            String  picFileName = fileName.substring(0, fileName.indexOf(".")) + '.webp'
            fileName=picFileName
            response.setContentType("APPLICATION/OCTET-STREAM")
            response.setHeader("Content-Disposition", "Attachment;Filename=\"${fileName}\"")
            def file = new File("upload/Teacher/"+id+"/processed/"+ fileName)
            if (file.exists()) {
                def fileInputStream = new FileInputStream(file)
                def outputStream = response.getOutputStream()
                byte[] buffer = new byte[4096];
                int len;
                while ((len = fileInputStream.read(buffer)) > 0) {
                    outputStream.write(buffer, 0, len);
                }
                outputStream.flush()
                outputStream.close()
                fileInputStream.close()
            }   else render "";
        } else render "";
    }

    @Transactional
    def voteByNomineeId(){
        def json = userManagementService.voteByNomineeId(params)
        render json as JSON
    }

    @Transactional
    def getNomineeDetailsById(){
        def json = userManagementService.getNomineeDetailsById(params)
        render json as JSON
    }

    @Transactional
    def getNomineeVoteCount(){
        def json = userManagementService.getNomineeIdVoteCount(params)
        render json as JSON
    }

    @Transactional
    def nominations(){
        if("yes".equals(params.showData)) {
            Integer siteId = 1
            def json = userManagementService.getAllModeratedTeachersNomineeDetails(params,siteId)
            render json as JSON
        }
        ['title': "India's Favourite Teachers - Nominations"]
    }

    @Transactional
    def teachersNomineeForm(){
        ['title': "India's Favourite Teachers - Nomination",commonTemplate: "true"]
    }

    @Transactional
    def nomineeDetails(){
        ['title': "India's Favourite Teachers",commonTemplate: "true"]
    }
    @Transactional
    def teachersPollResult(){
        ['title': "India's Favourite Teachers",commonTemplate: "true"]
    }

    @Transactional @Secured(['ROLE_CLIENT_ORDER_MANAGER'])
    def addUserAndBooks(){
        [commonTemplate: "true"]
    }

    @Transactional
    def updatePublishersSecretKey()
    {
        List publishers = Publishers.findAll()

        String secretKey
        publishers.each{ publisher ->
            secretKey = "ABC"+publisher.id
            publisher.secretKey = Base64.getEncoder().encodeToString(secretKey.getBytes())
            publisher.save(failOnError: true, flush: true)

        }
    }

    @Transactional
    def getLatestReadBooks(){
        String username = springSecurityService.currentUser.username
        if( redisService.("latestReadBooks_"+username)==null) userManagementService.getLatestReadBooks(username)
        def json = [latestReadBooks:redisService.("latestReadBooks_"+username)]
        render json as JSON
    }


    @Transactional @Secured(['ROLE_USER'])
    def getUserDetailsForCart(){
        User user = User.findByUsername(springSecurityService.currentUser.username)
        def json = [username:user.username,name:user.name,mobile:user.mobile!=null?user.mobile:"",email:user.email!=null?user.email:""]
        render json as JSON
    }

    @Transactional
    def favouriteMcqs(){
        ['title': 'Favourite MCQs', commonTemplate:"true"]
    }

    @Transactional
    def submitSpecimenCopyReq(){
        def json
        if (request.getHeader('Content-Type') != null && request.getHeader('Content-Type').startsWith("application/json")) {
            def jsonObj = request.JSON
            Integer siteId=utilService.getSiteId(request,session)
            SpecimenCopyDtl specimenCopyDtl = new SpecimenCopyDtl()

            specimenCopyDtl.name = jsonObj.teachName
            specimenCopyDtl.email = jsonObj.teachEmail
            specimenCopyDtl.phoneNumber = jsonObj.teachPhone
            specimenCopyDtl.dateOfBirth = jsonObj.teachDOB
            specimenCopyDtl.institute = jsonObj.teachInst
            specimenCopyDtl.address = jsonObj.teachAddress
            specimenCopyDtl.pincode = jsonObj.teachPincode
            specimenCopyDtl.country = jsonObj.teachCountry
            specimenCopyDtl.state = jsonObj.teachState
            specimenCopyDtl.district = jsonObj.teachDist
            specimenCopyDtl.publisher = jsonObj.teachPub
            specimenCopyDtl.level = jsonObj.teachLevel
            specimenCopyDtl.syllabus=jsonObj.teachSyllabus
            specimenCopyDtl.grade=jsonObj.teachGrade
            specimenCopyDtl.subject = jsonObj.teachSubject
            specimenCopyDtl.siteId = siteId
            specimenCopyDtl.specimentSent  = false
            specimenCopyDtl.dateCreated = new Date()

            specimenCopyDtl.save(failOnError: true, flush: true)
            json=["status":"OK",id:specimenCopyDtl.id]
        }
        render json as JSON
    }

    @Transactional
    def addTeacherID(){
        def json
        try {
            final MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;
            MultipartFile file = multiRequest.getFile("file");
            SpecimenCopyDtl specimenCopyDtl = SpecimenCopyDtl.findById(new Long(params.id))
            File uploadDir = new File("upload/SpecimenCopy/" + specimenCopyDtl.id)
            if (!uploadDir.exists()) uploadDir.mkdirs()
            BufferedImage image = ImageIO.read(file.getInputStream())
            String filename = file.originalFilename
            filename = filename.replaceAll("\\s+", "")
            //creating directory to process images
            File uploadDir1 = new File(uploadDir.absolutePath + "/processed")
            if (!uploadDir1.exists()) uploadDir1.mkdirs()
            //saving original image finally to webp
            String webPImage = filename.substring(0, filename.indexOf("."))
            webPImage = "teacherimage_"+specimenCopyDtl.id
            ImageIO.write(image, "webp", new File("upload/SpecimenCopy/" + specimenCopyDtl.id + "/processed/" + webPImage + ".webp"));
            specimenCopyDtl.teacherImage = "teacherimage_"+specimenCopyDtl.id+".webp"
            specimenCopyDtl.save(failOnError: true, flush: true)
            json = ["status": specimenCopyDtl.id ? "success" : "failed",id:specimenCopyDtl.id]
        } catch(Exception e){
            json=["status":"image upload failed"]
        }
        render json as JSON
    }

    def specimenCopyRequests(){
        ['title': 'specimen Copy Requests', commonTemplate:"true"]
    }

    @Transactional
    def getSpecimenCopyReq(){
        Integer siteId=utilService.getSiteId(request,session)
        List specimenReqs = SpecimenCopyDtl.findAllBySiteId(siteId)
        String status=""
        List specimenReqsList = specimenReqs.collect{it ->
            if(it.specimentSent ){
                status = 'Copy Sent'
            }else{
                status = 'Copy Not Sent'
            }

            return [id:it.id,name:it.name,email:it.email,phone:it.phoneNumber,address:it.address,publisher:it.publisher,level:it.level,syllabus:it.syllabus,grade:it.grade,subject:it.subject,image:it.teacherImage,dateCreated:it.dateCreated,status:status]
        }
        def json = specimenReqsList
        render json as JSON
    }


    @Transactional
    def showTeachersID(String fileName, String id){
        if(fileName!=null&&!"null".equals(fileName)&&fileName.length()>0) {
            String  picFileName = fileName.substring(0, fileName.indexOf(".")) + '.webp'
            fileName=picFileName
            response.setContentType("APPLICATION/OCTET-STREAM")
            response.setHeader("Content-Disposition", "Attachment;Filename=\"${fileName}\"")
            def file = new File("upload/SpecimenCopy/"+id+"/processed/"+ fileName)
            if (file.exists()) {
                def fileInputStream = new FileInputStream(file)
                def outputStream = response.getOutputStream()
                byte[] buffer = new byte[4096];
                int len;
                while ((len = fileInputStream.read(buffer)) > 0) {
                    outputStream.write(buffer, 0, len);
                }
                outputStream.flush()
                outputStream.close()
                fileInputStream.close()
            }   else render "";
        } else render "";
    }
    @Transactional
    def changeReqStatus(){
        SpecimenCopyDtl specimenReqs = SpecimenCopyDtl.findById(new Integer(params.id))
        specimenReqs.specimentSent = true
        specimenReqs.save(failOnError: true, flush: true)
    }
}
