@import "../theme/colors.less";
@import "../theme/fonts.less";
@import "../theme/responsive.less";

// Doubts Page Styles
.doubts-section {
  .nav-tabs {
    height:35px;
    position: relative;
    background: transparent;
    border: none;
    @media @extraSmallDevices {
      display: flex;
      justify-content: space-around;
    }
    .nav-link {
      border:none;
      border-radius: 0;
      font-size: 14px;
      color: @light-gray;
    }
  }
  .nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active {
    color: @theme-primary-color;
    @media @extraSmallDevices {
      background: lighten(@light-gray, 35%);
      border-radius: 5px;
    }
  }
  >.container {
    @media @extraSmallDevices, @smallDevices, @mediumDevices {
      padding: 0;
    }
  }
  .ask-doubt-btn {
    @media @extraSmallDevices {
      font-size: 12px;
    }
  }
  #doubt-search{
    display: none;
    align-content: center;
  }
  .search-ws {
    width:35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-width: 2px;
  }
  #search-book, .search-btn {
    height: 34px;
  }
  .askdoubt {
    position: absolute;
    right: 30px;
    top:-30px;
    border-radius: 10px;
    font-size: 16px;
    i{
      margin-right: 10px;
      font-size: 18px;
    }
    &:focus{
      outline: 0;
    }
  }
  .doubt-menu {
    position: sticky;
    top: 69px;
    z-index: 99;
    background: @white;
    border-radius: 5px;
    transition: all 0.35s linear;
    @media @extraSmallDevices {
      top: 60px;
      width:100%;
    }
    @media @smallDevices {
      top: 60px;
    }
    @media @mediumDevices {
      top: 54px;
    }
    @media @extraSmallDevices, @smallDevices, @mediumDevices {
      box-shadow: 0 4px 10px @gray-light-shadow;
      border-radius: 0 0 10px 10px;
    }
    &.reset-app {
      margin-right: 0 !important;
      margin-left: 0 !important;
      position: fixed;
      top:0;
      width:100%;
      .app-back-btn {
        display: none !important;
      }
    }
    .nav-tabs {
      li {
        a {
          padding: 0.2rem 1rem;
          border-right: 1px solid lighten(@light-gray, 35%);
          @media @extraSmallDevices {
            padding: 0.2rem 0.6rem;
            border-right: none !important;
          }
        }

        &#myAnswer-tab {
          a {
            border-right: 0;
          }
        }
        &:last-child {
          a {
            border-right: 0;
          }
        }
      }
      li:last-child {
        margin-left: 6px;
        padding-top: 4px;
        i {
          line-height: inherit;
        }
      }
      #mobile-filter {
        i {
          color: @light-gray;
          font-size: 20px;
        }
      }
    }
  }
  .filter-wrapper {
    .row {
      justify-content: center;
    }
    .myBox {
      position: relative;
      display: inline-block;
      @media @extraSmallDevices, @smallDevices, @mediumDevices {
        padding-right: 0;
      }
    }
    //.myBox:after {
    //  content: "▼";
    //  width: 25px;
    //  height: 20px;
    //  display: block;
    //  position: absolute;
    //  text-align:center;
    //  color:@dark-gray;
    //  top: 18px;
    //  right: 15px;
    //  z-index: 1;
    //  font-size: 10px;
    //  @media @extraSmallDevices, @smallDevices, @mediumDevices {
    //    right: 0;
    //  }
    //}
    .mySelect {
      position: relative;
      appearance: none;
      -moz-appearance: none;
      -webkit-appearance: none;
    }
  }
  .bootstrap-select {
    > .dropdown-toggle.bs-placeholder {
      background: none;
      border:none;
      color: @dark-gray;
      &:hover, &:focus, &:active {
        color: @dark-gray;
      }
    }
    .dropdown-toggle {
      &:focus {
        outline: 0 !important;
      }
      .filter-option {
        top:3px;
        font-size: 13px;
        padding-right: 20px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: @dark-gray;
      }
    }
    .dropup .dropdown-toggle::after {
      position: relative;
      top:3px;
    }
    .dropdown-toggle::after {
      position: relative;
      top:3px;
    }
    .open > .dropdown-menu {
      display: block;
      li a {
        display: block;
        padding: 3px 20px;
        clear: both;
        line-height: 1.5;
        color: @dark-gray;
        white-space: pre-wrap;
        font-size: 13px;
        &:hover {
          background-color: lighten(@light-gray, 30%);
        }
        span.text {
          line-height: 1.2;
        }
      }
      li.no-results {
        background: none;
        font-size: 13px;
        color: @red;
      }
    }
    .bs-searchbox .form-control {
      &:focus {
        outline: 0;
        box-shadow: none;
        border-color: @theme-primary-color;
      }
    }
    .dropdown-menu {
      @media @extraSmallDevices {
        max-width: 300px !important;
        min-width: 300px !important;
      }
    }
    button.btn-default {
      background-image: linear-gradient(lighten(@yellow, 40%), lighten(@yellow, 40%));
      border: none;
    }
  }
  .selected.form-control {
    background-image: linear-gradient(lighten(@yellow, 40%), lighten(@yellow, 40%));
    border: none;
  }
  select.form-control {
    border-radius: 5px;
    font-size: 13px;
    color: @dark-gray;
  }
  #queSave-modal{
    .bootstrap-select{
      margin-top:1.5rem !important;
      width: 100%;
      -ms-flex: auto;
      flex: auto;
      max-width: 100%;
    }
  }
  #alldoubts, #mydoubts, #myanswers {
    padding: 0;
  }
  .mobile-drop {
    @media @extraSmallDevices, @smallDevices, @mediumDevices {
      margin-top: 50px;
    }
    &.reset-app {
      margin-top: 140px;
      .discussion-card {
        &:first-child {
          @media @extraSmallDevices,@smallDevices,@mediumDevices {
            margin-top: 160px !important;
          }
        }
      }
      .filter-wrapper {
        @media @extraSmallDevices,@smallDevices,@mediumDevices {
          margin-top: 120px;
        }

      }
    }
  }

  // Both Doubt Admin and Doubts Page
  .discussion-card {
    box-sizing: border-box;
    padding: 1rem;
    padding-top: 0.5rem;
    background: @white;
    border:none;
    width: 100%;
    box-shadow: 0 0 10px @gray-light-shadow;
    border-radius: 10px;
    @media @extraSmallDevices, @smallDevices, @mediumDevices {
      padding: 10px;
      box-shadow: 0 0 10px @gray-light-shadow;
    }
    img {
      width: auto;
      max-width: 290px;
      max-height: 200px;
      border-radius: 5px;
      @media @extraSmallDevices {
        max-height: inherit;
        height: auto;
      }
    }
    ol {
      background: transparent;
      margin: 0;
      border-radius: 0;
      padding: 5px 0;
      margin-bottom: 10px;
      span {
        font-style: normal;
        font-weight: @regular;
        font-size: 10px;
        color:lighten(@light-gray, 15%);
      }
      li {
        &.breadcrumb-item {
          padding-top: 15px;
          line-height: 1.2;
        }
        a {
          color:@theme-primary-color;
          font-size: 12px;
        }
      }
      .breadcrumb-item+.breadcrumb-item::before {
        content:'>';
        font-size: 10px;
        padding: 3px;
        top: -1px;
        position: relative;
        color:@theme-primary-color;
      }
    }
  }
  .confirm,.addtag {
    background: none;
    color:@theme-primary-color;
    font-size: 10px;
    position: absolute;
    right: 5px;
    border: none;
    display: flex;
    align-items: center;
    i {
      font-size: 14px;
      color:@theme-primary-color;
    }
  }
  .breadcrumb {
    display: flex;
    align-items: center;
    min-height: 36px;
    >span {
      display: block;
      margin-right: 5px;
    }
    select {
      background: @theme-primary-color;
      color:@white;
      border-radius: 4px;
      border: none;
      margin-right: 5px;
      font-size: 12px;
      height: 26px;
    }
  }
  .profile {
    display: flex;
    align-items: center;
    img {
      width: 28px;
      height: 28px;
    }
    h4 {
      font-size:12px;
      margin-left: 10px;
      font-weight: @regular;
    }
    p {
      font-size: 10px;
      color:lighten(@light-gray, 15%);
      margin-left: 5px;
    }
  }
  .addtodoubts{
    position: absolute;
    display: flex;
    align-items: center;
    right:10px;
    top: 5px;
    button{
      color: lighten(@light-gray, 15%);
    }
    .drop-menu{
      background: none;
      border:none;
      color:@theme-primary-color;
      &:focus{
        outline:none;
      }
    }
    .dropleft .dropdown-toggle::before{
      display: none;
    }
    .dropdown-menu{
      background: @white;
      box-shadow: 0 0 10px @gray-light-shadow;
      border-radius: 5px;
      z-index: 9;
      border: none;
      i{
        font-size: 18px;
        margin-right: 10px;
        color:@theme-primary-color;
      }
      >li{
        padding: 0.3rem 0.5rem;
        &:hover{
          background: @gray-light-shadow;
        }

        a{
          cursor: pointer;
          color: @red;
          i{
            color: @red;
            -webkit-text-fill-color: @red;
          }
        }

        >a{
          &:hover{
            background: none;
          }
          display: flex;
          align-items: center;
        }
        &.notcorrect{
          >a{
            i{
              -webkit-transform: rotate(180deg);
              -moz-transform: rotate(180deg);
              -ms-transform: rotate(180deg);
              -o-transform: rotate(180deg);
              transform: rotate(180deg);
            }
          }
        }
      }
    }
    .dropdown, .dropleft, .dropright, .dropup{
      top:3px;
    }
  }
  .sub-name{
    font-size: 12px;
    color:@theme-primary-color;
  }
  .addDoubts{
    font-size: 12px;
    background: none;
    border:none;
    margin-right: 5px;
    font-weight: @light;
    &:focus{
      outline:none;
    }
    &:hover{
      color:@theme-primary-color;
    }
  }
  .answeredby{
    font-size: 12px;
    color:@theme-primary-color;
    text-transform: capitalize;
    span{
      padding-right: 5px;
      color: lighten(@light-gray, 15%);
      font-style: italic;
      font-weight: @light;
      text-transform: none;
    }
  }
  .q-question{
    color:@theme-primary-color;
  }
  .content{
    >p{
      margin-top: 0.5rem;
      color:@dark-gray;
      &:first-child{
        color:@theme-primary-color;
      }
      &:nth-child(2){
        color:@theme-primary-color;
      }
      &:nth-child(3){
        color:@theme-primary-color;
      }
    }
  }
  .card-actions{
    .dropleft .dropdown-toggle::before{
      display: none;
    }
    display: flex;
    justify-content: space-between;
    margin-top: 1rem;
    margin-bottom: 1rem;
    @media @extraSmallDevices,@smallDevices,@mediumDevices{
      padding: 0.4rem 0;
    }
    button{
      background: transparent;
      outline: 0;
      border:none;
      display: flex;
      align-items: center;
      font-size: 12px;
      color: lighten(@light-gray, 15%);
      white-space: nowrap;
      @media @extraSmallDevices,@smallDevices,@mediumDevices{
        font-size: 10px;
      }
      i{
        font-size: 14px;
        color: lighten(@light-gray, 15%);
        &.circle {
          border-radius: 50px;
          width: 24px;
          height: 24px;
          border: 1.25px solid lighten(@light-gray, 15%);
          display: flex;
          align-items: center;
          justify-content: center;
          margin-left: 5px;
          margin-right: 5px;
          &.bord{
            border-color:@blue;
            color: @blue;
          }
        }
      }
      &.drop-menu{
        background: rgba(68, 68, 68, 0.2);
        justify-content: center;
        border-radius: 4px;
        i{
          margin-right: 0;
        }
      }
    }
    .dropdown-toggle::after{
      border: none;
    }
    .dropdown-menu{
      background: @white;
      box-shadow: 0 0 10px @gray-light-shadow;
      border-radius: 5px;
      border: none;
      i{
        font-size: 18px;
        margin-right: 10px;
        color:@theme-primary-color;
      }
      >li{
        padding: 0.3rem 0.5rem;
        &:hover{
          background: @gray-light-shadow;
        }

        a{
          cursor: pointer;
          color: @red;
          i{
            color: @red;
            -webkit-text-fill-color: @red;
          }
        }

        >a{
          &:hover{
            background: none;
          }
          display: flex;
          align-items: center;
        }
        &.notcorrect{
          >a{
            i{
              -webkit-transform: rotate(180deg);
              -moz-transform: rotate(180deg);
              -ms-transform: rotate(180deg);
              -o-transform: rotate(180deg);
              transform: rotate(180deg);
            }
          }
        }
      }
    }
  }
  .card-actions button.answer{
    background: @white;
    border: 1.5px solid @theme-primary-color;
    box-sizing: border-box;
    border-radius: 5px;
    color:@theme-primary-color;
    font-weight: @regular;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    i{
      color: @theme-primary-color;
      margin-right: 10px;
    }
  }
  .flex-action{
    display: flex;
    align-items: center;
    min-height: 24px;
    #share-button{
      border-left: 0.5px solid lighten(@light-gray, 15%);
    }
  }
  .divider{
    background: url('../../images/discussionboard/line-horiz.svg') center center no-repeat;
    height: 2px;
    margin: 0 auto;
  }
  .answer-card {
    min-height: 290px;
    width:100%;
    padding: 1rem;
    border:none;
    box-shadow: 0 0 10px @gray-light-shadow;
    position: relative;
    background: @white;
    border-radius: 10px;
    @media @extraSmallDevices, @smallDevices {
      box-shadow: 0 0 10px @gray-dark-shadow;
    }
    .answer-textarea {
      min-height: 210px;
      border: 1px solid lighten(@light-gray, 15%);
      &:focus{
        outline:0;
      }
    }
    .answer-actions{
      margin-top: 1rem;
      display: flex;
      justify-content: flex-end;
      button{
        margin-right: 15px;
        i{
          color:@light-gray;
        }
        background: none;
        border:none;
        &.post{
          width: 275px;
          text-transform: uppercase;
          color:@theme-primary-color;
          min-height: 45px;
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1.25px solid @theme-primary-color;
          font-weight: 500;
        }
        &.cancels{
          width: 275px;
          background: @white;
          text-transform: uppercase;
          min-height: 45px;
          border:1px solid @light-gray;
          color:@light-gray;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
  .answer-cancel{
    border:1.5px solid @red;
    color:@red;
    align-items: center;
    justify-content: center;
    background: @white;
    box-sizing: border-box;
    border-radius: 5px;
    height: 43px;
    margin-top: 1.5rem;
    font-weight: @regular;
    i{
      color: @red;
      font-size: 18px;
      margin-right: 5px;
    }

  }
  #image-preview{
    img{
      max-height:300px;
      max-width:100%;
    }
  }
  .close-img,.close-preview{
    background: @white;
    position: absolute;
    top: -7px;
    right: 0;
    box-shadow: 0 0 10px @gray-dark-shadow;
    border: none;
    border-radius: 50px;
    width: 25px;
    height: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    i{
      color:@red;
      font-size: 18px;
    }
  }
  .close-preview{
    margin-top: 0;
    i{
      margin-right: 0;
    }
  }
  .answer-drop{
    .drop-menu{
      background: none;
      border:none;
      color:@theme-primary-color;
      &:focus{
        outline:none;
      }
    }
    .dropleft .dropdown-toggle::before{
      display: none;
    }
    .dropdown-menu{
      background: @white;
      box-shadow: 0 0 10px @gray-light-shadow;
      border-radius: 5px;
      border: none;
      z-index: 9;
      i{
        font-size: 18px;
        margin-right: 10px;
        color:@theme-primary-color;
      }
      >li{
        padding: 0.3rem 0.5rem;
        &:hover{
          background: @gray-light-shadow;
        }

        a{
          cursor: pointer;
          color: @red;
          font-size: 12px;
          i{
            color: @red;
            -webkit-text-fill-color: @red;
          }
        }

        >a{
          &:hover{
            background: none;
          }
          display: flex;
          align-items: center;
        }
        &.notcorrect{
          >a{
            i{
              -webkit-transform: rotate(180deg);
              -moz-transform: rotate(180deg);
              -ms-transform: rotate(180deg);
              -o-transform: rotate(180deg);
              transform: rotate(180deg);
            }
          }
        }
      }
    }
    .dropdown, .dropleft, .dropright, .dropup{
      top:3px;
    }
  }
  .answer-head{
    font-size: 14px;
    text-transform: uppercase;
    color:@dark-gray;
    margin-bottom: 1rem;
  }
  .userAnswer{
    background: fade(@light-gray,10%);
    margin-left: 10px;
    margin-top: 1rem;
    padding: 0.5rem;
    border-radius: 4px;
    img{
      max-width: 350px;
      @media @extraSmallDevices, @smallDevices, @mediumDevices{
        max-width: 290px;
      }
    }
    input{
      background: transparent;
    }
  }
  .question{
    font-size: 14px;
    color: lighten(@light-gray, 15%);
    font-weight: @semi-bold;
  }
  .answers{
    font-size: 12px;
    color: lighten(@light-gray, 15%);
  }
  .faq{
    color:@theme-primary-color;
    font-weight: @regular;
    font-size: 12px;
    a{
      text-decoration: underline;
      color:@theme-primary-color;
      font-weight: @bold;
    }
  }
  .que-saved{
    font-size: 16px;
    font-weight: @bold;
    color:@white;
  }
  .tag-text{
    font-size: 12px;
    font-weight: @regular;
    margin-top: 10px;
    color:@theme-primary-color;
  }
  #add-tags-btn{
    color:@theme-primary-color !important;
    border:1px solid @theme-primary-color !important;
  }
  #add-tags-btn{
    text-transform: uppercase;
    color:@theme-primary-color;
  }
  #pagination-div {
    @media @extraSmallDevices,@smallDevices,@mediumDevices {
      margin-bottom: 5rem;
    }
  }
  .row:before, .row:after {
    display: flex !important;
  }
  .cke_1{
    border:none;
  }
  .btn-askQuestion{
    background: @theme-primary-color !important;
    color:@white !important;
    font-size:12px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 5px;
    border:none !important;
    font-weight: @regular;
    padding: 10px 3rem !important;
    margin: 0 auto;
    span{
      font-weight: @bold;
    }
  }

  // Admin page only
  .admin-wrapper{
    min-height: 100vh;
    .container-fluid{
      margin: 0 4rem;
    }
  }
  .discussion-menu {
    margin-top: 1rem;
    &.nav-pills {
      .nav-item {
        margin-bottom: 1rem;
        text-align: center;
        .nav-link {
          box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
          border-radius: 5px;
          background: @white !important;
          color: @theme-primary-color;
          &.active {
            background: @theme-primary-color !important;
            color: @white;
          }
          &:focus {
            color: @white;
          }
        }
      }
    }
  }
  .line{
    position: absolute;
    left: 0;
    top: 1rem;
    img{
      height: 140px;
    }
  }
  .q-question{
    color:@theme-primary-color;
  }
  .moderate-btns{
    button{
      display: block;
      border: none;
      width: 75px;
      height: 75px;
      border-radius: 50px;
      background:@theme-primary-color;
      box-shadow: 0 0 10px @gray-dark-shadow;
      font-size: 9px;
      color: @white;
      outline: 0;
      margin: 10px auto;
      &.delete{
        border: 1px solid @theme-primary-color;
        background: @white;
        color:@red;
        i{
          color:@red;
        }
      }
      &:focus{
        outline: none;
      }
      i{
        display: block;
        margin-bottom: 5px;
      }
    }
  }
  .pagination {
    display: flex;
    align-items: center;
    li {
      margin-right: 10px;
      &.disabled{
        background: none;
        a{
          &.actions{
            color:#b4b4b4;
          }
        }
      }
      a {
        font-size: 18px;
        text-align: center;
        span {
          display: block;
        }
        background: none;
        color:@black;
        &.actions{
          font-size: 8px;
          span{
            font-size: 18px;
          }
        }
        &.page-link{
          border-radius: 0;
          border: none;
          &:hover{
            background: none;
          }
          &:focus{
            background: @theme-primary-color;
          }
        }
      }
      &.active{
        a{
          background: @theme-primary-color;
          width: 33px;
          height: 33px;
          border-radius: 50px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
  .answer-box{
    background: fade(@theme-primary-color,10%);
    width: 90%;
    border: none;
    min-height:40px;
    font-size: 14px;
  }
  .solved{
    position: absolute;
    right: 20px;
    color:@green;
    background: lighten(@light-gray, 30%);
    border-radius: 50px;
    width: 65px;
    font-size: 14px;
    text-align: center;
    display: block;
  }
  .answerContent{
    .card-actions{
      padding: 0 0.5rem;
      button{
        font-size: 10px;
      }
    }
  }
  .textOverflow{
    max-width: 98%;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
  }
  .taggs{
    display: block;
    margin-right: 10px;
  }
  .ans-modal-img-remove{
    color:@red;
    border:@red;
    border:1px solid @red;
  }
  .searchbar{
    .typeahead.dropdown-menu{
      max-width: 95%;
      min-width: 95%;
      li a{
        max-width: 98%;
        white-space: nowrap;
        overflow: hidden !important;
        text-overflow: ellipsis;
      }
    }
  }
  #cke_questionText{
    border-radius: 10px;
    border:none;
  }
  #myDoubtsTab,#myAnswerTab,#alldoubts{
    >p{
      margin-top: 1rem;
      padding: 5px;
    }
  }
  .reset-app #myDoubtsTab,#myAnswerTab,#alldoubts{
    >p{
      //margin-top: 7rem;
      padding: 5px;
    }
  }

}
#tags-modal{
  .modal-header{
    border:none;
  }
  .modal-body{
    h4{
      font-size: 14px;
    }
  }
  .modal-footer{
    border:none;
    justify-content: center;
    .skip{
      color:@light-gray;
      text-transform: uppercase;
    }
  }
}
#image-modal{
  #image-modal-body{
    p{
      text-align: center;
    }
  }
  .modal-footer{
    padding: 10px;
  }
}
#tutorial{
  .modal-dialog{
    height: 100%;
    margin: 0;
    .modal-content{
      height: 100%;
      background:rgba(0, 0, 0, 0.85);
      border: none;
      border-radius: 0;
      h2{
        font-size: 36px;
        font-weight: @bold;
      }
      p{
        font-size: 12px;
        i{
          font-size: 16px;
          position: relative;
          top: 4px;
        }
      }
    }
  }
  .modal-header{
    border: none;
    display: flex;
    align-items: center;
    .close{
      text-shadow: none;
      color:@white;
      opacity: 1;
    }
    .btn-next{
      border:1px solid @white;
      color:@white;
      border-radius: 4px;
      background: none;
      font-size:9px;
    }
  }
  .modal-footer{
    border:none;
  }
}
.carousel-item{
  h2,p{
    color:@white;
  }
  &.tutor{
    .circle{
      width: 26.25px;
      height: 26.25px;
      border-radius: 50px;
      border:1px solid @white;
      display: flex;
      align-items: center;
      justify-content: center;
      i{
        color:@white;
        font-size: 16px;
      }
    }
    .btn-answer{
      background: @white;
      border: 1.25px solid @theme-primary-color;
      box-sizing: border-box;
      border-radius: 5px;
      color:@theme-primary-color;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto;
      i{
        margin-right: 10px;
      }
    }
    h2{
      font-size: 36px;
      margin-bottom: 1rem;
    }
    p{
      font-size:14px;
      color:@white;
    }
    .wonderwonk{
      color:@white;
      font-size: 12px;
    }
    #grade-rating{
      display: flex;
      margin-right: 5px;
      i{
        background: -webkit-linear-gradient(@yellow, @yellow);
        background: -moz-linear-gradient(@yellow, @yellow);
        background: -webkit-gradient(@yellow, @yellow);
        background: -o-linear-gradient(@yellow, @yellow);
        background:  -ms-linear-gradient(@yellow, @yellow);
        background: linear-gradient(@yellow, @yellow);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        display: inline-block !important;
      }
    }
  }
}
.indicator-override{
  position: static;
  margin-bottom: 0;
  li{
    width: 14px;
    height: 8px;
    border-radius: 50%;
    background: @light-gray;
    &.active{
      background: @white;
    }
  }
}
.postmodal{
  text-align: center;
  position: absolute;
  box-shadow: 0 0 10px @gray-light-shadow;
  border-radius: 4px;
  &.fade:not(.show){
    opacity: 1;
  }
  margin: 0 auto;
  width: 319px;
  height: 190px;
  margin-top: 4rem;
  .modal-content{
    border: none;
  }
  h4 {
    font-size: 14px;
    color:@theme-primary-color;
    margin-top: 1rem;
    font-weight: @regular;
  }
  p{
    font-size: 10px;
    color:@light-gray;
  }
  .modal-dialog{
    transition: none !important;
    transform: none !important;
    margin: 0;
  }
}
.modalBackdrop{
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1040;
  width: 100%;
  height: 100%;
  background-color: @black;
  opacity: 0.5;
  border-radius: 4px;
  display: none;
}
.category-modal{
  background: @theme-primary-color !important;
  box-shadow: 0 -4px 10px @gray-light-shadow;
  border-radius: 10px 10px 0 0;
  display: none;
  position: absolute;
  &.fade:not(.show){
    opacity: 1;
  }
  margin: 0 auto;
  margin-top: 4rem;
  .modal-content{
    border: none;
    background: transparent;
    min-height: 200px;
    margin-top: 4rem;
  }
  h4 {
    font-size: 14px;
    color:@white;
    font-weight: @regular;
  }
  p{
    font-size: 10px;
    color:@white;
    margin-top: 0.5rem;
  }

  .modal-dialog{
    transition: none;
    transform: none;
    margin: 0;
  }
  .modal-footer{
    border:none;
    .skip{
      background: @white;
      box-shadow: 0 0 10px @gray-light-shadow;
      width: 100%;
      height: 50px;
      color:@theme-primary-color;
      border-radius: 5px;
      &:focus{
        outline:0;
      }
    }
  }
  .filter{
    border-color:@white !important;
    i{
      color:@white;
    }
  }
}
#mobileque-modal{
  .category-modal{
    display: block;
    position: fixed;
    .modal.fade .modal-dialog{
      transition: none !important;
    }
  }
  .modalBackdrop{
    position: fixed;
  }
  .postmodal{
    position: fixed;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
  }

}
.btn-dismiss{
  margin-top: 1rem;
  background: @theme-primary-color;
  color:@white;
  font-size: 14px;
}
.circle_around{
  border: 3px solid @theme-primary-color;
  border-radius: 50px;
  width: 40px;
  height: 40px;
  margin: 0 auto;
  margin-top: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  i{
    color:@theme-primary-color;
    font-weight: @bold;
  }

}
.btn-showMore{
  .btn-flashcard;
  color:@white !important;
}
.btn-flashcard{
  background: @theme-primary-color;
  color:@white;
}

body {
  &.guest-mode {
    .doubt-menu {
      top: 140px;
      @media @extraSmallDevices {
        top: 120px;
      }
    }
  }
  &.fixed-navbar {
    .doubt-menu {
      //top: 55px;
      @media @extraSmallDevices {
        //top: 90px;
      }
    }
  }
}