@import "../theme/colors.less";
@import "../theme/fonts.less";
@import "../theme/responsive.less";

// New UI Styles (Only for User Point of View)
.available-cards {
  a {
    margin-bottom: 30px;
    min-height: 145px;
    @media @extraSmallDevices,@smallDevices {
      margin-bottom: 20px;
    }
    &.access-granted {
      .card {
        border: 1px solid @theme-primary-color !important;
        z-index: 0;
        position: relative;
        background: lighten(@yellow,45%);
        &:hover {
          border-color: @theme-primary-color !important;
        }
        &:after {
          content: '';
          position: absolute;
          top: 0;
          left: -75%;
          z-index: 10;
          display: block;
          width: 25%;
          height: 100%;
          background: -webkit-linear-gradient(left, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, .5) 100%);
          background: linear-gradient(to right, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, .5) 100%);
          -webkit-transform: skewX(-20deg);
          transform: skewX(-20deg);
          -webkit-animation: shine 2s infinite;
          animation: shine 2s infinite;
        }
      }
    }
  }
  .card {
    box-shadow: 0 3px 7px @gray-light-shadow;
    transition: all 0.4s linear;
    padding: 25px;
    overflow: hidden;
    .institute-tag {
      position: absolute;
      top: -15px;
      right: -15px;
      z-index: 1;
      background: @black;
      height: 50px;
      width: 50px;
      border-radius: 50px;
      color: @white;
      transform: rotate(45deg);
      display: flex;
      justify-content: center;
      align-items: end;
      font-weight: @semi-bold;
      padding-bottom: 5px;
      box-shadow: 0 2px 5px @gray-light-shadow;
    }
    h5 {
      font-size: 20px;
      transition: all 0.25s linear;
      color: @orange;
    }
    p {
      line-height: normal;
    }
    .image-icon {
      width: 50px;
      position: absolute;
      right: 10px;
      opacity: 0.1;
      bottom: 10px;
      img {
        width: 100%;
        height: auto;
      }
    }
    .arrow-link-right {
      margin: 10px 0 -10px 0;
      svg {
        width: 28px;
        height: 28px;
        color: @black;
        transition: all 0.25s linear;
      }
    }
    &:hover {
      box-shadow: 0 12px 15px @gray-light-shadow;
      background-color: lighten(@yellow,45%);
      h5 {
        //color: @yellow;
      }
      .arrow-link-right svg {
        //fill: @yellow;
        margin-left: 15px;
      }
    }
  }
  .shape-arrow {
    position: absolute;
    left: 35%;
    bottom: -30%;
    z-index: -1;
    width: 150px;
    opacity: 0.1;
    //transform: rotate(-30deg);
    @media @mediumDevices {
      width: 100px;
      top: 0;
    }
    img {
      width: 100%;
      height: auto;
    }
  }
  .shape-animated {
    position: absolute;
    border-radius: 100%;
    z-index: -1;
    @media @mediumDevices,@largeDevices,@extraLargeDevices {
      animation: rotateAnimation infinite linear;
    }
  }
  .shape-round {
    width: 150px;
    height: 150px;
    background: #f9edd1;
    animation-duration: 30s;
    bottom: 0;
    right: 15%;
  }
  .shape-bg {
    width: 1400px;
    height: 1400px;
    position: absolute;
    right: -40%;
    bottom: -100%;
    z-index: -2;
    //transform: scaleX(-1) rotate(60deg);
    @media @extraSmallDevices {
      right: -100%;
      bottom: 10%;
    }
    svg {
      width: 1400px;
      height: 1400px;
    }
  }
}

@-webkit-keyframes rotateAnimation {
  0% {
    -webkit-transform: translate(0) rotate(0deg);
    transform: translate(0) rotate(0deg)
  }

  20% {
    -webkit-transform: translate(73px, 1px) rotate(36deg);
    transform: translate(73px, 1px) rotate(36deg)
  }

  40% {
    -webkit-transform: translate(141px, 72px) rotate(72deg);
    transform: translate(141px, 72px) rotate(72deg)
  }

  60% {
    -webkit-transform: translate(83px, 122px) rotate(108deg);
    transform: translate(83px, 122px) rotate(108deg)
  }

  80% {
    -webkit-transform: translate(-40px, 72px) rotate(144deg);
    transform: translate(-40px, 72px) rotate(144deg)
  }

  to {
    -webkit-transform: translate(0) rotate(0deg);
    transform: translate(0) rotate(0deg)
  }
}

@keyframes rotateAnimation {
  0% {
    -webkit-transform: translate(0) rotate(0deg);
    transform: translate(0) rotate(0deg)
  }

  20% {
    -webkit-transform: translate(73px, 1px) rotate(36deg);
    transform: translate(73px, 1px) rotate(36deg)
  }

  40% {
    -webkit-transform: translate(141px, 72px) rotate(72deg);
    transform: translate(141px, 72px) rotate(72deg)
  }

  60% {
    -webkit-transform: translate(83px, 122px) rotate(108deg);
    transform: translate(83px, 122px) rotate(108deg)
  }

  80% {
    -webkit-transform: translate(-40px, 72px) rotate(144deg);
    transform: translate(-40px, 72px) rotate(144deg)
  }

  to {
    -webkit-transform: translate(0) rotate(0deg);
    transform: translate(0) rotate(0deg)
  }
}


// My Home or Dashboard Page Styles
.db-main {
  overflow-x: hidden;
  overflow-y: hidden;
  .welcome-user h3 {
    @media @extraSmallDevices, @smallDevices {
      font-size: 1.5rem;
    }
  }
  .db-section-title {
    h5 {
      @media @extraSmallDevices, @smallDevices {
        font-size: 1rem;
      }
    }
  }
  .top_main_mobile__menu {
    margin-bottom: 2rem;
    margin-top: 1rem;
    .mdl-button-modifier {
      text-transform: inherit;
      background: @white;
      box-shadow: 0 2px 5px @gray-light-shadow;
      border-radius: 10px;
      margin: 0 .25rem;
      height: 80px;
      display: flex;
      justify-content: start;
      align-items: center;
      color: @gray;
      font-size: 12px;
      flex-direction: column;
      padding: 20px 15px;
      line-height: 1.2;
      text-align: center;
      @media @extraSmallDevices {
        height: 90px;
        padding: 18px 15px;
      }
      &:hover {
        color: @light-green;
      }
      img {
        width: 18px;
        margin-bottom: 7px;
      }
      &:nth-child(6) img {
        width: 20px;
      }
    }
  }
  .db-common-info {
    background: @white;
    .card {
      border: none;
      position: relative;
      border-right: 1px solid #EEEEEE;
      border-bottom: 1px solid #EEEEEE;
      border-radius: 0;
      justify-content: center;
      min-height: 150px;
      box-shadow: none;
      //z-index: 2;
      @media @extraSmallDevices, @smallDevices {
        min-height: 100px;
        padding: 2rem 0.5rem;
        &:nth-child(2n) {
          border-right: none;
          &:after, &:before {
            display: none;
          }
        }
      }
      @media @mediumDevices {
        padding: 2rem 1rem;
        &:nth-child(3n) {
          border-right: none;
          &:after, &:before {
            display: none;
          }
        }
      }
      @media @largeDevices {
        padding: 2rem 1rem;
        &:nth-child(4n) {
          border-right: none;
          &:after, &:before {
            display: none;
          }
        }
      }
      @media @extraLargeDevices {
        &:nth-child(6n) {
          border-right: none;
          &:after, &:before {
            display: none;
          }
        }
      }
      &:before {
        content: '';
        position: absolute;
        width: 30px;
        height: 30px;
        background: @white;
        right: -15px;
        top: -20px;
        border-radius: 50px;
        z-index: 1;
      }
      &:after {
        content: '';
        position: absolute;
        width: 30px;
        height: 30px;
        background:@white;
        right: -15px;
        bottom: -20px;
        border-radius: 50px;
        z-index: 1;
      }
      a {
        img {
          width: 50px;
          height: 50px;
          border-radius: 50px;
          margin: 0 auto;
          padding: 2px;
          @media @extraSmallDevices {
            width: 45px;
            height: 45px;
          }
        }
        p {
          margin-top: 10px;
          color: rgba(68, 68, 68, 0.85);
          @media @extraSmallDevices {
            font-size: 12px;
          }
        }
        &:hover {
          text-decoration: none;
          p {
            color: @theme-primary-color;
          }
        }
      }
      .todo-count {
        width: 50px;
        height: 50px;
        display: inline-block;
        small {
          position: absolute;
          background: @red;
          padding: 1px;
          width: 22px;
          height: 22px;
          text-align: center;
          border-radius: 50px;
          color: @white;
          font-weight: @medium;
          right: 0;
          top: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 0 10px @gray-light-shadow;
        }
      }
      &.institute-access {
        &.access-granted {
          border: 1px solid @theme-primary-color;
          background-color: #F1F1F1;
          z-index: 0;
          position: relative;
          &:after {
            content: '';
            position: absolute;
            top: 0;
            left: -75%;
            z-index: 10;
            display: block;
            width: 25%;
            height: 100%;
            background: -webkit-linear-gradient(left, rgba(255,255,255,0) 0%, rgba(255,255,255,.3) 100%);
            background: linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,.3) 100%);
            -webkit-transform: skewX(-20deg);
            transform: skewX(-20deg);
            -webkit-animation: shine 2s infinite;
            animation: shine 2s infinite;
            @media @extraSmallDevices, @smallDevices, @mediumDevices {
              display: block;
            }
          }
          p {
            font-weight: @medium;
          }
        }
      }
    }
    &.has-institutes {
      .card {
        @media @extraSmallDevices, @smallDevices, @mediumDevices {
          &:after, &:before {
            display: none;
          }
        }
      }
    }
  }
  .four-cards {
    .card {
      @media @extraLargeDevices {
        &:last-child {
          border-right: none;
          &:after, &:before {
            display: none;
          }
        }
      }
    }
  }
  .three-cards {
    .card {
      @media @mediumDevices, @largeDevices, @extraLargeDevices {
        &:last-child {
          border-right: none;
        }
      }
      @media @largeDevices, @extraLargeDevices {
        &:last-child {
          &:after, &:before {
            display: none;
          }
        }
      }
    }
  }
  .two-cards {
    .card {
      @media @mediumDevices, @largeDevices, @extraLargeDevices {
        &:last-child {
          border-right: none;
          &:after, &:before {
            display: none;
          }
        }
      }
    }
  }
  .single-card {
    .card {
      border: none;
      &:after, &:before {
        display: none;
      }
    }
  }
}