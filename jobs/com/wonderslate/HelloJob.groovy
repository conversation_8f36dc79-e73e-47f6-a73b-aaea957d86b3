package com.wonderslate

import com.wonderslate.cache.DataProviderService
import com.wonderslate.data.AutogptService
import com.wonderslate.log.TestTable
import grails.util.Environment
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class HelloJob  {
    // one line to stop accidental double-fires
    static concurrent = false      // (static in Quartz 2 plugin)
    AutogptService autogptService

    /** Define when the job should run */
    static triggers = {
        // fires every minute, 10 s after app start
        simple name: 'helloTrigger',
               startDelay: 10000L,
               repeatInterval: 60000L
        // or, if you prefer cron:
        // cron name: 'helloCron', cronExpression: '0 0/1 * * * ?'
    }

    private static final Logger log = LoggerFactory.getLogger(HelloJob)

    /** What the job actually does */
    def execute() {

		println("HelloJob says hello at ${new Date()} testTable id is  ")
        // your real work goes here
    }
}
