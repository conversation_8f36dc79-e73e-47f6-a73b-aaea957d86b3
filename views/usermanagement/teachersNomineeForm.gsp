<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/books/navheader_new"></g:render>
<link rel="stylesheet" href="/assets/wonderslate/teachersForm.css">
<link rel="stylesheet" href="/assets/wonderslate/teacherNomineeLB.css">
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<section class="headerSection-wrapper" style="height: 20vh">
    <div class="container">
        <h1 style="text-align: center"><strong style="color: #30BAC6">India's <span style="color: #F79420">Favourite</span> Teachers Award</strong></h1>

        <h3 class="text-white" style="text-align: center !important;"><strong>Nominate</strong></h3>
    </div>

</section>


<section class="formSection-wrapper">
    <div class="container">
        <div id="multi-step-form-container">
            <!-- Form Steps / Progress Bar -->
            <ul class="form-stepper form-stepper-horizontal text-center mx-auto pl-0 w-50">
                <!-- Step 1 -->
                <li class="form-stepper-active text-center form-stepper-list" step="1">
                    <a class="mx-2">
                        <span class="form-stepper-circle">
                            <span>1</span>
                        </span>
                        <div class="label">Your Details</div>
                    </a>
                </li>
                <!-- Step 2 -->
                <li class="form-stepper-unfinished text-center form-stepper-list" step="2">
                    <a class="mx-2">
                        <span class="form-stepper-circle text-muted">
                            <span>2</span>
                        </span>
                        <div class="label text-muted">Teacher's Details</div>
                    </a>
                </li>


            </ul>
            <!-- Step Wise Form Content -->
            <form id="userAccountSetupForm" name="userAccountSetupForm" class="needs-validation mb-4" method="POST">
                <!-- Step 1 Content -->
                <section id="step-1" class="form-step w-50 mx-auto">
                    <div>
                        <h3 class="font-normal" style="color: #fff !important;">Your Details</h3>
                        <div class="mt-3">
                            <div>
                                <p>Are you a Student or a Parent?</p>
                            </div>
                            <div class="custom-control custom-radio custom-control-inline">
                                <input type="radio" id="student"  name="userType" class="custom-control-input" value="student" checked>
                                <label class="custom-control-label" for="student">Student</label>
                            </div>
                            <div class="custom-control custom-radio custom-control-inline">
                                <input type="radio" id="parent" name="userType" class="custom-control-input" value="parent">
                                <label class="custom-control-label" for="parent">Parent</label>
                            </div>
                        </div>
                    </div>
                    <div>
                        <div class="mt-3">
                            <p>Your Name <span class="text-danger">*</span></p>
                            <input class="form-control" maxlength="100" type="text" id="userName" required>
                        </div>
                        <div class="invalid-tooltip-1 text-danger" style="display: none">
                            Please enter your name.
                        </div>
                    </div>

                    <div class="mt-3">
                        <p>Your City <span class="text-danger">*</span></p>
                        <input type="text" name="city" maxlength="150" placeholder="eg. Bengaluru" id="city" class="form-control mb-3">

                        <div class="invalid-tooltip-city text-danger mb-3" style="display: none">
                            Please enter your city.
                        </div>
                        <p>Your State <span class="text-danger">*</span></p>
                        <select id="state" class="form-control mb-3">

                        </select>

                        <div class="invalid-tooltip-state text-danger mb-3" style="display: none">
                            Please select your state.
                        </div>
                        <p>Your Email</p>
                        <input type="email" name="email" maxlength="300" placeholder="eg. <EMAIL>" id="emailId" class="form-control mb-3">
                        <div class="invalid-tooltip-sEmail text-danger mb-3" style="display: none">
                            Please enter your email ID.
                        </div>

                        <p>Your Mobile Number <span class="text-danger">*</span></p>
                        <input type="number" name="contactNumber" maxlength="10"maxlength="150" placeholder="eg. 123456789" id="contactNumber" class="form-control mb-3">

                        <div class="invalid-tooltip-sMob text-danger mb-3" style="display: none">
                            Please enter your mobile number.
                        </div>

                        <p>Your Pincode <span class="text-danger">*</span></p>
                        <input type="number" name="pincode" maxlength="7" placeholder="eg. 560008" id="pincode" class="form-control mb-3">

                        <div class="invalid-tooltip-sPincode text-danger mb-3" style="display: none">
                            Please enter your mobile number.
                        </div>

                    </div>
                    <div class="mt-3 d-flex justify-content-center align-items-center" style="gap: 1rem">
                        <button class="button btn-navigate-form-step" type="button" step_number="2">Next</button>
                    </div>
                </section>

                <!-- Step 2 Content, default hidden on page load. -->
                <section id="step-2" class="form-step d-none w-50 mx-auto">
                    <!-- Step 2 input fields -->
                <h2 class="font-normal" style="color: #fff !important;">Teacher's Details</h2>
                    <div class="mt-3">
                        <div>
                            <p>Name of the Teacher you are nominating <span class="text-danger">*</span></p>
                            <input type="text" name="teachersName" maxlength="150" id="teachersName" class="form-control mb-3">
                            <div class="invalid-tooltip-tchName text-danger" style="display: none">
                                Please enter teacher name.
                            </div>
                        </div>
                        <div>
                            <p>Why her or him should be recognised as an Favourite Teacher? <br>The impact they have left on your career & life. <span class="text-danger">*</span></p>
                            <textarea  name="reason" id="reason" maxlength="300" class="form-control mb-3" placeholder="Your Message"></textarea>
                            <div class="invalid-tooltip-reason text-danger" style="display: none">
                                This field cannot be empty.
                            </div>
                        </div>

                    </div>

                    <div class="mt-3">
                        <div>
                            <p>Name of the School/Coaching <span class="text-danger">*</span></p>
                            <input type="text" name="instName" maxlength="150" id="instName" class="form-control mb-3">
                            <div class="invalid-tooltip-instName text-danger" style="display: none">
                                This field cannot be empty.
                            </div>
                        </div>
                        <div>
                            <p>Class/es she or he teaches <span class="text-danger">*</span></p>
                            <input type="text" name="class" maxlength="150" id="class" class="form-control mb-3 ">
                            <div class="invalid-tooltip-class text-danger" style="display: none">
                                This field cannot be empty.
                            </div>
                        </div>
                        <div>
                            <p>Subject/s she or he teaches <span class="text-danger">*</span></p>
                            <input type="text" name="subject" maxlength="150" id="subject" class="form-control mb-3 ">
                            <div class="invalid-tooltip-sub text-danger" style="display: none">
                                This field cannot be empty.
                            </div>
                        </div>
                        <div>
                            <p>Teacher's Email ID</p>
                            <input type="email" name="teacherMail" maxlength="150" id="teacherMail" class="form-control mb-3">
                            <div class="invalid-tooltip-tcMail text-danger" style="display: none">
                                This field cannot be empty.
                            </div>
                        </div>
                        <div>
                            <p>Teacher's Mobile <span class="text-danger">*</span></p>
                            <input type="number" name="teacherContact" maxlength="10" id="teacherContact" class="form-control mb-3 ">
                            <div class="invalid-tooltip-tcMob text-danger" style="display: none">
                                This field cannot be empty.
                            </div>
                        </div>
                        <div class="form-group">
                            <p>Teacher/School Image</p>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="teacherImg" accept="image/*">
                                <label class="custom-file-label" for="teacherImg">Choose file...</label>
                            </div>
                            <p id="teacherImgFileSize" class="text-danger"></p>
                            <p id="coverImageFileName"></p>
                            <p>Image size should not exceed 100 KB</p>
                            <div class="invalid-tooltip-tcImg text-danger" style="display: none">
                                This field cannot be empty.
                            </div>
                        </div>
                    </div>

                    <div class="mt-3 d-flex justify-content-center align-items-center" style="gap: 1rem">
                        <button class="button btn-navigate-form-step btn-prev" type="button" step_number="1">Previous</button>
                        <button class="button submit-btn" type="submit" id="formSubmit">Submit</button>
                    </div>
                </section>
            </form>
        </div>
    </div>
</section>

<div class="container p-4 mt-5">
    <div class="lb-head text-center">
        <h1 class="text-white"><strong>Poll Results</strong></h1>
    </div>
</div>
<div class="container topc" style="margin-top: 45px">
    <div class="top3">

    </div>
</div>


<div class="container mb-5" id="nextList">

</div>

<div class="container text-center mt-3 pb-5">
    <a href="/usermanagement/nominations" class="btn outline-btn-1" target="_blank">View All Nominations</a>

</div>
<div class="modal fade teacher-profile-modal modal-modifier" id="profile" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog  modal-dialog-centered  modal-dialog-zoom">
        <div class="modal-content modal-content-modifier" >

            <button type="button" class="close close-icon d-flex justify-content-end mr-4" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <div class="modal-body modal-body-modifier text-center p-4">
                <div>
                    <img class="card-img">
                </div>
                <div class="p-3">
                    <h3 class="ttName"></h3>
                    <p class="ttSchl"></p>
                    <div class="d-flex align-items-center justify-content-center">
                        <p class="uCity"></p>
                        <p class="uState"></p>
                    </div>
                    <p class="ttNmd"></p>

                    <a class="btn btn-primary vBtn" id="voteBtnProfile">Vote</a>
                </div>
            </div>

        </div>
    </div>
</div>

<div id="favTeacherPage">

</div>
<g:render template="/books/footer_new"></g:render>
<script>

    const navigateToFormStep = (stepNumber) => {

        if (stepNumber == 2 && $("#userName").val() == ""){
            $('.invalid-tooltip-1').show();
            $("#userName").focus();
        }else if (stepNumber == 2 && $("#city").val() == ""){
            $('.invalid-tooltip-city').show();
            $("#city").focus();
        }else if (stepNumber == 2 && $('#state').val() == ""){
            $('.invalid-tooltip-state').show();
            $('#state').focus();
        }else if (stepNumber ==2 && $('#contactNumber').val()==""){
            $('.invalid-tooltip-sMob').show();
            $('#contactNumber').focus();
        }else if (stepNumber == 2 && $('#pincode').val() == ""){
            $('.invalid-tooltip-sPincode').show();
            $('#pincode').focus();
        }else if (stepNumber == 4 && $('#teachersName').val()==""){
            $('.invalid-tooltip-tchName').show();
            $('#teachersName').focus();
        }else if (stepNumber == 4 && $('#reason').val()==""){
            $('.invalid-tooltip-reason').show();
            $('#reason').focus();
        }
        else{
            $('.invalid-tooltip-1').hide();
            $('.invalid-tooltip-city').hide();
            $('.invalid-tooltip-state').hide();
            $('.invalid-tooltip-sEmail').hide();
            $('.invalid-tooltip-sMob').hide();
            $('.invalid-tooltip-sPincode').hide();
            $('.invalid-tooltip-tchName').hide();
            $('.invalid-tooltip-reason').hide();
            document.querySelectorAll(".form-step").forEach((formStepElement) => {
                formStepElement.classList.add("d-none");
            });
            document.querySelectorAll(".form-stepper-list").forEach((formStepHeader) => {
                formStepHeader.classList.add("form-stepper-unfinished");
                formStepHeader.classList.remove("form-stepper-active", "form-stepper-completed");
            });
            document.querySelector("#step-" + stepNumber).classList.remove("d-none");
            const formStepCircle = document.querySelector('li[step="' + stepNumber + '"]');

            formStepCircle.classList.remove("form-stepper-unfinished", "form-stepper-completed");
            formStepCircle.classList.add("form-stepper-active");

            for (let index = 0; index < stepNumber; index++) {

                const formStepCircle = document.querySelector('li[step="' + index + '"]');

                if (formStepCircle) {

                    formStepCircle.classList.remove("form-stepper-unfinished", "form-stepper-active");
                    formStepCircle.classList.add("form-stepper-completed");
                }
            }
            if (stepNumber >=2){
                $('.paragraphContent').hide();
                $(".headerSection-wrapper").css("background-color", "");
            }else{
                $('.paragraphContent').show();
                $(".headerSection-wrapper").css("background","rgba(247, 148, 32,0.5)");
            }
        }

    };

    document.querySelectorAll(".btn-navigate-form-step").forEach((formNavigationBtn) => {

        formNavigationBtn.addEventListener("click", () => {
            const stepNumber = parseInt(formNavigationBtn.getAttribute("step_number"));

            navigateToFormStep(stepNumber);
        });
    });


    var stateObject = {
        "Andhra Pradesh": [
            "Anantapur",
            "Chittoor",
            "East Godavari",
            "Guntur",
            "Krishna",
            "Kurnool",
            "Nellore",
            "Prakasam",
            "Srikakulam",
            "Visakhapatnam",
            "Vizianagaram",
            "West Godavari",
            "YSR Kadapa"
        ],
        "Arunachal Pradesh": [
            "Tawang",
            "West Kameng",
            "East Kameng",
            "Papum Pare",
            "Kurung Kumey",
            "Kra Daadi",
            "Lower Subansiri",
            "Upper Subansiri",
            "West Siang",
            "East Siang",
            "Siang",
            "Upper Siang",
            "Lower Siang",
            "Lower Dibang Valley",
            "Dibang Valley",
            "Anjaw",
            "Lohit",
            "Namsai",
            "Changlang",
            "Tirap",
            "Longding"
        ],
        "Assam": [
            "Baksa",
            "Barpeta",
            "Biswanath",
            "Bongaigaon",
            "Cachar",
            "Charaideo",
            "Chirang",
            "Darrang",
            "Dhemaji",
            "Dhubri",
            "Dibrugarh",
            "Goalpara",
            "Golaghat",
            "Hailakandi",
            "Hojai",
            "Jorhat",
            "Kamrup Metropolitan",
            "Kamrup",
            "Karbi Anglong",
            "Karimganj",
            "Kokrajhar",
            "Lakhimpur",
            "Majuli",
            "Morigaon",
            "Nagaon",
            "Nalbari",
            "Dima Hasao",
            "Sivasagar",
            "Sonitpur",
            "South Salmara-Mankachar",
            "Tinsukia",
            "Udalguri",
            "West Karbi Anglong"
        ],
        "Bihar": [
            "Araria",
            "Arwal",
            "Aurangabad",
            "Banka",
            "Begusarai",
            "Bhagalpur",
            "Bhojpur",
            "Buxar",
            "Darbhanga",
            "East Champaran (Motihari)",
            "Gaya",
            "Gopalganj",
            "Jamui",
            "Jehanabad",
            "Kaimur (Bhabua)",
            "Katihar",
            "Khagaria",
            "Kishanganj",
            "Lakhisarai",
            "Madhepura",
            "Madhubani",
            "Munger (Monghyr)",
            "Muzaffarpur",
            "Nalanda",
            "Nawada",
            "Patna",
            "Purnia (Purnea)",
            "Rohtas",
            "Saharsa",
            "Samastipur",
            "Saran",
            "Sheikhpura",
            "Sheohar",
            "Sitamarhi",
            "Siwan",
            "Supaul",
            "Vaishali",
            "West Champaran"
        ],
        "Chandigarh (UT)": [
            "Chandigarh"
        ],
        "Chhattisgarh": [
            "Balod",
            "Baloda Bazar",
            "Balrampur",
            "Bastar",
            "Bemetara",
            "Bijapur",
            "Bilaspur",
            "Dantewada (South Bastar)",
            "Dhamtari",
            "Durg",
            "Gariyaband",
            "Janjgir-Champa",
            "Jashpur",
            "Kabirdham (Kawardha)",
            "Kanker (North Bastar)",
            "Kondagaon",
            "Korba",
            "Korea (Koriya)",
            "Mahasamund",
            "Mungeli",
            "Narayanpur",
            "Raigarh",
            "Raipur",
            "Rajnandgaon",
            "Sukma",
            "Surajpur  ",
            "Surguja"
        ],
        "Dadra and Nagar Haveli (UT)": [
            "Dadra & Nagar Haveli"
        ],
        "Daman and Diu (UT)": [
            "Daman",
            "Diu"
        ],
        "Delhi (NCT)": [
            "Central Delhi",
            "East Delhi",
            "New Delhi",
            "North Delhi",
            "North East  Delhi",
            "North West  Delhi",
            "Shahdara",
            "South Delhi",
            "South East Delhi",
            "South West  Delhi",
            "West Delhi"
        ],
        "Goa": [
            "North Goa",
            "South Goa"
        ],
        "Gujarat": [
            "Ahmedabad",
            "Amreli",
            "Anand",
            "Aravalli",
            "Banaskantha (Palanpur)",
            "Bharuch",
            "Bhavnagar",
            "Botad",
            "Chhota Udepur",
            "Dahod",
            "Dangs (Ahwa)",
            "Devbhoomi Dwarka",
            "Gandhinagar",
            "Gir Somnath",
            "Jamnagar",
            "Junagadh",
            "Kachchh",
            "Kheda (Nadiad)",
            "Mahisagar",
            "Mehsana",
            "Morbi",
            "Narmada (Rajpipla)",
            "Navsari",
            "Panchmahal (Godhra)",
            "Patan",
            "Porbandar",
            "Rajkot",
            "Sabarkantha (Himmatnagar)",
            "Surat",
            "Surendranagar",
            "Tapi (Vyara)",
            "Vadodara",
            "Valsad"
        ],
        "Haryana": [
            "Ambala",
            "Bhiwani",
            "Charkhi Dadri",
            "Faridabad",
            "Fatehabad",
            "Gurgaon",
            "Hisar",
            "Jhajjar",
            "Jind",
            "Kaithal",
            "Karnal",
            "Kurukshetra",
            "Mahendragarh",
            "Mewat",
            "Palwal",
            "Panchkula",
            "Panipat",
            "Rewari",
            "Rohtak",
            "Sirsa",
            "Sonipat",
            "Yamunanagar"
        ],
        "Himachal Pradesh": [
            "Bilaspur",
            "Chamba",
            "Hamirpur",
            "Kangra",
            "Kinnaur",
            "Kullu",
            "Lahaul &amp; Spiti",
            "Mandi",
            "Shimla",
            "Sirmaur (Sirmour)",
            "Solan",
            "Una"
        ],
        "Jammu and Kashmir": [
            "Anantnag",
            "Bandipore",
            "Baramulla",
            "Budgam",
            "Doda",
            "Ganderbal",
            "Jammu",
            "Kargil",
            "Kathua",
            "Kishtwar",
            "Kulgam",
            "Kupwara",
            "Leh",
            "Poonch",
            "Pulwama",
            "Rajouri",
            "Ramban",
            "Reasi",
            "Samba",
            "Shopian",
            "Srinagar",
            "Udhampur"
        ],
        "Jharkhand": [
            "Bokaro",
            "Chatra",
            "Deoghar",
            "Dhanbad",
            "Dumka",
            "East Singhbhum",
            "Garhwa",
            "Giridih",
            "Godda",
            "Gumla",
            "Hazaribag",
            "Jamtara",
            "Khunti",
            "Koderma",
            "Latehar",
            "Lohardaga",
            "Pakur",
            "Palamu",
            "Ramgarh",
            "Ranchi",
            "Sahibganj",
            "Seraikela-Kharsawan",
            "Simdega",
            "West Singhbhum"
        ],
        "Karnataka": [
            "Bagalkot",
            "Ballari (Bellary)",
            "Belagavi (Belgaum)",
            "Bengaluru (Bangalore) Rural",
            "Bengaluru (Bangalore) Urban",
            "Bidar",
            "Chamarajanagar",
            "Chikballapur",
            "Chikkamagaluru (Chikmagalur)",
            "Chitradurga",
            "Dakshina Kannada",
            "Davangere",
            "Dharwad",
            "Gadag",
            "Hassan",
            "Haveri",
            "Kalaburagi (Gulbarga)",
            "Kodagu",
            "Kolar",
            "Koppal",
            "Mandya",
            "Mysuru (Mysore)",
            "Raichur",
            "Ramanagara",
            "Shivamogga (Shimoga)",
            "Tumakuru (Tumkur)",
            "Udupi",
            "Uttara Kannada (Karwar)",
            "Vijayapura (Bijapur)",
            "Yadgir"
        ],
        "Kerala": [
            "Alappuzha",
            "Ernakulam",
            "Idukki",
            "Kannur",
            "Kasaragod",
            "Kollam",
            "Kottayam",
            "Kozhikode",
            "Malappuram",
            "Palakkad",
            "Pathanamthitta",
            "Thiruvananthapuram",
            "Thrissur",
            "Wayanad"
        ],
        "Lakshadweep (UT)": [
            "Agatti",
            "Amini",
            "Androth",
            "Bithra",
            "Chethlath",
            "Kavaratti",
            "Kadmath",
            "Kalpeni",
            "Kilthan",
            "Minicoy"
        ],
        "Madhya Pradesh": [
            "Agar Malwa",
            "Alirajpur",
            "Anuppur",
            "Ashoknagar",
            "Balaghat",
            "Barwani",
            "Betul",
            "Bhind",
            "Bhopal",
            "Burhanpur",
            "Chhatarpur",
            "Chhindwara",
            "Damoh",
            "Datia",
            "Dewas",
            "Dhar",
            "Dindori",
            "Guna",
            "Gwalior",
            "Harda",
            "Hoshangabad",
            "Indore",
            "Jabalpur",
            "Jhabua",
            "Katni",
            "Khandwa",
            "Khargone",
            "Mandla",
            "Mandsaur",
            "Morena",
            "Narsinghpur",
            "Neemuch",
            "Panna",
            "Raisen",
            "Rajgarh",
            "Ratlam",
            "Rewa",
            "Sagar",
            "Satna",
            "Sehore",
            "Seoni",
            "Shahdol",
            "Shajapur",
            "Sheopur",
            "Shivpuri",
            "Sidhi",
            "Singrauli",
            "Tikamgarh",
            "Ujjain",
            "Umaria",
            "Vidisha"
        ],
        "Maharashtra": [
            "Ahmednagar",
            "Akola",
            "Amravati",
            "Aurangabad",
            "Beed",
            "Bhandara",
            "Buldhana",
            "Chandrapur",
            "Dhule",
            "Gadchiroli",
            "Gondia",
            "Hingoli",
            "Jalgaon",
            "Jalna",
            "Kolhapur",
            "Latur",
            "Mumbai City",
            "Mumbai Suburban",
            "Nagpur",
            "Nanded",
            "Nandurbar",
            "Nashik",
            "Osmanabad",
            "Palghar",
            "Parbhani",
            "Pune",
            "Raigad",
            "Ratnagiri",
            "Sangli",
            "Satara",
            "Sindhudurg",
            "Solapur",
            "Thane",
            "Wardha",
            "Washim",
            "Yavatmal"
        ],
        "Manipur": [
            "Bishnupur",
            "Chandel",
            "Churachandpur",
            "Imphal East",
            "Imphal West",
            "Jiribam",
            "Kakching",
            "Kamjong",
            "Kangpokpi",
            "Noney",
            "Pherzawl",
            "Senapati",
            "Tamenglong",
            "Tengnoupal",
            "Thoubal",
            "Ukhrul"
        ],
        "Meghalaya": [
            "East Garo Hills",
            "East Jaintia Hills",
            "East Khasi Hills",
            "North Garo Hills",
            "Ri Bhoi",
            "South Garo Hills",
            "South West Garo Hills ",
            "South West Khasi Hills",
            "West Garo Hills",
            "West Jaintia Hills",
            "West Khasi Hills"
        ],
        "Mizoram": [
            "Aizawl",
            "Champhai",
            "Kolasib",
            "Lawngtlai",
            "Lunglei",
            "Mamit",
            "Saiha",
            "Serchhip"
        ],
        "Nagaland": [
            "Dimapur",
            "Kiphire",
            "Kohima",
            "Longleng",
            "Mokokchung",
            "Mon",
            "Peren",
            "Phek",
            "Tuensang",
            "Wokha",
            "Zunheboto"
        ],
        "Odisha": [
            "Angul",
            "Balangir",
            "Balasore",
            "Bargarh",
            "Bhadrak",
            "Boudh",
            "Cuttack",
            "Deogarh",
            "Dhenkanal",
            "Gajapati",
            "Ganjam",
            "Jagatsinghapur",
            "Jajpur",
            "Jharsuguda",
            "Kalahandi",
            "Kandhamal",
            "Kendrapara",
            "Kendujhar (Keonjhar)",
            "Khordha",
            "Koraput",
            "Malkangiri",
            "Mayurbhanj",
            "Nabarangpur",
            "Nayagarh",
            "Nuapada",
            "Puri",
            "Rayagada",
            "Sambalpur",
            "Sonepur",
            "Sundargarh"
        ],
        "Puducherry (UT)": [
            "Karaikal",
            "Mahe",
            "Pondicherry",
            "Yanam"
        ],
        "Punjab": [
            "Amritsar",
            "Barnala",
            "Bathinda",
            "Faridkot",
            "Fatehgarh Sahib",
            "Fazilka",
            "Ferozepur",
            "Gurdaspur",
            "Hoshiarpur",
            "Jalandhar",
            "Kapurthala",
            "Ludhiana",
            "Mansa",
            "Moga",
            "Muktsar",
            "Nawanshahr (Shahid Bhagat Singh Nagar)",
            "Pathankot",
            "Patiala",
            "Rupnagar",
            "Sahibzada Ajit Singh Nagar (Mohali)",
            "Sangrur",
            "Tarn Taran"
        ],
        "Rajasthan": [
            "Ajmer",
            "Alwar",
            "Banswara",
            "Baran",
            "Barmer",
            "Bharatpur",
            "Bhilwara",
            "Bikaner",
            "Bundi",
            "Chittorgarh",
            "Churu",
            "Dausa",
            "Dholpur",
            "Dungarpur",
            "Hanumangarh",
            "Jaipur",
            "Jaisalmer",
            "Jalore",
            "Jhalawar",
            "Jhunjhunu",
            "Jodhpur",
            "Karauli",
            "Kota",
            "Nagaur",
            "Pali",
            "Pratapgarh",
            "Rajsamand",
            "Sawai Madhopur",
            "Sikar",
            "Sirohi",
            "Sri Ganganagar",
            "Tonk",
            "Udaipur"
        ],
        "Sikkim": [
            "East Sikkim",
            "North Sikkim",
            "South Sikkim",
            "West Sikkim"
        ],
        "Tamil Nadu": [
            "Ariyalur",
            "Chennai",
            "Coimbatore",
            "Cuddalore",
            "Dharmapuri",
            "Dindigul",
            "Erode",
            "Kanchipuram",
            "Kanyakumari",
            "Karur",
            "Krishnagiri",
            "Madurai",
            "Nagapattinam",
            "Namakkal",
            "Nilgiris",
            "Perambalur",
            "Pudukkottai",
            "Ramanathapuram",
            "Salem",
            "Sivaganga",
            "Thanjavur",
            "Theni",
            "Thoothukudi (Tuticorin)",
            "Tiruchirappalli",
            "Tirunelveli",
            "Tiruppur",
            "Tiruvallur",
            "Tiruvannamalai",
            "Tiruvarur",
            "Vellore",
            "Viluppuram",
            "Virudhunagar"
        ],
        "Telangana": [
            "Adilabad",
            "Bhadradri Kothagudem",
            "Hyderabad",
            "Jagtial",
            "Jangaon",
            "Jayashankar Bhoopalpally",
            "Jogulamba Gadwal",
            "Kamareddy",
            "Karimnagar",
            "Khammam",
            "Komaram Bheem Asifabad",
            "Mahabubabad",
            "Mahabubnagar",
            "Mancherial",
            "Medak",
            "Medchal",
            "Nagarkurnool",
            "Nalgonda",
            "Nirmal",
            "Nizamabad",
            "Peddapalli",
            "Rajanna Sircilla",
            "Rangareddy",
            "Sangareddy",
            "Siddipet",
            "Suryapet",
            "Vikarabad",
            "Wanaparthy",
            "Warangal (Rural)",
            "Warangal (Urban)",
            "Yadadri Bhuvanagiri"
        ],
        "Tripura": [
            "Dhalai",
            "Gomati",
            "Khowai",
            "North Tripura",
            "Sepahijala",
            "South Tripura",
            "Unakoti",
            "West Tripura"
        ],
        "Uttarakhand": [
            "Almora",
            "Bageshwar",
            "Chamoli",
            "Champawat",
            "Dehradun",
            "Haridwar",
            "Nainital",
            "Pauri Garhwal",
            "Pithoragarh",
            "Rudraprayag",
            "Tehri Garhwal",
            "Udham Singh Nagar",
            "Uttarkashi"
        ],
        "Uttar Pradesh": [
            "Agra",
            "Aligarh",
            "Allahabad",
            "Ambedkar Nagar",
            "Amethi (Chatrapati Sahuji Mahraj Nagar)",
            "Amroha (J.P. Nagar)",
            "Auraiya",
            "Azamgarh",
            "Baghpat",
            "Bahraich",
            "Ballia",
            "Balrampur",
            "Banda",
            "Barabanki",
            "Bareilly",
            "Basti",
            "Bhadohi",
            "Bijnor",
            "Budaun",
            "Bulandshahr",
            "Chandauli",
            "Chitrakoot",
            "Deoria",
            "Etah",
            "Etawah",
            "Faizabad",
            "Farrukhabad",
            "Fatehpur",
            "Firozabad",
            "Gautam Buddha Nagar",
            "Ghaziabad",
            "Ghazipur",
            "Gonda",
            "Gorakhpur",
            "Hamirpur",
            "Hapur (Panchsheel Nagar)",
            "Hardoi",
            "Hathras",
            "Jalaun",
            "Jaunpur",
            "Jhansi",
            "Kannauj",
            "Kanpur Dehat",
            "Kanpur Nagar",
            "Kanshiram Nagar (Kasganj)",
            "Kaushambi",
            "Kushinagar (Padrauna)",
            "Lakhimpur - Kheri",
            "Lalitpur",
            "Lucknow",
            "Maharajganj",
            "Mahoba",
            "Mainpuri",
            "Mathura",
            "Mau",
            "Meerut",
            "Mirzapur",
            "Moradabad",
            "Muzaffarnagar",
            "Pilibhit",
            "Pratapgarh",
            "RaeBareli",
            "Rampur",
            "Saharanpur",
            "Sambhal (Bhim Nagar)",
            "Sant Kabir Nagar",
            "Shahjahanpur",
            "Shamali (Prabuddh Nagar)",
            "Shravasti",
            "Siddharth Nagar",
            "Sitapur",
            "Sonbhadra",
            "Sultanpur",
            "Unnao",
            "Varanasi"
        ],
        "West Bengal": [
            "Alipurduar",
            "Bankura",
            "Birbhum",
            "Burdwan (Bardhaman)",
            "Cooch Behar",
            "Dakshin Dinajpur (South Dinajpur)",
            "Darjeeling",
            "Hooghly",
            "Howrah",
            "Jalpaiguri",
            "Kalimpong",
            "Kolkata",
            "Malda",
            "Murshidabad",
            "Nadia",
            "North 24 Parganas",
            "Paschim Medinipur (West Medinipur)",
            "Purba Medinipur (East Medinipur)",
            "Purulia",
            "South 24 Parganas",
            "Uttar Dinajpur (North Dinajpur)"
        ]
    };

    var stateKeysObj = Object.keys(stateObject);
    var shtml="<option value=''>Select State</option>";
    for (var s=0;s<stateKeysObj.length;s++){
        shtml +="<option value='"+stateKeysObj[s]+"'>"+stateKeysObj[s]+"</option>"
    }
    document.getElementById('state').innerHTML = shtml;
    var submitData = "";
    var validImg=true;


    $("#userName,#city,#emailId,#state,#pincode,#contactNumber,#teachersName,#reason,#teacherContact,#instName,#class,#subject,#teacherContact").on('keydown',function (){
        $('.invalid-tooltip-1').hide();
        $('.invalid-tooltip-city').hide();
        $('.invalid-tooltip-sMob').hide();
        $('.invalid-tooltip-sPincode').hide();
        $('.invalid-tooltip-state').hide();
        $('.invalid-tooltip-tchName').hide();
        $('.invalid-tooltip-reason').hide();
        $('.invalid-tooltip-instName').hide();
        $('.invalid-tooltip-class').hide();
        $('.invalid-tooltip-sub').hide();
        $('.invalid-tooltip-tcMob').hide();
        $('.invalid-tooltip-tcImg').hide();
    })
    $("#state").on('change',function (){
        $('.invalid-tooltip-state').hide();
    })


    $('#teacherImg').change(function() {
        var selectedFile = this.files[0];
        var _size = selectedFile.size;
        $('.invalid-tooltip-tcImg').hide();
        if(_size >= 102400){
            document.getElementById("teacherImgFileSize").innerHTML = "File size exceeds 100 KB.";
            validImg=false;
            $('#teacherImg').val('');
        }else{
            $('#teacherImgFileSize').text('');
            validImg=true;
            document.getElementById("coverImageFileName").innerHTML = "<strong>File name: </strong> "+selectedFile.name+" <a href='javascript:removeGroupCoverImage();' class='text-danger'>Remove</a>";
        }
    });

    function removeGroupCoverImage() {
        $('#teacherImgFileSize').text('')
        $('#coverImageFileName').text('');
        $('#teacherImg').val('');
    }
    $("#formSubmit").on('click',function (e){
        e.preventDefault();
        if ($('#teachersName').val()==""){
            $('.invalid-tooltip-tchName').show();
            $('#teachersName').focus();
        }else if ($('#reason').val()==""){
            $('.invalid-tooltip-reason').show();
            $('#reason').focus();
        } else if($('#instName').val() == ""){
            $('.invalid-tooltip-instName').show();
            $('#instName').focus();
        }else if($('#class').val()==""){
            $('.invalid-tooltip-class').show();
            $('#class').focus();
        }else if ($('#subject').val()==""){
            $('.invalid-tooltip-sub').show();
            $('#subject').focus();
        }else if ($('#teacherContact').val()==""){
            $('.invalid-tooltip-tcMob').show();
            $('#teacherContact').focus();
        } else{
            $('.invalid-tooltip-instName').hide();
            $('.invalid-tooltip-class').hide();
            $('.invalid-tooltip-sub').hide();
            $('.invalid-tooltip-tcMail').hide();
            $('.invalid-tooltip-tcMob').hide();
            $('.invalid-tooltip-tcImg').hide();

            var formData = new FormData();
            var files = $("#teacherImg")[0].files;
            submitData = {
                userName:$("#userName").val(),
                userType:$('input[name="userType"]:checked').val(),
                userCity:$('#city').val(),
                userMobile:$('#contactNumber').val(),
                userEmail:$("#emailId").val(),
                userPincode:$('#pincode').val(),
                userState:$('#state').val(),
                teacherClass:$("#class").val(),
                teacherEmail:$("#teacherMail").val(),
                teacherName:$('#teachersName').val(),
                teacherMobile:$("#teacherContact").val(),
                description:$('#reason').val(),
                comments:$("#message").val(),
                schoolName:$('#instName').val(),
                teacherSubject:$('#subject').val()
            }

            submitData = JSON.stringify(submitData);
            $(".loading-icon").removeClass("hidden");
            $.ajax({
                type: "POST",
                url: '/usermanagement/addTeacherNomineeDetails',
                dataType: 'json',
                contentType : "application/json",
                data:  submitData,
                success: function(data) {
                    if(files.length > 0){
                        addTeacherImage(data)
                    }else{
                        $(".loading-icon").addClass("hidden");
                        window.location.href = '/usermanagement/nomineeDetails?nomineeId='+data.id
                    }
                }
            });

            function addTeacherImage(data){
                if(files.length > 0) {
                    formData.append('file',files[0]);
                    formData.append('id', data.id);
                    $.ajax({
                        type: "POST",
                        url: '/usermanagement/addTeacherImage',
                        type: 'POST',
                        cache: false,
                        contentType: false,
                        processData: false,
                        data: formData,
                        success: function(data) {
                            $(".loading-icon").addClass("hidden");
                            var nomineeId= data.id;
                            window.location.href = '/usermanagement/nomineeDetails?nomineeId='+nomineeId
                        }
                    });
                }

            }
        }
    })

    //LEADERBOARD
    var currentDate = new Date().toISOString().split("T")[0];
    var nextLeaders = [];
    var topLeaders =[];
    var dataValues;

    function getDailRank(){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="usermanagement" action="getTeachersNomineeDetails" onSuccess="dailyRankUI(data)" />
    }


    getDailRank()
    function dailyRankUI(data){
        $('.loading-icon').addClass('hidden');
        if (data !=='no ranks'){
            dataValues = data;

            var leaderBoardData = data.teachersNomineeList;
            var lbHtml = "";
            var tHtml = "";

            nextLeaders = leaderBoardData.splice(3);
            topLeaders = leaderBoardData;

            for (var i=0;i<nextLeaders.length;i++){

                lbHtml +="<div class='d-flex align-items-center lb-wrapper mb-3' onclick='openProfile("+(i+4)+")'>\n"+
                    "<div class='d-flex align-items-center t-position'>\n"+
                    "<h3>#</h3>\n"+
                    "<h3>"+(i+4)+"</h3>\n"+
                    "</div>";
                lbHtml += "<div class='lb-content'>\n"+
                    "<div class='lb-content__img-wrapper'>";
                if(nextLeaders[i].teacherImage !=null && nextLeaders[i].teacherImage !="" && nextLeaders[i].teacherImage!=undefined){
                    lbHtml +="<img src='/usermanagement/showTeachersImage?id="+nextLeaders[i].id+"&fileName="+nextLeaders[i].teacherImage+"'>";
                }else{
                    lbHtml +="<img src='/assets/landingpageImages/img_avatar3.png'>";
                }
                lbHtml +=  "</div>";

                lbHtml += "<div class='lb-content__list'>";
                lbHtml +="<div>\n"+
                    "<h3>"+nextLeaders[i].teacherName+"</h3>";
                if (nextLeaders[i].schoolName !=undefined && nextLeaders[i].schoolName !=null){
                    lbHtml +=  "<p>"+nextLeaders[i].schoolName+"</p>";
                }

                lbHtml += "</div>";
                lbHtml += "<div class='lb-content__list-votes text-center'>\n"+
                    "<p>"+nextLeaders[i].voteCount+"</p>\n"+
                    "<p>Votes</p>\n"+
                    "</div>";
                lbHtml+="</div>";
                lbHtml+=" </div>";
                lbHtml+=" </div>";

            }


            for(var t=0;t<topLeaders.length;t++){
                tHtml +="<div class='item' id='item-"+(t+1)+"' onclick='openProfile("+(t+1)+")'>\n"+
                    "<div class='pos'>"+(t+1)+"</div>";
                if(topLeaders[t].teacherImage !=null && topLeaders[t].teacherImage !="" && topLeaders[t].teacherImage!=undefined){
                    tHtml +="<div class='pic' style='background-image: url(/usermanagement/showTeachersImage?id="+topLeaders[t].id+"&fileName="+topLeaders[t].teacherImage+")'></div>";
                }else{
                    tHtml +="<div class='pic' style='background-image: url(/assets/landingpageImages/img_avatar3.png)'></div>";
                }
                tHtml +="<div class='name'>"+topLeaders[t].teacherName+"</div>";
                if (topLeaders[t].schoolName !=null &&  topLeaders[t].schoolName != undefined){
                    tHtml +="<div class='schl'>"+topLeaders[t].schoolName+"</div>";
                }
                tHtml +=   "<div class='score'>"+topLeaders[t].voteCount+"</div>";
                tHtml += "<div class=''>\n"+
                    "<img src='/assets/prepJoy/"+(t+1)+".svg' alt='' class='badgeImg'>\n"+
                    "</div>\n"+
                    "</div>";
            }

            document.querySelector('#nextList').innerHTML = lbHtml;
            document.querySelector('.top3').innerHTML = tHtml;
            $('.top3').css('margin-bottom','20px');
        }else{
            var tHtml = "";
            tHtml +="<div class='d-flex flex-column justify-content-center align-items-center'><h2>Not many leaders?</h2>\n"+
                "<p><a href='/usermanagement/teachersNomineeForm'>Nominate your favourite teacher now.</a></p></div>";
            document.querySelector('.list').innerHTML = "";
            document.querySelector('.top3').innerHTML = tHtml; // can be changed
            $('.top3').css('margin-bottom','75px');
        }

        $("#item-1").addClass('one');
        $("#item-2").addClass('two');
        $("#item-3").addClass('three');
    }

    $( window ).on( "load", function (){
        $('.loading-icon').removeClass('hidden');
    });
    $( document ).ready(function() {
        $('.loading-icon').addClass('hidden');
    });

    function openProfile(id){
        if (id > 3){
            var selectedData = nextLeaders[id-4];
        }else{
            var selectedData = topLeaders[id-1];
        }

        if (selectedData.teacherImage !=null && selectedData.teacherImage!="" && selectedData.teacherImage != undefined){
            $('.card-img').attr("src","/usermanagement/showTeachersImage?id="+selectedData.id+"&fileName="+selectedData.teacherImage+"");
        }else{
            $('.card-img').attr("src","/assets/landingpageImages/img_avatar3.png");
        }
        $('.ttName').text(selectedData.teacherName);
        $('.ttNmd').text("Nominated By :"+selectedData.userName);
        if (selectedData.schoolName != undefined){
            $('.ttSchl').text(selectedData.schoolName);
        }

        $('#voteBtnProfile').attr('href','/usermanagement/nomineeDetails?nomineeId='+selectedData.id);
        $('#voteBtnProfile').attr('target','_blank');
        $('.vc').text(selectedData.voteCount);
        $('.uCity').text(selectedData.userCity+",");
        $('.uState').text(" "+selectedData.userState);

        $('#profile').modal('show')
    }
</script>