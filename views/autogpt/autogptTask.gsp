<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}

@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
.form-group a {
    color: white;
}

/* Enhanced styles for chapter selection and status display */
.chapter-selection-container {
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
    background-color: #f9f9f9;
}

.chapter-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ccc;
    padding: 10px;
    background-color: white;
    border-radius: 3px;
}

.chapter-item {
    padding: 8px;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
}

.chapter-item:last-child {
    border-bottom: none;
}

.chapter-item input[type="checkbox"] {
    margin-right: 10px;
}

.chapter-item label {
    margin: 0;
    cursor: pointer;
    flex-grow: 1;
}

.select-all-container {
    padding: 10px;
    background-color: #e9ecef;
    border-bottom: 1px solid #ccc;
    font-weight: bold;
}

.status-panel {
    border: 1px solid #ddd;
    border-radius: 5px;
    margin-bottom: 15px;
    background-color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.status-header {
    padding: 12px 15px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #ddd;
    font-weight: bold;
    border-radius: 5px 5px 0 0;
}

.status-body {
    padding: 15px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.status-item:last-child {
    border-bottom: none;
}

.status-label {
    font-weight: 500;
}

.status-value {
    display: flex;
    align-items: center;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    margin-right: 8px;
}

.status-pending {
    background-color: #6c757d;
    color: white;
}

.status-progress {
    background-color: #007bff;
    color: white;
}

.status-success {
    background-color: #28a745;
    color: white;
}

.status-error {
    background-color: #dc3545;
    color: white;
}

.timing-display {
    font-family: monospace;
    font-size: 11px;
    color: #666;
}

.progress-container {
    margin-top: 20px;
}

.overall-progress {
    margin-bottom: 20px;
}

.validation-error {
    color: #dc3545;
    font-size: 14px;
    margin-top: 10px;
    display: none;
}

.btn-submit {
    background-color: #007bff;
    border-color: #007bff;
    padding: 10px 30px;
    font-size: 16px;
}

.btn-submit:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: none;
    z-index: 9999;
}

.loading-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 2s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-content">
        <div class="spinner"></div>
        <div id="loadingMessage">Loading chapters...</div>
    </div>
</div>

<div class="container-fluid" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-10 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h3 class="text-center">AutoGPT V21 - Enhanced Task Manager</h3>

                    <!-- Chapter Selection Section -->
                    <div class="chapter-selection-container">
                        <h4>Select Chapters for Processing</h4>
                        <div class="select-all-container">
                            <label>
                                <input type="checkbox" id="selectAllChapters">
                                <strong>Select All Chapters</strong>
                            </label>&nbsp;&nbsp;
                            <label>
                                <input type="checkbox" id="createSnapshot">
                                <strong>Create Snapshot</strong>
                            </label>
                        </div>
                        <div class="chapter-list" id="chapterList">
                            <g:if test="${availableChapters && availableChapters.size() > 0}">
                                <g:set var="currentBookId" value="" />
                                <g:each in="${availableChapters}" var="chapter">
                                    <g:if test="${chapter.bookId != currentBookId}">
                                        <g:set var="currentBookId" value="${chapter.bookId}" />
                                        <div style="background-color: #e9ecef; padding: 8px; font-weight: bold; margin-top: 10px; border-radius: 3px;">
                                            ${bookTitle}
                                        </div>
                                    </g:if>
                                    <div class="chapter-item">
                                        <input type="checkbox" class="chapter-checkbox" value="${chapter.id}" id="chapter_${chapter.id}">
                                        <label for="chapter_${chapter.id}">${chapter.name} (ID: ${chapter.id})</label>
                                    </div>
                                </g:each>
                            </g:if>
                            <g:else>
                                <div class="text-center" style="padding: 20px;">
                                    No chapters available for processing.
                                </div>
                            </g:else>
                        </div>
                        <div class="validation-error" id="validationError">
                            Please select at least one chapter before proceeding.
                        </div>
                        <div style="margin-top: 15px;">
                            <button class="btn btn-primary btn-submit" id="submitBtn" onclick="startAutoGPTProcessing()" disabled>
                                Start AutoGPT Processing
                            </button>&nbsp;&nbsp;
                            <button class="btn btn-secondary" onclick="addToAutoGPTQueue()">
                                Add to AutoGPT Queue
                            </button>
                        </div>
                    </div>

                    <!-- Progress Section -->
                    <div class="progress-container" id="progressContainer" style="display: none;">
                        <div class="overall-progress">
                            <h4>Overall Progress</h4>
                            <div class="progress">
                                <div class="progress-bar" id="overallProgressBar" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                            </div>
                            <div style="margin-top: 10px;">
                                <span id="overallStatus">Preparing to start...</span>
                                <span class="timing-display float-right" id="overallTiming">Total Time: 00:00</span>
                            </div>
                        </div>

                        <!-- Individual Chapter Status Panels -->
                        <div id="chapterStatusPanels">
                            <!-- Status panels will be dynamically generated here -->
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>

</body>
</html>

<script>
    // Global variables
    let selectedChapters = [];
    let processingQueue = [];
    let currentProcessingIndex = 0;
    let overallStartTime = null;
    let chapterTimings = {};

    // Initialize the page
    $(document).ready(function() {
        setupEventListeners();
    });

    function setupEventListeners() {
        // Select All checkbox functionality
        $('#selectAllChapters').change(function() {
            const isChecked = $(this).is(':checked');
            $('.chapter-checkbox').prop('checked', isChecked);
            updateSelectedChapters();
        });

        // Individual chapter checkbox change
        $(document).on('change', '.chapter-checkbox', function() {
            updateSelectedChapters();
            updateSelectAllState();
        });
    }

    function showLoading(message) {
        $('#loadingMessage').text(message);
        $('#loadingOverlay').show();
    }

    function hideLoading() {
        $('#loadingOverlay').hide();
    }



    function updateSelectedChapters() {
        selectedChapters = [];
        $('.chapter-checkbox:checked').each(function() {
            const chapterId = $(this).val();
            const chapterName = $(this).next('label').text();
            selectedChapters.push({
                id: chapterId,
                name: chapterName
            });
        });

        // Update submit button state
        const submitBtn = $('#submitBtn');
        if (selectedChapters.length > 0) {
            submitBtn.prop('disabled', false);
            $('#validationError').hide();
        } else {
            submitBtn.prop('disabled', true);
        }
    }

    function updateSelectAllState() {
        const totalCheckboxes = $('.chapter-checkbox').length;
        const checkedCheckboxes = $('.chapter-checkbox:checked').length;

        const selectAllCheckbox = $('#selectAllChapters');
        if (checkedCheckboxes === 0) {
            selectAllCheckbox.prop('indeterminate', false);
            selectAllCheckbox.prop('checked', false);
        } else if (checkedCheckboxes === totalCheckboxes) {
            selectAllCheckbox.prop('indeterminate', false);
            selectAllCheckbox.prop('checked', true);
        } else {
            selectAllCheckbox.prop('indeterminate', true);
        }
    }

    function showError(message) {
        alert(message);
    }

    function formatTime(milliseconds) {
        const totalSeconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = totalSeconds % 60;
        return minutes.toString().padStart(2, '0') + ':' + seconds.toString().padStart(2, '0');
    }

    function addToAutoGPTQueue() {
        if (selectedChapters.length === 0) {
            $('#validationError').show();
            return;
        }

        var chapterIds = selectedChapters.map(chapter => chapter.id).join(',');
        var createSnapshot = $('#createSnapshot').is(':checked');
        <g:remoteFunction controller="autogpt" action="addToAutoGptTask" params="'chapterIds='+chapterIds+'&createSnapshot='+createSnapshot" onSuccess="queueAdded(data)"/>

    }
    function queueAdded(data) {
        if (data.status === 'OK') {
            alert('Chapters added to AutoGPT queue successfully!');
        } else {
            alert('Error adding chapters to AutoGPT queue: ' + data.message);
        }
    }


    function startAutoGPTProcessing() {
        if (selectedChapters.length === 0) {
            $('#validationError').show();
            return;
        }

        // Initialize processing
        processingQueue = [...selectedChapters];
        currentProcessingIndex = 0;
        overallStartTime = Date.now();
        chapterTimings = {};

        // Show progress container
        $('#progressContainer').show();

        // Create status panels for each selected chapter
        createChapterStatusPanels();

        // Update overall status
        updateOverallProgress();

        // Disable submit button
        $('#submitBtn').prop('disabled', true);

        // Start processing first chapter
        processNextChapter();
    }

    function createChapterStatusPanels() {
        const container = $('#chapterStatusPanels');
        container.empty();

        selectedChapters.forEach(function(chapter) {
            const panelHtml =
                '<div class="status-panel" id="panel_' + chapter.id + '">' +
                    '<div class="status-header">' +
                        chapter.name + ' (ID: ' + chapter.id + ')' +
                    '</div>' +
                    '<div class="status-body">' +
                        '<div class="status-item">' +
                            '<span class="status-label">Overall Status:</span>' +
                            '<span class="status-value">' +
                                '<span class="status-badge status-pending" id="overall_status_' + chapter.id + '">Pending</span>' +
                                '<span class="timing-display" id="overall_timing_' + chapter.id + '">00:00</span>' +
                            '</span>' +
                        '</div>' +
                        '<div class="status-item">' +
                            '<span class="status-label">PDF Vectors:</span>' +
                            '<span class="status-value">' +
                                '<span class="status-badge status-pending" id="vectors_status_' + chapter.id + '">Pending</span>' +
                                '<span class="timing-display" id="vectors_timing_' + chapter.id + '">00:00</span>' +
                            '</span>' +
                        '</div>' +
                        '<div class="status-item">' +
                            '<span class="status-label">Metadata:</span>' +
                            '<span class="status-value">' +
                                '<span class="status-badge status-pending" id="metadata_status_' + chapter.id + '">Pending</span>' +
                                '<span class="timing-display" id="metadata_timing_' + chapter.id + '">00:00</span>' +
                            '</span>' +
                        '</div>' +
                        '<div class="status-item">' +
                            '<span class="status-label">Exercise Collection:</span>' +
                            '<span class="status-value">' +
                                '<span class="status-badge status-pending" id="exercises_status_' + chapter.id + '">Pending</span>' +
                                '<span class="timing-display" id="exercises_timing_' + chapter.id + '">00:00</span>' +
                            '</span>' +
                        '</div>' +
                        '<div class="status-item">' +
                            '<span class="status-label">Question Bank:</span>' +
                            '<span class="status-value">' +
                                '<span class="status-badge status-pending" id="questionbank_status_' + chapter.id + '">Pending</span>' +
                                '<span class="timing-display" id="questionbank_timing_' + chapter.id + '">00:00</span>' +
                            '</span>' +
                        '</div>' +
                    '</div>' +
                '</div>';

            container.append(panelHtml);
        });
    }

    function updateOverallProgress() {
        const totalChapters = selectedChapters.length;
        const completedChapters = currentProcessingIndex;
        const progressPercentage = totalChapters > 0 ? Math.round((completedChapters / totalChapters) * 100) : 0;

        $('#overallProgressBar').css('width', progressPercentage + '%').text(progressPercentage + '%');

        let statusText = '';
        if (completedChapters === 0) {
            statusText = 'Starting processing...';
        } else if (completedChapters < totalChapters) {
            statusText = 'Processing chapter ' + (currentProcessingIndex + 1) + ' of ' + totalChapters;
        } else {
            statusText = 'All chapters completed!';
        }
        $('#overallStatus').text(statusText);

        // Update overall timing
        if (overallStartTime) {
            const elapsed = Date.now() - overallStartTime;
            $('#overallTiming').text('Total Time: ' + formatTime(elapsed));
        }
    }

    function processNextChapter() {
        if (currentProcessingIndex >= processingQueue.length) {
            // All chapters completed
            updateOverallProgress();
            $('#submitBtn').prop('disabled', false).text('Start New Processing');
            return;
        }

        const chapter = processingQueue[currentProcessingIndex];
        chapterTimings[chapter.id] = {
            start: Date.now(),
            vectors: null,
            metadata: null,
            exercises: null,
            questionbank: null
        };

        // Update overall status to show current chapter
        $('#overall_status_' + chapter.id).removeClass('status-pending').addClass('status-progress').text('In Progress');

        // Start with PDF vectors
        storeVectorsForChapter(chapter);
    }

    function storeVectorsForChapter(chapter) {
        const startTime = Date.now();
        $('#vectors_status_' + chapter.id).removeClass('status-pending').addClass('status-progress').text('Processing');

        <g:remoteFunction controller="autogpt" action="storePdfVectors" params="'chapterId='+chapter.id" onSuccess="vectorStored(data, chapter, startTime);" onFailure="vectorFailed(chapter, startTime);"/>
    }

    function vectorStored(data, chapter, startTime) {
        const endTime = Date.now();
        const elapsed = endTime - startTime;
        chapterTimings[chapter.id].vectors = elapsed;

        $('#vectors_timing_' + chapter.id).text(formatTime(elapsed));

        if (data.status === 'OK') {
            $('#vectors_status_' + chapter.id).removeClass('status-progress').addClass('status-success').text('Success');
            // Store resId for next steps
            chapter.resId = data.resId;
            // Continue to metadata
            storeMetadataForChapter(chapter);
        } else {
            $('#vectors_status_' + chapter.id).removeClass('status-progress').addClass('status-error').text('Failed');
            $('#overall_status_' + chapter.id).removeClass('status-progress').addClass('status-error').text('Failed');
            // Move to next chapter
            currentProcessingIndex++;
            updateOverallProgress();
            processNextChapter();
        }
    }

    function vectorFailed(chapter, startTime) {
        const endTime = Date.now();
        const elapsed = endTime - startTime;
        $('#vectors_timing_' + chapter.id).text(formatTime(elapsed));
        $('#vectors_status_' + chapter.id).removeClass('status-progress').addClass('status-error').text('Error');
        $('#overall_status_' + chapter.id).removeClass('status-progress').addClass('status-error').text('Failed');
        // Move to next chapter
        currentProcessingIndex++;
        updateOverallProgress();
        processNextChapter();
    }

    function storeMetadataForChapter(chapter) {
        const startTime = Date.now();
        $('#metadata_status_' + chapter.id).removeClass('status-pending').addClass('status-progress').text('Processing');

        <g:remoteFunction controller="autogpt" action="getChapterMetaData" params="'resId='+chapter.resId" onSuccess="metadataStored(data, chapter, startTime);" onFailure="metadataFailed(chapter, startTime);"/>
    }

    function metadataStored(data, chapter, startTime) {
        const endTime = Date.now();
        const elapsed = endTime - startTime;
        chapterTimings[chapter.id].metadata = elapsed;

        $('#metadata_timing_' + chapter.id).text(formatTime(elapsed));

        if (data.status === 'OK') {
            $('#metadata_status_' + chapter.id).removeClass('status-progress').addClass('status-success').text('Success');
            // Continue to exercise collection
            collectExercisesForChapter(chapter);
        } else {
            $('#metadata_status_' + chapter.id).removeClass('status-progress').addClass('status-error').text('Failed');
            $('#overall_status_' + chapter.id).removeClass('status-progress').addClass('status-error').text('Failed');
            // Move to next chapter
            currentProcessingIndex++;
            updateOverallProgress();
            processNextChapter();
        }
    }

    function metadataFailed(chapter, startTime) {
        const endTime = Date.now();
        const elapsed = endTime - startTime;
        $('#metadata_timing_' + chapter.id).text(formatTime(elapsed));
        $('#metadata_status_' + chapter.id).removeClass('status-progress').addClass('status-error').text('Error');
        $('#overall_status_' + chapter.id).removeClass('status-progress').addClass('status-error').text('Failed');
        // Move to next chapter
        currentProcessingIndex++;
        updateOverallProgress();
        processNextChapter();
    }

    function collectExercisesForChapter(chapter) {
        const startTime = Date.now();
        $('#exercises_status_' + chapter.id).removeClass('status-pending').addClass('status-progress').text('Processing');

        <g:remoteFunction controller="autogpt" action="exerciseCollector" params="'resId='+chapter.resId" onSuccess="exercisesCollected(data, chapter, startTime);" onFailure="exercisesFailed(chapter, startTime);"/>
    }

    function exercisesCollected(data, chapter, startTime) {
        const endTime = Date.now();
        const elapsed = endTime - startTime;
        chapterTimings[chapter.id].exercises = elapsed;

        $('#exercises_timing_' + chapter.id).text(formatTime(elapsed));

        if (data.status === 'OK') {
            $('#exercises_status_' + chapter.id).removeClass('status-progress').addClass('status-success').text('Success');
            // Continue to question bank building
            buildQuestionBankForChapter(chapter);
        } else {
            $('#exercises_status_' + chapter.id).removeClass('status-progress').addClass('status-error').text('Failed');
            $('#overall_status_' + chapter.id).removeClass('status-progress').addClass('status-error').text('Failed');
            // Move to next chapter
            currentProcessingIndex++;
            updateOverallProgress();
            processNextChapter();
        }
    }

    function exercisesFailed(chapter, startTime) {
        const endTime = Date.now();
        const elapsed = endTime - startTime;
        $('#exercises_timing_' + chapter.id).text(formatTime(elapsed));
        $('#exercises_status_' + chapter.id).removeClass('status-progress').addClass('status-error').text('Error');
        $('#overall_status_' + chapter.id).removeClass('status-progress').addClass('status-error').text('Failed');
        // Move to next chapter
        currentProcessingIndex++;
        updateOverallProgress();
        processNextChapter();
    }

    function buildQuestionBankForChapter(chapter) {
        const startTime = Date.now();
        $('#questionbank_status_' + chapter.id).removeClass('status-pending').addClass('status-progress').text('Processing');

        <g:remoteFunction controller="autogpt" action="questionBankBuilder" params="'resId='+chapter.resId" onSuccess="questionBankBuilt(data, chapter, startTime);" onFailure="questionBankFailed(chapter, startTime);"/>
    }

    function questionBankBuilt(data, chapter, startTime) {
        const endTime = Date.now();
        const elapsed = endTime - startTime;
        chapterTimings[chapter.id].questionbank = elapsed;

        $('#questionbank_timing_' + chapter.id).text(formatTime(elapsed));

        if (data.status === 'OK') {
            $('#questionbank_status_' + chapter.id).removeClass('status-progress').addClass('status-success').text('Success');
            $('#overall_status_' + chapter.id).removeClass('status-progress').addClass('status-success').text('Completed');

            // Calculate total time for this chapter
            const totalTime = chapterTimings[chapter.id].vectors +
                            chapterTimings[chapter.id].metadata +
                            chapterTimings[chapter.id].exercises +
                            chapterTimings[chapter.id].questionbank;
            $('#overall_timing_' + chapter.id).text(formatTime(totalTime));
        } else {
            $('#questionbank_status_' + chapter.id).removeClass('status-progress').addClass('status-error').text('Failed');
            $('#overall_status_' + chapter.id).removeClass('status-progress').addClass('status-error').text('Failed');
        }

        // Move to next chapter
        currentProcessingIndex++;
        updateOverallProgress();
        processNextChapter();
    }

    function questionBankFailed(chapter, startTime) {
        const endTime = Date.now();
        const elapsed = endTime - startTime;
        $('#questionbank_timing_' + chapter.id).text(formatTime(elapsed));
        $('#questionbank_status_' + chapter.id).removeClass('status-progress').addClass('status-error').text('Error');
        $('#overall_status_' + chapter.id).removeClass('status-progress').addClass('status-error').text('Failed');

        // Move to next chapter
        currentProcessingIndex++;
        updateOverallProgress();
        processNextChapter();
    }

    // Update overall timing every second during processing
    setInterval(function() {
        if (overallStartTime && currentProcessingIndex < processingQueue.length) {
            const elapsed = Date.now() - overallStartTime;
            $('#overallTiming').text('Total Time: ' + formatTime(elapsed));
        }
    }, 1000);

</script>

Total lines in file: 758
