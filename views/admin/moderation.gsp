<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<link href="https://cdn.datatables.net/1.10.20/css/dataTables.bootstrap4.min.css" type="text/css" rel="stylesheet">

<style>
.dataTables_paginate ul.pagination .page-link {
    font-size: 15px;
}
.dataTables_paginate ul.pagination .page-link:not(:disabled):not(.disabled) {
    color: #212529;
}
.dataTables_paginate ul.pagination .page-link:focus {
    box-shadow: 0 0 0 0.1rem rgba(0,123,255,.25);
}
.dataTables_paginate ul.pagination .page-item.active .page-link {
    background-color: #007bff !important;
}
#jwVideoModalData p {
    font-size: 15px;
}
#jwVideoModalData p strong {
    font-weight: 500;
}
p{
    margin-bottom: 0!important;

}
</style>

<script>
    var loggedIn=false;
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<!--<div>-->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid my-5 px-5" style="min-height: calc(100vh - 160px);">
    <div class='row m-0 px-5'>
        <div class='col-md-12 main p-4' style=" margin: 40px auto; float: none;">
            <div id="content-books">
                <table id="videoData" class='table table-hover table-bordered w-100' style="display: none;">
                    <thead>
                    <tr class="bg-primary text-white">
                        <th>Teacher Image </th>
                        <th>Details</th>
                        <th width="20%" style="text-align: center">Action</th>
                    </tr>
                    </thead>
                </table>
            </div>
        </div>
    </div>
</div>

<div class="modal fade jw-video-modal" id="jwVideoModal" data-keyboard="false" data-backdrop="static" >
    <div class="modal-dialog">
        <div class="modal-content">
            <!-- Modal body -->
            <div class="modal-body">
                <div id="jwVideoModalData" class="px-3 pt-3">

                </div>
                <div class="text-center p-3">
                    <button type="button" class="btn btn-primary col-md-3" data-dismiss="modal">Okay</button>
                </div>
            </div>

        </div>
    </div>
</div>


<g:render template="/${session['entryController']}/footer_new"></g:render>
<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="jquery.simple-dtpicker.js"/>
<script src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.20/js/dataTables.bootstrap4.min.js"></script>
<script>

    function getData(){
        $('.loading-icon').removeClass('hidden');
        if ($.fn.dataTable.isDataTable('#videoData')) {
            $('#videoData').DataTable().destroy();
        }
        $('#videoData').show();
        $('#videoData').DataTable({
            // "processing": true,
            // "serverSide": true,
            "bRetrieve": true,
            "searching": true,
            "ordering":  false,
            'ajax': {
                'url': '/admin/moderation',
                'type': 'GET',
                'data': function (outData) {
                    outData.showData = "yes"
                    return outData;
                },
                dataFilter: function (inData) {
                    return inData;
                },
                error: function (err, status) {
                    console.log(err);
                },
            },
            'columns': [
                {
                    'data': 'id',
                    'render': function (data, type, row) {
                        if(row.teacherImage!=null) {
                            return "<img style='width:130px;height: 130px;object-fit: contain' src=\"/usermanagement/showTeachersImage?fileName=" + row.teacherImage + "&id=" + row.id + "\">";
                        }else{
                            return "<p>No image found</p>";
                        }
                    }
                },
                {
                    'data': 'userName',
                    'render':function (data, type, row) {
                        var dateObj = new Date(row.dateCreated).toISOString().split("T")[0];
                        var [year, month, day] = dateObj .split('-');
                        var dateFormatted = [day, month, year].join('-');
                        return "<h4>Nominated By : "+row.userName+"</h4>"+
                            "<p>Date Added : "+dateFormatted+"</p>\n"+
                            "<p>Teacher Name : "+row.teacherName+"</p>\n"+
                            "<p>Teacher School : "+row.teacherSchool+"</p>\n"+
                            "<p>Teacher Class : "+row.teacherClass+"</p>\n"+
                            "<p>Teacher Subject : "+row.teacherSubject+"</p>\n"+
                            "<p>Description : "+row.description+"</p>"+
                            "<p>Vote Count : "+row.voteCount+"</p>";

                    }
                },
                {
                    'data': 'id',
                    'render': function (data, type, row) {
                            return "<input type=\"button\" class='btn btn-sm btn-success'  onclick='moderatePost(\"" + row.id + "\",\"moderate\");' value='Moderate'><td>   </td>"+
                                "<input type=\"button\"  class='btn btn-sm btn-danger' onclick='moderatePost(\"" + row.id + "\",\"delete\");' value='Delete'>";
                    }
                },
            ],
        })

        $('.loading-icon').addClass('hidden');
    }

    function moderatePost(id,mode) {
        <g:remoteFunction controller="admin" action="moderatePostById"  params="'postId='+id+'&mode='+mode"/>
        alert("updated successfully");
         location.reload();
    }



    window.onload=getData();


</script>


</body>
</html>
