<style type="text/css">

.shine {
    background: #d4d7d9;
    background-image: linear-gradient(to right, #d4d7d9 0%, #e4e9ed 20%, #d4d7d9 40%, #d4d7d9 100%);
    background-repeat: no-repeat;
    background-size: 800px 104px;
    display: inline-block;
    position: relative;

    -webkit-animation-duration: 1s;
    -webkit-animation-fill-mode: forwards;
    -webkit-animation-iteration-count: infinite;
    -webkit-animation-name: placeholderShimmer;
    -webkit-animation-timing-function: linear;
}

box {
    height: 104px;
    width: 100%;
}

div.line-wrapper {
    display: inline-flex;
    flex-direction: column;
    margin-left: 25px;
    margin-top: 15px;
    vertical-align: top;
}

lines {
    height: 10px;
    margin-top: 10px;
    width: 100%;
}

photo {
    display: block!important;
    width: 325px;
    height: 30px;
    margin-top: 15px;
}
.mt-20{
    margin-top: 10px;
}

@-webkit-keyframes placeholderShimmer {
    0% {
        background-position: -468px 0;
    }

    100% {
        background-position: 468px 0;
    }
}
.flexAlign{
    display: flex;
    justify-content: center;
}

</style>
<%
    boolean eUtkarshSite = "eutkarsh".equals(session['entryController'])
%>
<div class="tab-content">
    <% if(!"evidya".equals(session["entryController"])){%>
    <div role="tabpanel" class="tab-pane fade in show active" id="all">
        <div class="container">

            <% if(!("sage".equals(session["entryController"])||"evidya".equals(session["entryController"]))){%>
            <div id="allAddButton" class="d-flex justify-content-end">

                <div class="dropdown">
                    <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
                        <i class="material-icons">
                            add
                        </i>  <span>Add</span>
                    </button>
                    <sec:ifLoggedIn>
                        <div class="dropdown-menu">
                            <a class="dropdown-item" href="javascript:openVideos();"><span>Add new videos111 (Direct)</span></a>
                            <a class="dropdown-item" href="javascript:searchRelatedVideos();"><span>Add related videos (Recommended)</span></a>
                            <a class="dropdown-item" href="javascript:openSolution();"><span>Add related Ref.link (Recommended)</span></a>
                            <a class="dropdown-item" href="javascript:createNewStudySet();"><span>Add revision</span></a>
                        </div>
                    </sec:ifLoggedIn>
                    <sec:ifNotLoggedIn>
                        <div class="dropdown-menu">
                            <a class="dropdown-item" href="javascript:loginOpen()"><span>Add new videos (Direct)</span></a>
                            <a class="dropdown-item" href="javascript:loginOpen()"><span>Add related videos (Recommended)</span></a>
                            <a class="dropdown-item" href="javascript:loginOpen()"><span>Add related Ref.link (Recommended)</span></a>
                            <a class="dropdown-item" href="javascript:loginOpen()"><span>Add revision</span></a>
                        </div>
                    </sec:ifNotLoggedIn>
                </div>

            </div>
            <%}%>
            <div id="content-data-all" class="col-md-12 col-xs-12 col-sm-12"></div>
        </div>

    </div>
    <%}%>
    <div role="tabpanel" class="tab-pane fade in <%= "evidya".equals(session["entryController"])?" show active": ""%>" id="read" oncopy="return false" oncut="return false" onpaste="return false">
        <div id="content-data-readingMaterial" class="row quiz-section"></div>
        <div id="notesLoading" style="display: none">
            <div class="container">
                <div class="content-wrapper mt-20">
                    <div class="content row flexAlign">
                        <photo class="shine col-md-3 col-sm-6 col-xs-12" ></photo>
                    </div>
                </div>
                <br>
                <div class="content-wrapper">
                    <div class="content row flexAlign">

                        <div class="line-wrapper col-md-6 col-sm-12 col-xs-12">
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                        </div>
                    </div>
                </div>
                <br>
                <div class="content-wrapper">
                    <div class="content row flexAlign">

                        <div class="line-wrapper col-md-6 col-sm-12 col-xs-12">
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                        </div>
                    </div>
                </div>

                <br>
                <div class="content-wrapper">
                    <div class="content row flexAlign">
                        <div class="line-wrapper col-md-6 col-sm-12 col-xs-12">
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                        </div>
                    </div>
                </div>
                <br>
                <div class="content-wrapper">
                    <div class="content row flexAlign">
                        <div class="line-wrapper col-md-6 col-sm-12 col-xs-12">
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                        </div>
                    </div>
                </div>
                <br>
                <div class="content-wrapper">
                    <div class="content row flexAlign">
                        <div class="line-wrapper col-md-6 col-sm-12 col-xs-12">
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                        </div>
                    </div>
                </div>
                <br>
                <div class="content-wrapper">
                    <div class="content row flexAlign">
                        <div class="line-wrapper col-md-6 col-sm-12 col-xs-12">
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                        </div>
                    </div>
                </div>
                <br>
                <div class="content-wrapper">
                    <div class="content row flexAlign">
                        <div class="line-wrapper col-md-6 col-sm-12 col-xs-12">
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                        </div>
                    </div>
                </div>
                <br>
                <div class="content-wrapper">
                    <div class="content row flexAlign">
                        <div class="line-wrapper col-md-6 col-sm-12 col-xs-12">
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                        </div>
                    </div>
                </div>
                <br>
                <div class="content-wrapper">
                    <div class="content row flexAlign">
                        <div class="line-wrapper col-md-6 col-sm-12 col-xs-12">
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="htmlreadingcontent"></div>
        <div class="col-md-10" id="notesnavigation"></div>

    </div>
    <div role="tabpanel" class="tab-pane fade" id="userNotes">
        <div id="notesLoadingSecond" style="display: none">
            <div class="container">
                <div class="content-wrapper mt-20">
                    <div class="content row flexAlign">
                        <photo class="shine col-md-3 col-sm-6 col-xs-12" ></photo>
                    </div>
                </div>
                <br>
                <div class="content-wrapper">
                    <div class="content row flexAlign">

                        <div class="line-wrapper col-md-6 col-sm-12 col-xs-12">
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                        </div>
                    </div>
                </div>
                <br>
                <div class="content-wrapper">
                    <div class="content row flexAlign">

                        <div class="line-wrapper col-md-6 col-sm-12 col-xs-12">
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                        </div>
                    </div>
                </div>

                <br>
                <div class="content-wrapper">
                    <div class="content row flexAlign">
                        <div class="line-wrapper col-md-6 col-sm-12 col-xs-12">
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                        </div>
                    </div>
                </div>
                <br>
                <div class="content-wrapper">
                    <div class="content row flexAlign">
                        <div class="line-wrapper col-md-6 col-sm-12 col-xs-12">
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                        </div>
                    </div>
                </div>
                <br>
                <div class="content-wrapper">
                    <div class="content row flexAlign">
                        <div class="line-wrapper col-md-6 col-sm-12 col-xs-12">
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                        </div>
                    </div>
                </div>
                <br>
                <div class="content-wrapper">
                    <div class="content row flexAlign">
                        <div class="line-wrapper col-md-6 col-sm-12 col-xs-12">
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                        </div>
                    </div>
                </div>
                <br>
                <div class="content-wrapper">
                    <div class="content row flexAlign">
                        <div class="line-wrapper col-md-6 col-sm-12 col-xs-12">
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                        </div>
                    </div>
                </div>
                <br>
                <div class="content-wrapper">
                    <div class="content row flexAlign">
                        <div class="line-wrapper col-md-6 col-sm-12 col-xs-12">
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                        </div>
                    </div>
                </div>
                <br>
                <div class="content-wrapper">
                    <div class="content row flexAlign">
                        <div class="line-wrapper col-md-6 col-sm-12 col-xs-12">
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                            <lines class="shine"></lines>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="addNotes"  style="display:none">
            <g:render template="/wonderpublish/addnotes"></g:render>
        </div>
        <div id="content-data-no-notes" style="display: flex;">
            <div class="container">
                <sec:ifNotLoggedIn>
                    <div class="row justify-content-center">
                        <div>
                            <a href='javascript:loginOpen();' class='btn btn-block btn-theme'>Add your notes here</a>
                            <a id="upload-notesPr" href='javascript:loginOpen();' class='btn btn-block btn-secondaries'>Upload Notes </a>
                        </div>
                    </div>
                </sec:ifNotLoggedIn>
                <sec:ifLoggedIn>
                    <div class="row justify-content-center">
                        <div>
                            <a href='javascript:showNotesEditor();' class='btn btn-block btn-theme'>Add your notes here</a>
                            <%if(instructor){%>
                            <a id="upload-notesPr" class='btn btn-block btn-secondaries' data-toggle="modal" data-target="#addFileNotes">Upload Notes</a>
                            <%}%>
                        </div>
                    </div>
                </sec:ifLoggedIn>
            %{--<div class="row">--}%
            %{--<div class="col-md-8 col-md-offset-2 text-center">--}%
            %{--Add your notes for this chapter here.--}%
            %{--</div>--}%
            %{--</div>--}%
            </div>
        </div>
        <div id="displayNotes"></div>
        <div id="content-data-userNotes" class="row quiz-section "></div>

    </div>
    <div role="tabpanel" class="tab-pane fade" id="lonwg-qa">
        <div id="content-data-QA" class="col-md-12 col-xs-12 col-sm-12"></div>
    </div>
    <div role="tabpanel" class="tab-pane fade" id="qa">
        <div id="content-data-shortQAndA"></div>
    </div>
    <div role="tabpanel" class="tab-pane fade" id="learn">
        <div id="content-data-quiz" class="row quiz-section"></div>
    </div>

    <div role="tabpanel" class="tab-pane fade" id="flash-cards">

        <div id="flash-card-carousel" class="carousel slide flash-card-slider" data-interval="false">
            <ol class="carousel-indicators">
                <li data-target="#flash-card-carousel" data-slide-to="0" class="active"></li>
            </ol>
            <div class="carousel-inner row quiz-section" id="content-data-flashCards" role="listbox"></div>

            <div class="carousel-control-wrapper col-md-12">
                <a class="left carousel-control" id="carousel-btn-left" href="#flash-card-carousel" role="button" data-slide="prev">
                    <i class="material-icons">chevron_left</i>
                </a>
                <p class="card-number" id="card-number"></p>
                <a class="right carousel-control" id="carousel-btn-right" href="#flash-card-carousel" role="button" data-slide="next">
                    <i class="material-icons">chevron_right</i>
                </a>
            </div>
        </div>
    </div>
    <div role="tabpanel" class="tab-pane fade" id="create-test">
        <div class="quiz-section row">
            <div class='quiz-item-wrapper'>
                <div class='quiz-item'>
                    <p class='quiz-name'>This Chapter</p>
                </div>
                <div class='quiz-buttons'>
                    <a href="javascript:startTestGenerator('singlechapter');" class='btn btn-block quiz-learn-btn waves-effect waves-ripple'>Start</a>
                </div>
            </div>
            <div class='quiz-item-wrapper'>
                <div class='quiz-item'>
                    <p class='quiz-name'>Multiple Chapters</p>
                </div>
                <div class='quiz-buttons'>
                    <a href="javascript:startTestGenerator('singlebook');" class='btn btn-block quiz-learn-btn waves-effect waves-ripple'>Start</a>
                </div>
            </div>
            <div class='quiz-item-wrapper'>
                <div class='quiz-item'>
                    <p class='quiz-name'>Multiple eBooks</p>
                </div>
                <div class='quiz-buttons'>
                    <a href="javascript:startTestGenerator('multiplebooks');" class='btn btn-block quiz-learn-btn waves-effect waves-ripple'>Start</a>
                </div>
            </div>
        </div>
    </div>
    <div role="tabpanel" class="tab-pane fade" id="discuss">
        <g:render template="/wonderpublish/discforum"></g:render>
    </div>
    <div role="tabpanel" class="tab-pane fade" id="videos">
        <div id="withnovideos" style="display: none">
            <div class="container">
                <sec:ifNotLoggedIn>
                    <div class="row justify-content-center">
                        <div class="text-center">
                            <a href='javascript:loginOpen()' class='btn btn-block btn-theme'>Add Related Videos</a>
                            <a href='javascript:loginOpen()' class='btn btn-block btn-secondaries'>See Related Videos</a>
                        </div>
                    </div>
                </sec:ifNotLoggedIn>
                <sec:ifLoggedIn>
                    <div class="row justify-content-center" id="videoInAppOnly" style="display: none">
                        <div class=""><b>Videos for this chapter is available in app only</b></div>
                    </div>
                    <div class="row justify-content-center">
                        <div class="text-center">
                            <a href='javascript:createNewVideo();' class='btn btn-block btn-theme' data-toggle="modal" data-target="#addVideo">
                                Add Related Videos
                            </a>
                            <a href='javascript:searchRelatedVideos();' class='btn btn-block btn-secondaries'>
                                See Related Videos
                            </a>
                        </div>
                    </div>
                </sec:ifLoggedIn>
                <br>
                <div class="row justify-content-center">
                    <div class="refrenceText text-center">Add your videos here or See related videos here </div>
                </div>
            </div>
        </div>
        <div id="videoAddButton" class="container"  style="display: none">

            <div class="d-flex justify-content-end">

                <div class="dropdown">
                    <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
                        <i class="material-icons">
                            add
                        </i>  <span>Add a Video</span>
                    </button>
                    <sec:ifLoggedIn>
                        <div class="dropdown-menu">
                            <a class="dropdown-item" href="javascript:openVideos();"><span>Add new videos</span><i class="material-icons">
                                search
                            </i></a>
                            <a class="dropdown-item" href="javascript:searchRelatedVideos();"><span>Add related videos</span><i class="material-icons">
                                search
                            </i></a>
                            <a class="dropdown-item" href="javascript:searchRelatedVideos();"><span>Search Related videos</span><i class="material-icons">
                                search
                            </i></a>

                        </div>
                    </sec:ifLoggedIn>
                    <sec:ifNotLoggedIn>
                        <div class="dropdown-menu">
                            <a class="dropdown-item" href="javascript:loginOpen();"><span>Add new videos</span><i class="material-icons">
                                search
                            </i></a>
                            <a class="dropdown-item" href="javascript:loginOpen();"><span>Add related videos</span><i class="material-icons">
                                search
                            </i></a>
                            <a class="dropdown-item" href="javascript:loginOpen();"><span>Search Related videos</span><i class="material-icons">
                                search
                            </i></a>

                        </div>
                    </sec:ifNotLoggedIn>
                </div>
            </div>
        </div>

        <div id="searchVideos" style="display: none"></div>
        <g:render template="/wonderpublish/videoPlay"></g:render>
    </div>
    <div role="tabpanel" class="tab-pane fade" id="additional">

        <div id="withnoweblinks"  style="display: none">
            <div class="container">
                <sec:ifNotLoggedIn>
                    <div class="row justify-content-center">
                        <div class="text-center">

                            <% if("eutkarsh".equals(session["entryController"])){%>
                            <a href='#' class='btn btn-block btn-theme' data-toggle="modal" data-target="#addWeburl">
                                Add WebLinks
                            </a>
                            <%}else{%>
                            <a href='#' class='btn btn-block btn-theme' data-toggle="modal" data-target="#addWeburl">
                                Add solutions link
                            </a>
                            <%}%>
                            <% if("eutkarsh".equals(session["entryController"])){%>
                            <a href="javascript:googleSearch('solutions')" class='btn btn-block btn-secondaries'>
                                Search WebLinks for this chapter
                            </a>
                            <%}else{%>
                            <a href="javascript:googleSearch('solutions')" class='btn btn-block btn-secondaries'>
                                Search solutions for this chapter
                            </a>
                            <%}%>
                            <a href='javascript:loginOpen()' class='btn btn-block btn-secondaries'>Search worksheets for this chapter</a>

                            <a href='javascript:loginOpen()' class='btn btn-block btn-secondaries'>Search previous year question papers</a>
                        </div>
                    </div>
                </sec:ifNotLoggedIn>
                <sec:ifLoggedIn>
                    <div class="row justify-content-center">
                        <div class="text-center">
                            <% if("eutkarsh".equals(session["entryController"])){%>
                            <a href='#' class='btn btn-block btn-theme' data-toggle="modal" data-target="#addWeburl">
                                Add WebLinks
                            </a>
                            <%}else{%>
                            <a href='#' class='btn btn-block btn-theme' data-toggle="modal" data-target="#addWeburl">
                                Add solutions link
                            </a>
                            <%}%>
                            <% if("eutkarsh".equals(session["entryController"])){%>
                            <a href="javascript:googleSearch('solutions')" class='btn btn-block btn-secondaries'>
                                Search WebLinks for this chapter
                            </a>
                            <%}else{%>
                            <a href="javascript:googleSearch('solutions')" class='btn btn-block btn-secondaries'>
                                Search solutions for this chapter
                            </a>
                            <%}%>



                            <a href="javascript:googleSearch('worksheets')" class='btn btn-block btn-secondaries'>
                                Search worksheets for this chapter
                            </a>

                            <a href="javascript:googleSearch('previous year question papers')" class='btn btn-block btn-secondaries'>
                                Search previous year question papers
                            </a>
                        </div>
                    </div>
                </sec:ifLoggedIn>
                <div class="row justify-content-center">
                    <p class="refrenceText">Add your Reference here or Search related Reference here </p>
                </div>
            </div>
        </div>
        <div id="addRefButton" class="d-flex justify-content-end" style="display: none">
            <div class="dropdown">
                <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
                    <i class="material-icons">
                        add
                    </i>
                    %{--          <span>Solutions link</span>--}%
                    <% if(eUtkarshSite){%>
                    <span>WebLinks</span>
                    <%}else{%>
                    <span>Solutions link</span>
                    <%}%>
                </button>
            <sec:ifLoggedIn>
                <div class="dropdown-menu">
                    <a class="dropdown-item" href="javascript:openSolution();"><span>Add Ref. link</span><i class="material-icons">
                        search
                    </i></a>
                    <a class="dropdown-item" href="javascript:googleSearch('solutions')"><span>Search Related refs.</span><i class="material-icons">
                        search
                    </i></a>
                    <a class="dropdown-item" href="javascript:googleSearch('worksheets')"><span>Search Related worksheets.</span><i class="material-icons">
                        search
                    </i></a>
                    <a class="dropdown-item" href="javascript:googleSearch('previous year question papers')"><span>Search Previous year questions.</span><i class="material-icons">
                        search
                    </i></a>
                </div>
            </sec:ifLoggedIn>

            <sec:ifNotLoggedIn>
                <div class="dropdown-menu">
                        <a class="dropdown-item" href="javascript:loginOpen();"><span>Add Ref. link</span><i class="material-icons">
                        search
                    </i></a>
                    <a class="dropdown-item" href="javascript:loginOpen();"><span>Search Related refs.</span><i class="material-icons">
                        search
                    </i></a>
                    <a class="dropdown-item" href="javascript:loginOpen();"><span>Search Related worksheets.</span><i class="material-icons">
                        search
                    </i></a>
                    <a class="dropdown-item" href="javascript:loginOpen();"><span>Search Previous year questions.</span><i class="material-icons">
                        search
                    </i></a>
                </div>
            </sec:ifNotLoggedIn>

            </div>
        </div>
        %{--<div id="content-data-url">--}%

        %{--</div>--}%
        <g:render template="/wonderpublish/additionalReferences"></g:render>
        <div id="qainwebref" style="display: none">

            %{--<a href="javascript:backVideos()" class="pr-back-btn"><i class="fas fa-angle-left"></i>Back</a>--}%
        </div>
    </div>
    <div role="tabpanel" class="tab-pane fade" id="studySets">
        <div id="content-data-studyset-nosets">
            <div class="container">
                <sec:ifNotLoggedIn>
                    <div class="row justify-content-center">
                        <div class="text-center"><a href='javascript:loginOpen()' class='btn btn-block btn-theme'>Add first revision set</a></div>
                    </div>
                </sec:ifNotLoggedIn>
                <sec:ifLoggedIn>
                    <div class="row justify-content-center">
                        <div class="text-center"><a href='javascript:createNewStudySet()' class='btn btn-block btn-theme'>Add first revision set</a></div>
                    </div>
                </sec:ifLoggedIn>
                <div class="row">
                    <div class="text-center">Revision sets are the best way to learn and revise. Just add the <b>term</b> you want to learn and its <b>definition</b>. Wonderslate will automatically convert them to flash
                    cards, Multiple choice questions, True or False quizzes. .  </div>
                </div>
            </div>
        </div>
        <div id="content-data-studyset" class="row quiz-section">

        </div>
        <g:render template="/wonderpublish/wonderGoaStudySet"></g:render>
    </div>
    <%if("1".equals(""+session["siteId"])){%>
    <div class="sharethis-inline-share-buttons" id="sharethisid"></div>
    <%}%>
</div>
<div class="upload-url">
    <div class="modal fade" id="addFileNotes" role="dialog">
        <div class="modal-dialog modal-sm modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">Upload Notes</h4>
                </div>
                <div class="modal-body">
                    <div class="row text-center">
                        <g:uploadForm name="resource3Form" url="[action:'addFile',controller:'resourceCreator']"  method="post">

                            <g:textField id="resourceName" name="resourceName"  placeholder="Name" />
                            <input id="file3" type="file"  name="file"  accept=".pdf,.epub , .jpg , .jpeg, .png, .zip" />

                            <div class="alert-thin alert alert-warning col-sm-12 text-left red" style="display: none;" id="notesUploadAlert">
                                ** Enter a name for this reading material.
                            </div>
                            <input type="hidden" name="resourceType" value="Notes">
                            <input type="hidden" name="useType" value="notes">
                            <input type="hidden" name="chapterId">
                            <input type="hidden" name="bookId">
                            <input type="hidden" name="quizMode" value="file">
                            <input type="hidden" name="from" value="book">
                        </g:uploadForm>
                    </div>
                </div>
                <div class="alert-thin alert alert-warning col-sm-12 text-left red" style="display: none;" id="userWeblinkUploadAlert">
                    ** Enter Name for the notes
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">CANCEL</button>
                    <button type="button" class="btn " onclick="javascript:uploadNotes();">ADD</button>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="https://code.jquery.com/jquery-2.2.4.js"></script>
<script>
    $(document).ready(function(){
        if(instructor===true) {
            $('#upload-notesPr').show();
        }
    });
    // var shareThis = document.getElementById("sharethisid");
    // shareThis.setAttribute("data-url","https://wonderslate.page.link/?apn=com.wonderslate.wonderpublish&ibi=com.wonderslate.app&link="+encodeURIComponent(window.location.href ));

    function openSolution() {
        alert("testing");
        if(allTabMode){
            $('#chapter-details-tabs a[href="#additional"]').tab('show');
        }
        $('#addWeburl').modal('show');
    }
    function openVideos() {
        <sec:ifNotLoggedIn>
        loginOpen();
        </sec:ifNotLoggedIn>
        <sec:ifLoggedIn>
        if(allTabMode){
            $('#chapter-details-tabs a[href="#videos"]').tab('show');
        }
        $('#addVideo').modal('show');
        </sec:ifLoggedIn>
    }
</script>
